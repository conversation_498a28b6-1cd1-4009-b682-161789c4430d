module bid-gateway

go 1.20

require (
	github.com/fsnotify/fsnotify v1.7.0
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.19.1
	github.com/pkg/errors v0.9.1
	github.com/spf13/viper v1.18.2
	// github.com/tensorflow/tensorflow v2.15.0+incompatible // indirect
	github.com/urfave/cli v1.22.14
	google.golang.org/genproto/googleapis/api v0.0.0-20240205150955-31a09d347014
	google.golang.org/grpc v1.61.1
	google.golang.org/protobuf v1.32.0
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

require (
	//github.com/howge/tfs28/apis v0.0.0-20240227082946-0770e0608514
	github.com/lestrrat-go/file-rotatelogs v2.4.0+incompatible
	github.com/sirupsen/logrus v1.9.3
	golang.org/x/net v0.21.0
	gopkg.in/yaml.v2 v2.4.0
)

require (
	github.com/cpuguy83/go-md2man/v2 v2.0.3 // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	//github.com/howge/tfs28/config v0.0.0-00010101000000-000000000000 // indirect
	github.com/jonboulle/clockwork v0.4.0 // indirect
	github.com/lestrrat-go/strftime v1.0.6 // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/pelletier/go-toml/v2 v2.1.1 // indirect
	github.com/russross/blackfriday/v2 v2.1.0 // indirect
	github.com/sagikazarmark/locafero v0.4.0 // indirect
	github.com/sagikazarmark/slog-shim v0.1.0 // indirect
	github.com/sourcegraph/conc v0.3.0 // indirect
	github.com/spf13/afero v1.11.0 // indirect
	github.com/spf13/cast v1.6.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	github.com/tensorflow/tensorflow/tensorflow/go/core/example/example_protos_go_proto v0.0.0-00010101000000-000000000000
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/allocation_description_go_proto v0.0.0-00010101000000-000000000000 // indirect
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/attr_value_go_proto v0.0.0-00010101000000-000000000000
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/cost_graph_go_proto v0.0.0-00010101000000-000000000000 // indirect
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/device_attributes_go_proto v0.0.0-00010101000000-000000000000 // indirect
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/full_type_go_proto v0.0.0-00010101000000-000000000000 // indirect
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/function_go_proto v0.0.0-00010101000000-000000000000 // indirect
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/graph_go_proto v0.0.0-00010101000000-000000000000 // indirect
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/model_go_proto v0.0.0-00010101000000-000000000000
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/node_def_go_proto v0.0.0-00010101000000-000000000000 // indirect
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/op_def_go_proto v0.0.0-00010101000000-000000000000 // indirect
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/resource_handle_go_proto v0.0.0-00010101000000-000000000000 // indirect
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/step_stats_go_proto v0.0.0-00010101000000-000000000000 // indirect
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_description_go_proto v0.0.0-00010101000000-000000000000
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto v0.0.0-00010101000000-000000000000
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto v0.0.0-00010101000000-000000000000
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_slice_go_proto v0.0.0-00010101000000-000000000000 // indirect
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto v0.0.0-00010101000000-000000000000
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/variable_go_proto v0.0.0-00010101000000-000000000000 // indirect
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/versions_go_proto v0.0.0-00010101000000-000000000000 // indirect
	github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto v0.0.0-00010101000000-000000000000
	github.com/tensorflow/tensorflow/tensorflow/go/stream_executor v0.0.0-00010101000000-000000000000 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/exp v0.0.0-20240222234643-814bf88cf225 // indirect
	golang.org/x/sys v0.17.0 // indirect
	//golang.org/x/text v0.14.0 // indirect
	google.golang.org/genproto v0.0.0-20240213162025-012b6fc9bca9 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240221002015-b0ce06bbee7c // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
)

require golang.org/x/text v0.14.0 // indirect

// replace (
// 	// github.com/tensorflow/tensorflow => ./pkg/tensorflow
// 	github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto => ./pkg/tensorflow/core/protobuf
// 	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto => ./pkg/tensorflow/core/framework/
// )

replace (
	github.com/tensorflow/tensorflow/tensorflow/go/core/example/example_protos_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/example/example_protos_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/allocation_description_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/allocation_description_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/api_def_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/api_def_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/attr_value_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/attr_value_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/cost_graph_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/cost_graph_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/device_attributes_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/device_attributes_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/full_type_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/full_type_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/function_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/function_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/graph_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/graph_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/model_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/model_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/node_def_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/node_def_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/op_def_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/op_def_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/resource_handle_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/resource_handle_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/step_stats_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/step_stats_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_description_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_description_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_slice_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_slice_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/variable_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/variable_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/framework/versions_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/versions_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto
	github.com/tensorflow/tensorflow/tensorflow/go/stream_executor => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/stream_executor
)
