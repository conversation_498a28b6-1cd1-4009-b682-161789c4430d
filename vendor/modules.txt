# github.com/cpuguy83/go-md2man/v2 v2.0.3
## explicit; go 1.11
github.com/cpuguy83/go-md2man/v2/md2man
# github.com/fsnotify/fsnotify v1.7.0
## explicit; go 1.17
github.com/fsnotify/fsnotify
# github.com/golang/protobuf v1.5.3
## explicit; go 1.9
github.com/golang/protobuf/jsonpb
github.com/golang/protobuf/proto
github.com/golang/protobuf/ptypes
github.com/golang/protobuf/ptypes/any
github.com/golang/protobuf/ptypes/duration
github.com/golang/protobuf/ptypes/timestamp
# github.com/grpc-ecosystem/grpc-gateway/v2 v2.19.1
## explicit; go 1.19
github.com/grpc-ecosystem/grpc-gateway/v2/internal/httprule
github.com/grpc-ecosystem/grpc-gateway/v2/runtime
github.com/grpc-ecosystem/grpc-gateway/v2/utilities
# github.com/hashicorp/hcl v1.0.0
## explicit
github.com/hashicorp/hcl
github.com/hashicorp/hcl/hcl/ast
github.com/hashicorp/hcl/hcl/parser
github.com/hashicorp/hcl/hcl/printer
github.com/hashicorp/hcl/hcl/scanner
github.com/hashicorp/hcl/hcl/strconv
github.com/hashicorp/hcl/hcl/token
github.com/hashicorp/hcl/json/parser
github.com/hashicorp/hcl/json/scanner
github.com/hashicorp/hcl/json/token
# github.com/jonboulle/clockwork v0.4.0
## explicit; go 1.15
# github.com/lestrrat-go/file-rotatelogs v2.4.0+incompatible
## explicit
github.com/lestrrat-go/file-rotatelogs
github.com/lestrrat-go/file-rotatelogs/internal/option
# github.com/lestrrat-go/strftime v1.0.6
## explicit; go 1.13
github.com/lestrrat-go/strftime
github.com/lestrrat-go/strftime/internal/errors
# github.com/magiconair/properties v1.8.7
## explicit; go 1.19
github.com/magiconair/properties
# github.com/mitchellh/mapstructure v1.5.0
## explicit; go 1.14
github.com/mitchellh/mapstructure
# github.com/pelletier/go-toml/v2 v2.1.1
## explicit; go 1.16
github.com/pelletier/go-toml/v2
github.com/pelletier/go-toml/v2/internal/characters
github.com/pelletier/go-toml/v2/internal/danger
github.com/pelletier/go-toml/v2/internal/tracker
github.com/pelletier/go-toml/v2/unstable
# github.com/pkg/errors v0.9.1
## explicit
github.com/pkg/errors
# github.com/russross/blackfriday/v2 v2.1.0
## explicit
github.com/russross/blackfriday/v2
# github.com/sagikazarmark/locafero v0.4.0
## explicit; go 1.20
github.com/sagikazarmark/locafero
# github.com/sagikazarmark/slog-shim v0.1.0
## explicit; go 1.20
github.com/sagikazarmark/slog-shim
# github.com/sirupsen/logrus v1.9.3
## explicit; go 1.13
github.com/sirupsen/logrus
# github.com/sourcegraph/conc v0.3.0
## explicit; go 1.19
github.com/sourcegraph/conc
github.com/sourcegraph/conc/internal/multierror
github.com/sourcegraph/conc/iter
github.com/sourcegraph/conc/panics
# github.com/spf13/afero v1.11.0
## explicit; go 1.19
github.com/spf13/afero
github.com/spf13/afero/internal/common
github.com/spf13/afero/mem
# github.com/spf13/cast v1.6.0
## explicit; go 1.19
github.com/spf13/cast
# github.com/spf13/pflag v1.0.5
## explicit; go 1.12
github.com/spf13/pflag
# github.com/spf13/viper v1.18.2
## explicit; go 1.18
github.com/spf13/viper
github.com/spf13/viper/internal/encoding
github.com/spf13/viper/internal/encoding/dotenv
github.com/spf13/viper/internal/encoding/hcl
github.com/spf13/viper/internal/encoding/ini
github.com/spf13/viper/internal/encoding/javaproperties
github.com/spf13/viper/internal/encoding/json
github.com/spf13/viper/internal/encoding/toml
github.com/spf13/viper/internal/encoding/yaml
github.com/spf13/viper/internal/features
# github.com/subosito/gotenv v1.6.0
## explicit; go 1.18
github.com/subosito/gotenv
# github.com/tensorflow/tensorflow/tensorflow/go/core/example/example_protos_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/example/example_protos_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/example/example_protos_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/allocation_description_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/allocation_description_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/allocation_description_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/attr_value_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/attr_value_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/attr_value_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/cost_graph_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/cost_graph_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/cost_graph_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/device_attributes_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/device_attributes_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/device_attributes_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/full_type_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/full_type_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/full_type_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/function_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/function_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/function_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/graph_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/graph_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/graph_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/model_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/model_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/model_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/node_def_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/node_def_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/node_def_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/op_def_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/op_def_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/op_def_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/resource_handle_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/resource_handle_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/resource_handle_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/step_stats_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/step_stats_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/step_stats_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_description_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_description_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_description_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_slice_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_slice_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_slice_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/variable_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/variable_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/variable_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/versions_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/versions_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/framework/versions_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/stream_executor v0.0.0-00010101000000-000000000000 => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/stream_executor
## explicit; go 1.20
github.com/tensorflow/tensorflow/tensorflow/go/stream_executor
# github.com/urfave/cli v1.22.14
## explicit; go 1.11
github.com/urfave/cli
# go.uber.org/multierr v1.11.0
## explicit; go 1.19
go.uber.org/multierr
# golang.org/x/exp v0.0.0-20240222234643-814bf88cf225
## explicit; go 1.20
golang.org/x/exp/constraints
golang.org/x/exp/slices
golang.org/x/exp/slog
golang.org/x/exp/slog/internal
golang.org/x/exp/slog/internal/buffer
# golang.org/x/net v0.21.0
## explicit; go 1.18
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/h2c
golang.org/x/net/http2/hpack
golang.org/x/net/idna
golang.org/x/net/internal/timeseries
golang.org/x/net/trace
# golang.org/x/sys v0.17.0
## explicit; go 1.18
golang.org/x/sys/unix
golang.org/x/sys/windows
# golang.org/x/text v0.14.0
## explicit; go 1.18
golang.org/x/text/encoding
golang.org/x/text/encoding/internal
golang.org/x/text/encoding/internal/identifier
golang.org/x/text/encoding/unicode
golang.org/x/text/internal/utf8internal
golang.org/x/text/runes
golang.org/x/text/secure/bidirule
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
# google.golang.org/genproto v0.0.0-20240213162025-012b6fc9bca9
## explicit; go 1.19
google.golang.org/genproto/internal
# google.golang.org/genproto/googleapis/api v0.0.0-20240205150955-31a09d347014
## explicit; go 1.19
google.golang.org/genproto/googleapis/api
google.golang.org/genproto/googleapis/api/annotations
google.golang.org/genproto/googleapis/api/httpbody
# google.golang.org/genproto/googleapis/rpc v0.0.0-20240221002015-b0ce06bbee7c
## explicit; go 1.19
google.golang.org/genproto/googleapis/rpc/status
# google.golang.org/grpc v1.61.1
## explicit; go 1.19
google.golang.org/grpc
google.golang.org/grpc/attributes
google.golang.org/grpc/backoff
google.golang.org/grpc/balancer
google.golang.org/grpc/balancer/base
google.golang.org/grpc/balancer/grpclb/state
google.golang.org/grpc/balancer/roundrobin
google.golang.org/grpc/binarylog/grpc_binarylog_v1
google.golang.org/grpc/channelz
google.golang.org/grpc/codes
google.golang.org/grpc/connectivity
google.golang.org/grpc/credentials
google.golang.org/grpc/credentials/insecure
google.golang.org/grpc/encoding
google.golang.org/grpc/encoding/proto
google.golang.org/grpc/grpclog
google.golang.org/grpc/health/grpc_health_v1
google.golang.org/grpc/internal
google.golang.org/grpc/internal/backoff
google.golang.org/grpc/internal/balancer/gracefulswitch
google.golang.org/grpc/internal/balancerload
google.golang.org/grpc/internal/binarylog
google.golang.org/grpc/internal/buffer
google.golang.org/grpc/internal/channelz
google.golang.org/grpc/internal/credentials
google.golang.org/grpc/internal/envconfig
google.golang.org/grpc/internal/grpclog
google.golang.org/grpc/internal/grpcrand
google.golang.org/grpc/internal/grpcsync
google.golang.org/grpc/internal/grpcutil
google.golang.org/grpc/internal/idle
google.golang.org/grpc/internal/metadata
google.golang.org/grpc/internal/pretty
google.golang.org/grpc/internal/resolver
google.golang.org/grpc/internal/resolver/dns
google.golang.org/grpc/internal/resolver/dns/internal
google.golang.org/grpc/internal/resolver/passthrough
google.golang.org/grpc/internal/resolver/unix
google.golang.org/grpc/internal/serviceconfig
google.golang.org/grpc/internal/status
google.golang.org/grpc/internal/syscall
google.golang.org/grpc/internal/transport
google.golang.org/grpc/internal/transport/networktype
google.golang.org/grpc/keepalive
google.golang.org/grpc/metadata
google.golang.org/grpc/peer
google.golang.org/grpc/resolver
google.golang.org/grpc/resolver/dns
google.golang.org/grpc/serviceconfig
google.golang.org/grpc/stats
google.golang.org/grpc/status
google.golang.org/grpc/tap
# google.golang.org/protobuf v1.32.0
## explicit; go 1.17
google.golang.org/protobuf/encoding/protojson
google.golang.org/protobuf/encoding/prototext
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/descfmt
google.golang.org/protobuf/internal/descopts
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/encoding/defval
google.golang.org/protobuf/internal/encoding/json
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/encoding/tag
google.golang.org/protobuf/internal/encoding/text
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/filedesc
google.golang.org/protobuf/internal/filetype
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/impl
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/set
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/internal/version
google.golang.org/protobuf/proto
google.golang.org/protobuf/reflect/protodesc
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
google.golang.org/protobuf/runtime/protoimpl
google.golang.org/protobuf/types/descriptorpb
google.golang.org/protobuf/types/known/anypb
google.golang.org/protobuf/types/known/durationpb
google.golang.org/protobuf/types/known/fieldmaskpb
google.golang.org/protobuf/types/known/structpb
google.golang.org/protobuf/types/known/timestamppb
google.golang.org/protobuf/types/known/wrapperspb
# gopkg.in/ini.v1 v1.67.0
## explicit
gopkg.in/ini.v1
# gopkg.in/yaml.v2 v2.4.0
## explicit; go 1.15
gopkg.in/yaml.v2
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
# github.com/tensorflow/tensorflow/tensorflow/go/core/example/example_protos_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/example/example_protos_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/allocation_description_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/allocation_description_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/api_def_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/api_def_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/attr_value_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/attr_value_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/cost_graph_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/cost_graph_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/device_attributes_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/device_attributes_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/full_type_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/full_type_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/function_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/function_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/graph_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/graph_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/model_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/model_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/node_def_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/node_def_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/op_def_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/op_def_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/resource_handle_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/resource_handle_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/step_stats_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/step_stats_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_description_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_description_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_slice_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_slice_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/variable_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/variable_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/framework/versions_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/framework/versions_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto
# github.com/tensorflow/tensorflow/tensorflow/go/stream_executor => ./vendorext/github.com/tensorflow/tensorflow/tensorflow/go/stream_executor
