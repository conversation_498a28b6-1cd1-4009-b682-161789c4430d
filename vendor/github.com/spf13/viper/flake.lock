{"nodes": {"devenv": {"inputs": {"flake-compat": "flake-compat", "nix": "nix", "nixpkgs": "nixpkgs", "pre-commit-hooks": "pre-commit-hooks"}, "locked": {"lastModified": 1687972261, "narHash": "sha256-+mxvZfwMVoaZYETmuQWqTi/7T9UKoAE+WpdSQkOVJ2g=", "owner": "cachix", "repo": "devenv", "rev": "e85df562088573305e55906eaa964341f8cb0d9f", "type": "github"}, "original": {"owner": "cachix", "repo": "devenv", "type": "github"}}, "flake-compat": {"flake": false, "locked": {"lastModified": 1673956053, "narHash": "sha256-4gtG9iQuiKITOjNQQeQIpoIB6b16fm+504Ch3sNKLd8=", "owner": "edols<PERSON>", "repo": "flake-compat", "rev": "35bb57c0c8d8b62bbfd284272c928ceb64ddbde9", "type": "github"}, "original": {"owner": "edols<PERSON>", "repo": "flake-compat", "type": "github"}}, "flake-parts": {"inputs": {"nixpkgs-lib": "nixpkgs-lib"}, "locked": {"lastModified": 1687762428, "narHash": "sha256-DIf7mi45PKo+s8dOYF+UlXHzE0Wl/+k3tXUyAoAnoGE=", "owner": "hercules-ci", "repo": "flake-parts", "rev": "37dd7bb15791c86d55c5121740a1887ab55ee836", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "flake-parts", "type": "github"}}, "flake-utils": {"locked": {"lastModified": 1667395993, "narHash": "sha256-nuEHfE/LcWyuSWnS8t12N1wc105Qtau+/OdUAjtQ0rA=", "owner": "numtide", "repo": "flake-utils", "rev": "5aed5285a952e0b949eb3ba02c12fa4fcfef535f", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "gitignore": {"inputs": {"nixpkgs": ["devenv", "pre-commit-hooks", "nixpkgs"]}, "locked": {"lastModified": 1660459072, "narHash": "sha256-8DFJjXG8zqoONA1vXtgeKXy68KdJL5UaXR8NtVMUbx8=", "owner": "hercules-ci", "repo": "gitignore.nix", "rev": "a20de23b925fd8264fd7fad6454652e142fd7f73", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "gitignore.nix", "type": "github"}}, "lowdown-src": {"flake": false, "locked": {"lastModified": 1633514407, "narHash": "sha256-<PERSON>w32tiMjdK9t3ETl5fzGrutQTzh2rufgZV4A/BbxuD4=", "owner": "kristapsdz", "repo": "lowdown", "rev": "d2c2b44ff6c27b936ec27358a2653caaef8f73b8", "type": "github"}, "original": {"owner": "kristapsdz", "repo": "lowdown", "type": "github"}}, "nix": {"inputs": {"lowdown-src": "lowdown-src", "nixpkgs": ["devenv", "nixpkgs"], "nixpkgs-regression": "nixpkgs-regression"}, "locked": {"lastModified": 1676545802, "narHash": "sha256-EK4rZ+Hd5hsvXnzSzk2ikhStJnD63odF7SzsQ8CuSPU=", "owner": "<PERSON><PERSON><PERSON>", "repo": "nix", "rev": "7c91803598ffbcfe4a55c44ac6d49b2cf07a527f", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON>", "ref": "relaxed-flakes", "repo": "nix", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1678875422, "narHash": "sha256-T3o6NcQPwXjxJMn2shz86Chch4ljXgZn746c2caGxd8=", "owner": "NixOS", "repo": "nixpkgs", "rev": "126f49a01de5b7e35a43fd43f891ecf6d3a51459", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixpkgs-unstable", "repo": "nixpkgs", "type": "github"}}, "nixpkgs-lib": {"locked": {"dir": "lib", "lastModified": 1685564631, "narHash": "sha256-8ywr3AkblY4++3lIVxmrWZFzac7+f32ZEhH/A8pNscI=", "owner": "NixOS", "repo": "nixpkgs", "rev": "4f53efe34b3a8877ac923b9350c874e3dcd5dc0a", "type": "github"}, "original": {"dir": "lib", "owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "nixpkgs-regression": {"locked": {"lastModified": 1643052045, "narHash": "sha256-uGJ0VXIhWKGXxkeNnq4TvV3CIOkUJ3PAoLZ3HMzNVMw=", "owner": "NixOS", "repo": "nixpkgs", "rev": "215d4d0fd80ca5163643b03a33fde804a29cc1e2", "type": "github"}, "original": {"owner": "NixOS", "repo": "nixpkgs", "rev": "215d4d0fd80ca5163643b03a33fde804a29cc1e2", "type": "github"}}, "nixpkgs-stable": {"locked": {"lastModified": 1678872516, "narHash": "sha256-/E1YwtMtFAu2KUQKV/1+KFuReYPANM2Rzehk84VxVoc=", "owner": "NixOS", "repo": "nixpkgs", "rev": "9b8e5abb18324c7fe9f07cb100c3cd4a29cda8b8", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-22.11", "repo": "nixpkgs", "type": "github"}}, "nixpkgs_2": {"locked": {"lastModified": 1687886075, "narHash": "sha256-PeayJDDDy+uw1Ats4moZnRdL1OFuZm1Tj+KiHlD67+o=", "owner": "NixOS", "repo": "nixpkgs", "rev": "a565059a348422af5af9026b5174dc5c0dcefdae", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixpkgs-unstable", "repo": "nixpkgs", "type": "github"}}, "pre-commit-hooks": {"inputs": {"flake-compat": ["devenv", "flake-compat"], "flake-utils": "flake-utils", "gitignore": "gitignore", "nixpkgs": ["devenv", "nixpkgs"], "nixpkgs-stable": "nixpkgs-stable"}, "locked": {"lastModified": 1686050334, "narHash": "sha256-R0mczWjDzBpIvM3XXhO908X5e2CQqjyh/gFbwZk/7/Q=", "owner": "cachix", "repo": "pre-commit-hooks.nix", "rev": "6881eb2ae5d8a3516e34714e7a90d9d95914c4dc", "type": "github"}, "original": {"owner": "cachix", "repo": "pre-commit-hooks.nix", "type": "github"}}, "root": {"inputs": {"devenv": "devenv", "flake-parts": "flake-parts", "nixpkgs": "nixpkgs_2"}}}, "root": "root", "version": 7}