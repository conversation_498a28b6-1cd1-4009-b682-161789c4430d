package blackfriday

// Extracted from https://html.spec.whatwg.org/multipage/entities.json
var entities = map[string]bool{
	"&AElig":                            true,
	"&AElig;":                           true,
	"&AMP":                              true,
	"&AMP;":                             true,
	"&Aacute":                           true,
	"&Aacute;":                          true,
	"&Abreve;":                          true,
	"&Acirc":                            true,
	"&Acirc;":                           true,
	"&Acy;":                             true,
	"&Afr;":                             true,
	"&Agrave":                           true,
	"&Agrave;":                          true,
	"&Alpha;":                           true,
	"&Amacr;":                           true,
	"&And;":                             true,
	"&Aogon;":                           true,
	"&Aopf;":                            true,
	"&ApplyFunction;":                   true,
	"&Aring":                            true,
	"&Aring;":                           true,
	"&Ascr;":                            true,
	"&Assign;":                          true,
	"&Atilde":                           true,
	"&Atilde;":                          true,
	"&Auml":                             true,
	"&Auml;":                            true,
	"&Backslash;":                       true,
	"&Barv;":                            true,
	"&Barwed;":                          true,
	"&Bcy;":                             true,
	"&Because;":                         true,
	"&<PERSON><PERSON>llis;":                      true,
	"&Beta;":                            true,
	"&Bfr;":                             true,
	"&Bopf;":                            true,
	"&Breve;":                           true,
	"&Bscr;":                            true,
	"&Bumpeq;":                          true,
	"&CHcy;":                            true,
	"&COPY":                             true,
	"&COPY;":                            true,
	"&Cacute;":                          true,
	"&Cap;":                             true,
	"&CapitalDifferentialD;":            true,
	"&Cayleys;":                         true,
	"&Ccaron;":                          true,
	"&Ccedil":                           true,
	"&Ccedil;":                          true,
	"&Ccirc;":                           true,
	"&Cconint;":                         true,
	"&Cdot;":                            true,
	"&Cedilla;":                         true,
	"&CenterDot;":                       true,
	"&Cfr;":                             true,
	"&Chi;":                             true,
	"&CircleDot;":                       true,
	"&CircleMinus;":                     true,
	"&CirclePlus;":                      true,
	"&CircleTimes;":                     true,
	"&ClockwiseContourIntegral;":        true,
	"&CloseCurlyDoubleQuote;":           true,
	"&CloseCurlyQuote;":                 true,
	"&Colon;":                           true,
	"&Colone;":                          true,
	"&Congruent;":                       true,
	"&Conint;":                          true,
	"&ContourIntegral;":                 true,
	"&Copf;":                            true,
	"&Coproduct;":                       true,
	"&CounterClockwiseContourIntegral;": true,
	"&Cross;":                           true,
	"&Cscr;":                            true,
	"&Cup;":                             true,
	"&CupCap;":                          true,
	"&DD;":                              true,
	"&DDotrahd;":                        true,
	"&DJcy;":                            true,
	"&DScy;":                            true,
	"&DZcy;":                            true,
	"&Dagger;":                          true,
	"&Darr;":                            true,
	"&Dashv;":                           true,
	"&Dcaron;":                          true,
	"&Dcy;":                             true,
	"&Del;":                             true,
	"&Delta;":                           true,
	"&Dfr;":                             true,
	"&DiacriticalAcute;":                true,
	"&DiacriticalDot;":                  true,
	"&DiacriticalDoubleAcute;":          true,
	"&DiacriticalGrave;":                true,
	"&DiacriticalTilde;":                true,
	"&Diamond;":                         true,
	"&DifferentialD;":                   true,
	"&Dopf;":                            true,
	"&Dot;":                             true,
	"&DotDot;":                          true,
	"&DotEqual;":                        true,
	"&DoubleContourIntegral;":           true,
	"&DoubleDot;":                       true,
	"&DoubleDownArrow;":                 true,
	"&DoubleLeftArrow;":                 true,
	"&DoubleLeftRightArrow;":            true,
	"&DoubleLeftTee;":                   true,
	"&DoubleLongLeftArrow;":             true,
	"&DoubleLongLeftRightArrow;":        true,
	"&DoubleLongRightArrow;":            true,
	"&DoubleRightArrow;":                true,
	"&DoubleRightTee;":                  true,
	"&DoubleUpArrow;":                   true,
	"&DoubleUpDownArrow;":               true,
	"&DoubleVerticalBar;":               true,
	"&DownArrow;":                       true,
	"&DownArrowBar;":                    true,
	"&DownArrowUpArrow;":                true,
	"&DownBreve;":                       true,
	"&DownLeftRightVector;":             true,
	"&DownLeftTeeVector;":               true,
	"&DownLeftVector;":                  true,
	"&DownLeftVectorBar;":               true,
	"&DownRightTeeVector;":              true,
	"&DownRightVector;":                 true,
	"&DownRightVectorBar;":              true,
	"&DownTee;":                         true,
	"&DownTeeArrow;":                    true,
	"&Downarrow;":                       true,
	"&Dscr;":                            true,
	"&Dstrok;":                          true,
	"&ENG;":                             true,
	"&ETH":                              true,
	"&ETH;":                             true,
	"&Eacute":                           true,
	"&Eacute;":                          true,
	"&Ecaron;":                          true,
	"&Ecirc":                            true,
	"&Ecirc;":                           true,
	"&Ecy;":                             true,
	"&Edot;":                            true,
	"&Efr;":                             true,
	"&Egrave":                           true,
	"&Egrave;":                          true,
	"&Element;":                         true,
	"&Emacr;":                           true,
	"&EmptySmallSquare;":                true,
	"&EmptyVerySmallSquare;":            true,
	"&Eogon;":                           true,
	"&Eopf;":                            true,
	"&Epsilon;":                         true,
	"&Equal;":                           true,
	"&EqualTilde;":                      true,
	"&Equilibrium;":                     true,
	"&Escr;":                            true,
	"&Esim;":                            true,
	"&Eta;":                             true,
	"&Euml":                             true,
	"&Euml;":                            true,
	"&Exists;":                          true,
	"&ExponentialE;":                    true,
	"&Fcy;":                             true,
	"&Ffr;":                             true,
	"&FilledSmallSquare;":               true,
	"&FilledVerySmallSquare;":           true,
	"&Fopf;":                            true,
	"&ForAll;":                          true,
	"&Fouriertrf;":                      true,
	"&Fscr;":                            true,
	"&GJcy;":                            true,
	"&GT":                               true,
	"&GT;":                              true,
	"&Gamma;":                           true,
	"&Gammad;":                          true,
	"&Gbreve;":                          true,
	"&Gcedil;":                          true,
	"&Gcirc;":                           true,
	"&Gcy;":                             true,
	"&Gdot;":                            true,
	"&Gfr;":                             true,
	"&Gg;":                              true,
	"&Gopf;":                            true,
	"&GreaterEqual;":                    true,
	"&GreaterEqualLess;":                true,
	"&GreaterFullEqual;":                true,
	"&GreaterGreater;":                  true,
	"&GreaterLess;":                     true,
	"&GreaterSlantEqual;":               true,
	"&GreaterTilde;":                    true,
	"&Gscr;":                            true,
	"&Gt;":                              true,
	"&HARDcy;":                          true,
	"&Hacek;":                           true,
	"&Hat;":                             true,
	"&Hcirc;":                           true,
	"&Hfr;":                             true,
	"&HilbertSpace;":                    true,
	"&Hopf;":                            true,
	"&HorizontalLine;":                  true,
	"&Hscr;":                            true,
	"&Hstrok;":                          true,
	"&HumpDownHump;":                    true,
	"&HumpEqual;":                       true,
	"&IEcy;":                            true,
	"&IJlig;":                           true,
	"&IOcy;":                            true,
	"&Iacute":                           true,
	"&Iacute;":                          true,
	"&Icirc":                            true,
	"&Icirc;":                           true,
	"&Icy;":                             true,
	"&Idot;":                            true,
	"&Ifr;":                             true,
	"&Igrave":                           true,
	"&Igrave;":                          true,
	"&Im;":                              true,
	"&Imacr;":                           true,
	"&ImaginaryI;":                      true,
	"&Implies;":                         true,
	"&Int;":                             true,
	"&Integral;":                        true,
	"&Intersection;":                    true,
	"&InvisibleComma;":                  true,
	"&InvisibleTimes;":                  true,
	"&Iogon;":                           true,
	"&Iopf;":                            true,
	"&Iota;":                            true,
	"&Iscr;":                            true,
	"&Itilde;":                          true,
	"&Iukcy;":                           true,
	"&Iuml":                             true,
	"&Iuml;":                            true,
	"&Jcirc;":                           true,
	"&Jcy;":                             true,
	"&Jfr;":                             true,
	"&Jopf;":                            true,
	"&Jscr;":                            true,
	"&Jsercy;":                          true,
	"&Jukcy;":                           true,
	"&KHcy;":                            true,
	"&KJcy;":                            true,
	"&Kappa;":                           true,
	"&Kcedil;":                          true,
	"&Kcy;":                             true,
	"&Kfr;":                             true,
	"&Kopf;":                            true,
	"&Kscr;":                            true,
	"&LJcy;":                            true,
	"&LT":                               true,
	"&LT;":                              true,
	"&Lacute;":                          true,
	"&Lambda;":                          true,
	"&Lang;":                            true,
	"&Laplacetrf;":                      true,
	"&Larr;":                            true,
	"&Lcaron;":                          true,
	"&Lcedil;":                          true,
	"&Lcy;":                             true,
	"&LeftAngleBracket;":                true,
	"&LeftArrow;":                       true,
	"&LeftArrowBar;":                    true,
	"&LeftArrowRightArrow;":             true,
	"&LeftCeiling;":                     true,
	"&LeftDoubleBracket;":               true,
	"&LeftDownTeeVector;":               true,
	"&LeftDownVector;":                  true,
	"&LeftDownVectorBar;":               true,
	"&LeftFloor;":                       true,
	"&LeftRightArrow;":                  true,
	"&LeftRightVector;":                 true,
	"&LeftTee;":                         true,
	"&LeftTeeArrow;":                    true,
	"&LeftTeeVector;":                   true,
	"&LeftTriangle;":                    true,
	"&LeftTriangleBar;":                 true,
	"&LeftTriangleEqual;":               true,
	"&LeftUpDownVector;":                true,
	"&LeftUpTeeVector;":                 true,
	"&LeftUpVector;":                    true,
	"&LeftUpVectorBar;":                 true,
	"&LeftVector;":                      true,
	"&LeftVectorBar;":                   true,
	"&Leftarrow;":                       true,
	"&Leftrightarrow;":                  true,
	"&LessEqualGreater;":                true,
	"&LessFullEqual;":                   true,
	"&LessGreater;":                     true,
	"&LessLess;":                        true,
	"&LessSlantEqual;":                  true,
	"&LessTilde;":                       true,
	"&Lfr;":                             true,
	"&Ll;":                              true,
	"&Lleftarrow;":                      true,
	"&Lmidot;":                          true,
	"&LongLeftArrow;":                   true,
	"&LongLeftRightArrow;":              true,
	"&LongRightArrow;":                  true,
	"&Longleftarrow;":                   true,
	"&Longleftrightarrow;":              true,
	"&Longrightarrow;":                  true,
	"&Lopf;":                            true,
	"&LowerLeftArrow;":                  true,
	"&LowerRightArrow;":                 true,
	"&Lscr;":                            true,
	"&Lsh;":                             true,
	"&Lstrok;":                          true,
	"&Lt;":                              true,
	"&Map;":                             true,
	"&Mcy;":                             true,
	"&MediumSpace;":                     true,
	"&Mellintrf;":                       true,
	"&Mfr;":                             true,
	"&MinusPlus;":                       true,
	"&Mopf;":                            true,
	"&Mscr;":                            true,
	"&Mu;":                              true,
	"&NJcy;":                            true,
	"&Nacute;":                          true,
	"&Ncaron;":                          true,
	"&Ncedil;":                          true,
	"&Ncy;":                             true,
	"&NegativeMediumSpace;":             true,
	"&NegativeThickSpace;":              true,
	"&NegativeThinSpace;":               true,
	"&NegativeVeryThinSpace;":           true,
	"&NestedGreaterGreater;":            true,
	"&NestedLessLess;":                  true,
	"&NewLine;":                         true,
	"&Nfr;":                             true,
	"&NoBreak;":                         true,
	"&NonBreakingSpace;":                true,
	"&Nopf;":                            true,
	"&Not;":                             true,
	"&NotCongruent;":                    true,
	"&NotCupCap;":                       true,
	"&NotDoubleVerticalBar;":            true,
	"&NotElement;":                      true,
	"&NotEqual;":                        true,
	"&NotEqualTilde;":                   true,
	"&NotExists;":                       true,
	"&NotGreater;":                      true,
	"&NotGreaterEqual;":                 true,
	"&NotGreaterFullEqual;":             true,
	"&NotGreaterGreater;":               true,
	"&NotGreaterLess;":                  true,
	"&NotGreaterSlantEqual;":            true,
	"&NotGreaterTilde;":                 true,
	"&NotHumpDownHump;":                 true,
	"&NotHumpEqual;":                    true,
	"&NotLeftTriangle;":                 true,
	"&NotLeftTriangleBar;":              true,
	"&NotLeftTriangleEqual;":            true,
	"&NotLess;":                         true,
	"&NotLessEqual;":                    true,
	"&NotLessGreater;":                  true,
	"&NotLessLess;":                     true,
	"&NotLessSlantEqual;":               true,
	"&NotLessTilde;":                    true,
	"&NotNestedGreaterGreater;":         true,
	"&NotNestedLessLess;":               true,
	"&NotPrecedes;":                     true,
	"&NotPrecedesEqual;":                true,
	"&NotPrecedesSlantEqual;":           true,
	"&NotReverseElement;":               true,
	"&NotRightTriangle;":                true,
	"&NotRightTriangleBar;":             true,
	"&NotRightTriangleEqual;":           true,
	"&NotSquareSubset;":                 true,
	"&NotSquareSubsetEqual;":            true,
	"&NotSquareSuperset;":               true,
	"&NotSquareSupersetEqual;":          true,
	"&NotSubset;":                       true,
	"&NotSubsetEqual;":                  true,
	"&NotSucceeds;":                     true,
	"&NotSucceedsEqual;":                true,
	"&NotSucceedsSlantEqual;":           true,
	"&NotSucceedsTilde;":                true,
	"&NotSuperset;":                     true,
	"&NotSupersetEqual;":                true,
	"&NotTilde;":                        true,
	"&NotTildeEqual;":                   true,
	"&NotTildeFullEqual;":               true,
	"&NotTildeTilde;":                   true,
	"&NotVerticalBar;":                  true,
	"&Nscr;":                            true,
	"&Ntilde":                           true,
	"&Ntilde;":                          true,
	"&Nu;":                              true,
	"&OElig;":                           true,
	"&Oacute":                           true,
	"&Oacute;":                          true,
	"&Ocirc":                            true,
	"&Ocirc;":                           true,
	"&Ocy;":                             true,
	"&Odblac;":                          true,
	"&Ofr;":                             true,
	"&Ograve":                           true,
	"&Ograve;":                          true,
	"&Omacr;":                           true,
	"&Omega;":                           true,
	"&Omicron;":                         true,
	"&Oopf;":                            true,
	"&OpenCurlyDoubleQuote;":            true,
	"&OpenCurlyQuote;":                  true,
	"&Or;":                              true,
	"&Oscr;":                            true,
	"&Oslash":                           true,
	"&Oslash;":                          true,
	"&Otilde":                           true,
	"&Otilde;":                          true,
	"&Otimes;":                          true,
	"&Ouml":                             true,
	"&Ouml;":                            true,
	"&OverBar;":                         true,
	"&OverBrace;":                       true,
	"&OverBracket;":                     true,
	"&OverParenthesis;":                 true,
	"&PartialD;":                        true,
	"&Pcy;":                             true,
	"&Pfr;":                             true,
	"&Phi;":                             true,
	"&Pi;":                              true,
	"&PlusMinus;":                       true,
	"&Poincareplane;":                   true,
	"&Popf;":                            true,
	"&Pr;":                              true,
	"&Precedes;":                        true,
	"&PrecedesEqual;":                   true,
	"&PrecedesSlantEqual;":              true,
	"&PrecedesTilde;":                   true,
	"&Prime;":                           true,
	"&Product;":                         true,
	"&Proportion;":                      true,
	"&Proportional;":                    true,
	"&Pscr;":                            true,
	"&Psi;":                             true,
	"&QUOT":                             true,
	"&QUOT;":                            true,
	"&Qfr;":                             true,
	"&Qopf;":                            true,
	"&Qscr;":                            true,
	"&RBarr;":                           true,
	"&REG":                              true,
	"&REG;":                             true,
	"&Racute;":                          true,
	"&Rang;":                            true,
	"&Rarr;":                            true,
	"&Rarrtl;":                          true,
	"&Rcaron;":                          true,
	"&Rcedil;":                          true,
	"&Rcy;":                             true,
	"&Re;":                              true,
	"&ReverseElement;":                  true,
	"&ReverseEquilibrium;":              true,
	"&ReverseUpEquilibrium;":            true,
	"&Rfr;":                             true,
	"&Rho;":                             true,
	"&RightAngleBracket;":               true,
	"&RightArrow;":                      true,
	"&RightArrowBar;":                   true,
	"&RightArrowLeftArrow;":             true,
	"&RightCeiling;":                    true,
	"&RightDoubleBracket;":              true,
	"&RightDownTeeVector;":              true,
	"&RightDownVector;":                 true,
	"&RightDownVectorBar;":              true,
	"&RightFloor;":                      true,
	"&RightTee;":                        true,
	"&RightTeeArrow;":                   true,
	"&RightTeeVector;":                  true,
	"&RightTriangle;":                   true,
	"&RightTriangleBar;":                true,
	"&RightTriangleEqual;":              true,
	"&RightUpDownVector;":               true,
	"&RightUpTeeVector;":                true,
	"&RightUpVector;":                   true,
	"&RightUpVectorBar;":                true,
	"&RightVector;":                     true,
	"&RightVectorBar;":                  true,
	"&Rightarrow;":                      true,
	"&Ropf;":                            true,
	"&RoundImplies;":                    true,
	"&Rrightarrow;":                     true,
	"&Rscr;":                            true,
	"&Rsh;":                             true,
	"&RuleDelayed;":                     true,
	"&SHCHcy;":                          true,
	"&SHcy;":                            true,
	"&SOFTcy;":                          true,
	"&Sacute;":                          true,
	"&Sc;":                              true,
	"&Scaron;":                          true,
	"&Scedil;":                          true,
	"&Scirc;":                           true,
	"&Scy;":                             true,
	"&Sfr;":                             true,
	"&ShortDownArrow;":                  true,
	"&ShortLeftArrow;":                  true,
	"&ShortRightArrow;":                 true,
	"&ShortUpArrow;":                    true,
	"&Sigma;":                           true,
	"&SmallCircle;":                     true,
	"&Sopf;":                            true,
	"&Sqrt;":                            true,
	"&Square;":                          true,
	"&SquareIntersection;":              true,
	"&SquareSubset;":                    true,
	"&SquareSubsetEqual;":               true,
	"&SquareSuperset;":                  true,
	"&SquareSupersetEqual;":             true,
	"&SquareUnion;":                     true,
	"&Sscr;":                            true,
	"&Star;":                            true,
	"&Sub;":                             true,
	"&Subset;":                          true,
	"&SubsetEqual;":                     true,
	"&Succeeds;":                        true,
	"&SucceedsEqual;":                   true,
	"&SucceedsSlantEqual;":              true,
	"&SucceedsTilde;":                   true,
	"&SuchThat;":                        true,
	"&Sum;":                             true,
	"&Sup;":                             true,
	"&Superset;":                        true,
	"&SupersetEqual;":                   true,
	"&Supset;":                          true,
	"&THORN":                            true,
	"&THORN;":                           true,
	"&TRADE;":                           true,
	"&TSHcy;":                           true,
	"&TScy;":                            true,
	"&Tab;":                             true,
	"&Tau;":                             true,
	"&Tcaron;":                          true,
	"&Tcedil;":                          true,
	"&Tcy;":                             true,
	"&Tfr;":                             true,
	"&Therefore;":                       true,
	"&Theta;":                           true,
	"&ThickSpace;":                      true,
	"&ThinSpace;":                       true,
	"&Tilde;":                           true,
	"&TildeEqual;":                      true,
	"&TildeFullEqual;":                  true,
	"&TildeTilde;":                      true,
	"&Topf;":                            true,
	"&TripleDot;":                       true,
	"&Tscr;":                            true,
	"&Tstrok;":                          true,
	"&Uacute":                           true,
	"&Uacute;":                          true,
	"&Uarr;":                            true,
	"&Uarrocir;":                        true,
	"&Ubrcy;":                           true,
	"&Ubreve;":                          true,
	"&Ucirc":                            true,
	"&Ucirc;":                           true,
	"&Ucy;":                             true,
	"&Udblac;":                          true,
	"&Ufr;":                             true,
	"&Ugrave":                           true,
	"&Ugrave;":                          true,
	"&Umacr;":                           true,
	"&UnderBar;":                        true,
	"&UnderBrace;":                      true,
	"&UnderBracket;":                    true,
	"&UnderParenthesis;":                true,
	"&Union;":                           true,
	"&UnionPlus;":                       true,
	"&Uogon;":                           true,
	"&Uopf;":                            true,
	"&UpArrow;":                         true,
	"&UpArrowBar;":                      true,
	"&UpArrowDownArrow;":                true,
	"&UpDownArrow;":                     true,
	"&UpEquilibrium;":                   true,
	"&UpTee;":                           true,
	"&UpTeeArrow;":                      true,
	"&Uparrow;":                         true,
	"&Updownarrow;":                     true,
	"&UpperLeftArrow;":                  true,
	"&UpperRightArrow;":                 true,
	"&Upsi;":                            true,
	"&Upsilon;":                         true,
	"&Uring;":                           true,
	"&Uscr;":                            true,
	"&Utilde;":                          true,
	"&Uuml":                             true,
	"&Uuml;":                            true,
	"&VDash;":                           true,
	"&Vbar;":                            true,
	"&Vcy;":                             true,
	"&Vdash;":                           true,
	"&Vdashl;":                          true,
	"&Vee;":                             true,
	"&Verbar;":                          true,
	"&Vert;":                            true,
	"&VerticalBar;":                     true,
	"&VerticalLine;":                    true,
	"&VerticalSeparator;":               true,
	"&VerticalTilde;":                   true,
	"&VeryThinSpace;":                   true,
	"&Vfr;":                             true,
	"&Vopf;":                            true,
	"&Vscr;":                            true,
	"&Vvdash;":                          true,
	"&Wcirc;":                           true,
	"&Wedge;":                           true,
	"&Wfr;":                             true,
	"&Wopf;":                            true,
	"&Wscr;":                            true,
	"&Xfr;":                             true,
	"&Xi;":                              true,
	"&Xopf;":                            true,
	"&Xscr;":                            true,
	"&YAcy;":                            true,
	"&YIcy;":                            true,
	"&YUcy;":                            true,
	"&Yacute":                           true,
	"&Yacute;":                          true,
	"&Ycirc;":                           true,
	"&Ycy;":                             true,
	"&Yfr;":                             true,
	"&Yopf;":                            true,
	"&Yscr;":                            true,
	"&Yuml;":                            true,
	"&ZHcy;":                            true,
	"&Zacute;":                          true,
	"&Zcaron;":                          true,
	"&Zcy;":                             true,
	"&Zdot;":                            true,
	"&ZeroWidthSpace;":                  true,
	"&Zeta;":                            true,
	"&Zfr;":                             true,
	"&Zopf;":                            true,
	"&Zscr;":                            true,
	"&aacute":                           true,
	"&aacute;":                          true,
	"&abreve;":                          true,
	"&ac;":                              true,
	"&acE;":                             true,
	"&acd;":                             true,
	"&acirc":                            true,
	"&acirc;":                           true,
	"&acute":                            true,
	"&acute;":                           true,
	"&acy;":                             true,
	"&aelig":                            true,
	"&aelig;":                           true,
	"&af;":                              true,
	"&afr;":                             true,
	"&agrave":                           true,
	"&agrave;":                          true,
	"&alefsym;":                         true,
	"&aleph;":                           true,
	"&alpha;":                           true,
	"&amacr;":                           true,
	"&amalg;":                           true,
	"&amp":                              true,
	"&amp;":                             true,
	"&and;":                             true,
	"&andand;":                          true,
	"&andd;":                            true,
	"&andslope;":                        true,
	"&andv;":                            true,
	"&ang;":                             true,
	"&ange;":                            true,
	"&angle;":                           true,
	"&angmsd;":                          true,
	"&angmsdaa;":                        true,
	"&angmsdab;":                        true,
	"&angmsdac;":                        true,
	"&angmsdad;":                        true,
	"&angmsdae;":                        true,
	"&angmsdaf;":                        true,
	"&angmsdag;":                        true,
	"&angmsdah;":                        true,
	"&angrt;":                           true,
	"&angrtvb;":                         true,
	"&angrtvbd;":                        true,
	"&angsph;":                          true,
	"&angst;":                           true,
	"&angzarr;":                         true,
	"&aogon;":                           true,
	"&aopf;":                            true,
	"&ap;":                              true,
	"&apE;":                             true,
	"&apacir;":                          true,
	"&ape;":                             true,
	"&apid;":                            true,
	"&apos;":                            true,
	"&approx;":                          true,
	"&approxeq;":                        true,
	"&aring":                            true,
	"&aring;":                           true,
	"&ascr;":                            true,
	"&ast;":                             true,
	"&asymp;":                           true,
	"&asympeq;":                         true,
	"&atilde":                           true,
	"&atilde;":                          true,
	"&auml":                             true,
	"&auml;":                            true,
	"&awconint;":                        true,
	"&awint;":                           true,
	"&bNot;":                            true,
	"&backcong;":                        true,
	"&backepsilon;":                     true,
	"&backprime;":                       true,
	"&backsim;":                         true,
	"&backsimeq;":                       true,
	"&barvee;":                          true,
	"&barwed;":                          true,
	"&barwedge;":                        true,
	"&bbrk;":                            true,
	"&bbrktbrk;":                        true,
	"&bcong;":                           true,
	"&bcy;":                             true,
	"&bdquo;":                           true,
	"&becaus;":                          true,
	"&because;":                         true,
	"&bemptyv;":                         true,
	"&bepsi;":                           true,
	"&bernou;":                          true,
	"&beta;":                            true,
	"&beth;":                            true,
	"&between;":                         true,
	"&bfr;":                             true,
	"&bigcap;":                          true,
	"&bigcirc;":                         true,
	"&bigcup;":                          true,
	"&bigodot;":                         true,
	"&bigoplus;":                        true,
	"&bigotimes;":                       true,
	"&bigsqcup;":                        true,
	"&bigstar;":                         true,
	"&bigtriangledown;":                 true,
	"&bigtriangleup;":                   true,
	"&biguplus;":                        true,
	"&bigvee;":                          true,
	"&bigwedge;":                        true,
	"&bkarow;":                          true,
	"&blacklozenge;":                    true,
	"&blacksquare;":                     true,
	"&blacktriangle;":                   true,
	"&blacktriangledown;":               true,
	"&blacktriangleleft;":               true,
	"&blacktriangleright;":              true,
	"&blank;":                           true,
	"&blk12;":                           true,
	"&blk14;":                           true,
	"&blk34;":                           true,
	"&block;":                           true,
	"&bne;":                             true,
	"&bnequiv;":                         true,
	"&bnot;":                            true,
	"&bopf;":                            true,
	"&bot;":                             true,
	"&bottom;":                          true,
	"&bowtie;":                          true,
	"&boxDL;":                           true,
	"&boxDR;":                           true,
	"&boxDl;":                           true,
	"&boxDr;":                           true,
	"&boxH;":                            true,
	"&boxHD;":                           true,
	"&boxHU;":                           true,
	"&boxHd;":                           true,
	"&boxHu;":                           true,
	"&boxUL;":                           true,
	"&boxUR;":                           true,
	"&boxUl;":                           true,
	"&boxUr;":                           true,
	"&boxV;":                            true,
	"&boxVH;":                           true,
	"&boxVL;":                           true,
	"&boxVR;":                           true,
	"&boxVh;":                           true,
	"&boxVl;":                           true,
	"&boxVr;":                           true,
	"&boxbox;":                          true,
	"&boxdL;":                           true,
	"&boxdR;":                           true,
	"&boxdl;":                           true,
	"&boxdr;":                           true,
	"&boxh;":                            true,
	"&boxhD;":                           true,
	"&boxhU;":                           true,
	"&boxhd;":                           true,
	"&boxhu;":                           true,
	"&boxminus;":                        true,
	"&boxplus;":                         true,
	"&boxtimes;":                        true,
	"&boxuL;":                           true,
	"&boxuR;":                           true,
	"&boxul;":                           true,
	"&boxur;":                           true,
	"&boxv;":                            true,
	"&boxvH;":                           true,
	"&boxvL;":                           true,
	"&boxvR;":                           true,
	"&boxvh;":                           true,
	"&boxvl;":                           true,
	"&boxvr;":                           true,
	"&bprime;":                          true,
	"&breve;":                           true,
	"&brvbar":                           true,
	"&brvbar;":                          true,
	"&bscr;":                            true,
	"&bsemi;":                           true,
	"&bsim;":                            true,
	"&bsime;":                           true,
	"&bsol;":                            true,
	"&bsolb;":                           true,
	"&bsolhsub;":                        true,
	"&bull;":                            true,
	"&bullet;":                          true,
	"&bump;":                            true,
	"&bumpE;":                           true,
	"&bumpe;":                           true,
	"&bumpeq;":                          true,
	"&cacute;":                          true,
	"&cap;":                             true,
	"&capand;":                          true,
	"&capbrcup;":                        true,
	"&capcap;":                          true,
	"&capcup;":                          true,
	"&capdot;":                          true,
	"&caps;":                            true,
	"&caret;":                           true,
	"&caron;":                           true,
	"&ccaps;":                           true,
	"&ccaron;":                          true,
	"&ccedil":                           true,
	"&ccedil;":                          true,
	"&ccirc;":                           true,
	"&ccups;":                           true,
	"&ccupssm;":                         true,
	"&cdot;":                            true,
	"&cedil":                            true,
	"&cedil;":                           true,
	"&cemptyv;":                         true,
	"&cent":                             true,
	"&cent;":                            true,
	"&centerdot;":                       true,
	"&cfr;":                             true,
	"&chcy;":                            true,
	"&check;":                           true,
	"&checkmark;":                       true,
	"&chi;":                             true,
	"&cir;":                             true,
	"&cirE;":                            true,
	"&circ;":                            true,
	"&circeq;":                          true,
	"&circlearrowleft;":                 true,
	"&circlearrowright;":                true,
	"&circledR;":                        true,
	"&circledS;":                        true,
	"&circledast;":                      true,
	"&circledcirc;":                     true,
	"&circleddash;":                     true,
	"&cire;":                            true,
	"&cirfnint;":                        true,
	"&cirmid;":                          true,
	"&cirscir;":                         true,
	"&clubs;":                           true,
	"&clubsuit;":                        true,
	"&colon;":                           true,
	"&colone;":                          true,
	"&coloneq;":                         true,
	"&comma;":                           true,
	"&commat;":                          true,
	"&comp;":                            true,
	"&compfn;":                          true,
	"&complement;":                      true,
	"&complexes;":                       true,
	"&cong;":                            true,
	"&congdot;":                         true,
	"&conint;":                          true,
	"&copf;":                            true,
	"&coprod;":                          true,
	"&copy":                             true,
	"&copy;":                            true,
	"&copysr;":                          true,
	"&crarr;":                           true,
	"&cross;":                           true,
	"&cscr;":                            true,
	"&csub;":                            true,
	"&csube;":                           true,
	"&csup;":                            true,
	"&csupe;":                           true,
	"&ctdot;":                           true,
	"&cudarrl;":                         true,
	"&cudarrr;":                         true,
	"&cuepr;":                           true,
	"&cuesc;":                           true,
	"&cularr;":                          true,
	"&cularrp;":                         true,
	"&cup;":                             true,
	"&cupbrcap;":                        true,
	"&cupcap;":                          true,
	"&cupcup;":                          true,
	"&cupdot;":                          true,
	"&cupor;":                           true,
	"&cups;":                            true,
	"&curarr;":                          true,
	"&curarrm;":                         true,
	"&curlyeqprec;":                     true,
	"&curlyeqsucc;":                     true,
	"&curlyvee;":                        true,
	"&curlywedge;":                      true,
	"&curren":                           true,
	"&curren;":                          true,
	"&curvearrowleft;":                  true,
	"&curvearrowright;":                 true,
	"&cuvee;":                           true,
	"&cuwed;":                           true,
	"&cwconint;":                        true,
	"&cwint;":                           true,
	"&cylcty;":                          true,
	"&dArr;":                            true,
	"&dHar;":                            true,
	"&dagger;":                          true,
	"&daleth;":                          true,
	"&darr;":                            true,
	"&dash;":                            true,
	"&dashv;":                           true,
	"&dbkarow;":                         true,
	"&dblac;":                           true,
	"&dcaron;":                          true,
	"&dcy;":                             true,
	"&dd;":                              true,
	"&ddagger;":                         true,
	"&ddarr;":                           true,
	"&ddotseq;":                         true,
	"&deg":                              true,
	"&deg;":                             true,
	"&delta;":                           true,
	"&demptyv;":                         true,
	"&dfisht;":                          true,
	"&dfr;":                             true,
	"&dharl;":                           true,
	"&dharr;":                           true,
	"&diam;":                            true,
	"&diamond;":                         true,
	"&diamondsuit;":                     true,
	"&diams;":                           true,
	"&die;":                             true,
	"&digamma;":                         true,
	"&disin;":                           true,
	"&div;":                             true,
	"&divide":                           true,
	"&divide;":                          true,
	"&divideontimes;":                   true,
	"&divonx;":                          true,
	"&djcy;":                            true,
	"&dlcorn;":                          true,
	"&dlcrop;":                          true,
	"&dollar;":                          true,
	"&dopf;":                            true,
	"&dot;":                             true,
	"&doteq;":                           true,
	"&doteqdot;":                        true,
	"&dotminus;":                        true,
	"&dotplus;":                         true,
	"&dotsquare;":                       true,
	"&doublebarwedge;":                  true,
	"&downarrow;":                       true,
	"&downdownarrows;":                  true,
	"&downharpoonleft;":                 true,
	"&downharpoonright;":                true,
	"&drbkarow;":                        true,
	"&drcorn;":                          true,
	"&drcrop;":                          true,
	"&dscr;":                            true,
	"&dscy;":                            true,
	"&dsol;":                            true,
	"&dstrok;":                          true,
	"&dtdot;":                           true,
	"&dtri;":                            true,
	"&dtrif;":                           true,
	"&duarr;":                           true,
	"&duhar;":                           true,
	"&dwangle;":                         true,
	"&dzcy;":                            true,
	"&dzigrarr;":                        true,
	"&eDDot;":                           true,
	"&eDot;":                            true,
	"&eacute":                           true,
	"&eacute;":                          true,
	"&easter;":                          true,
	"&ecaron;":                          true,
	"&ecir;":                            true,
	"&ecirc":                            true,
	"&ecirc;":                           true,
	"&ecolon;":                          true,
	"&ecy;":                             true,
	"&edot;":                            true,
	"&ee;":                              true,
	"&efDot;":                           true,
	"&efr;":                             true,
	"&eg;":                              true,
	"&egrave":                           true,
	"&egrave;":                          true,
	"&egs;":                             true,
	"&egsdot;":                          true,
	"&el;":                              true,
	"&elinters;":                        true,
	"&ell;":                             true,
	"&els;":                             true,
	"&elsdot;":                          true,
	"&emacr;":                           true,
	"&empty;":                           true,
	"&emptyset;":                        true,
	"&emptyv;":                          true,
	"&emsp13;":                          true,
	"&emsp14;":                          true,
	"&emsp;":                            true,
	"&eng;":                             true,
	"&ensp;":                            true,
	"&eogon;":                           true,
	"&eopf;":                            true,
	"&epar;":                            true,
	"&eparsl;":                          true,
	"&eplus;":                           true,
	"&epsi;":                            true,
	"&epsilon;":                         true,
	"&epsiv;":                           true,
	"&eqcirc;":                          true,
	"&eqcolon;":                         true,
	"&eqsim;":                           true,
	"&eqslantgtr;":                      true,
	"&eqslantless;":                     true,
	"&equals;":                          true,
	"&equest;":                          true,
	"&equiv;":                           true,
	"&equivDD;":                         true,
	"&eqvparsl;":                        true,
	"&erDot;":                           true,
	"&erarr;":                           true,
	"&escr;":                            true,
	"&esdot;":                           true,
	"&esim;":                            true,
	"&eta;":                             true,
	"&eth":                              true,
	"&eth;":                             true,
	"&euml":                             true,
	"&euml;":                            true,
	"&euro;":                            true,
	"&excl;":                            true,
	"&exist;":                           true,
	"&expectation;":                     true,
	"&exponentiale;":                    true,
	"&fallingdotseq;":                   true,
	"&fcy;":                             true,
	"&female;":                          true,
	"&ffilig;":                          true,
	"&fflig;":                           true,
	"&ffllig;":                          true,
	"&ffr;":                             true,
	"&filig;":                           true,
	"&fjlig;":                           true,
	"&flat;":                            true,
	"&fllig;":                           true,
	"&fltns;":                           true,
	"&fnof;":                            true,
	"&fopf;":                            true,
	"&forall;":                          true,
	"&fork;":                            true,
	"&forkv;":                           true,
	"&fpartint;":                        true,
	"&frac12":                           true,
	"&frac12;":                          true,
	"&frac13;":                          true,
	"&frac14":                           true,
	"&frac14;":                          true,
	"&frac15;":                          true,
	"&frac16;":                          true,
	"&frac18;":                          true,
	"&frac23;":                          true,
	"&frac25;":                          true,
	"&frac34":                           true,
	"&frac34;":                          true,
	"&frac35;":                          true,
	"&frac38;":                          true,
	"&frac45;":                          true,
	"&frac56;":                          true,
	"&frac58;":                          true,
	"&frac78;":                          true,
	"&frasl;":                           true,
	"&frown;":                           true,
	"&fscr;":                            true,
	"&gE;":                              true,
	"&gEl;":                             true,
	"&gacute;":                          true,
	"&gamma;":                           true,
	"&gammad;":                          true,
	"&gap;":                             true,
	"&gbreve;":                          true,
	"&gcirc;":                           true,
	"&gcy;":                             true,
	"&gdot;":                            true,
	"&ge;":                              true,
	"&gel;":                             true,
	"&geq;":                             true,
	"&geqq;":                            true,
	"&geqslant;":                        true,
	"&ges;":                             true,
	"&gescc;":                           true,
	"&gesdot;":                          true,
	"&gesdoto;":                         true,
	"&gesdotol;":                        true,
	"&gesl;":                            true,
	"&gesles;":                          true,
	"&gfr;":                             true,
	"&gg;":                              true,
	"&ggg;":                             true,
	"&gimel;":                           true,
	"&gjcy;":                            true,
	"&gl;":                              true,
	"&glE;":                             true,
	"&gla;":                             true,
	"&glj;":                             true,
	"&gnE;":                             true,
	"&gnap;":                            true,
	"&gnapprox;":                        true,
	"&gne;":                             true,
	"&gneq;":                            true,
	"&gneqq;":                           true,
	"&gnsim;":                           true,
	"&gopf;":                            true,
	"&grave;":                           true,
	"&gscr;":                            true,
	"&gsim;":                            true,
	"&gsime;":                           true,
	"&gsiml;":                           true,
	"&gt":                               true,
	"&gt;":                              true,
	"&gtcc;":                            true,
	"&gtcir;":                           true,
	"&gtdot;":                           true,
	"&gtlPar;":                          true,
	"&gtquest;":                         true,
	"&gtrapprox;":                       true,
	"&gtrarr;":                          true,
	"&gtrdot;":                          true,
	"&gtreqless;":                       true,
	"&gtreqqless;":                      true,
	"&gtrless;":                         true,
	"&gtrsim;":                          true,
	"&gvertneqq;":                       true,
	"&gvnE;":                            true,
	"&hArr;":                            true,
	"&hairsp;":                          true,
	"&half;":                            true,
	"&hamilt;":                          true,
	"&hardcy;":                          true,
	"&harr;":                            true,
	"&harrcir;":                         true,
	"&harrw;":                           true,
	"&hbar;":                            true,
	"&hcirc;":                           true,
	"&hearts;":                          true,
	"&heartsuit;":                       true,
	"&hellip;":                          true,
	"&hercon;":                          true,
	"&hfr;":                             true,
	"&hksearow;":                        true,
	"&hkswarow;":                        true,
	"&hoarr;":                           true,
	"&homtht;":                          true,
	"&hookleftarrow;":                   true,
	"&hookrightarrow;":                  true,
	"&hopf;":                            true,
	"&horbar;":                          true,
	"&hscr;":                            true,
	"&hslash;":                          true,
	"&hstrok;":                          true,
	"&hybull;":                          true,
	"&hyphen;":                          true,
	"&iacute":                           true,
	"&iacute;":                          true,
	"&ic;":                              true,
	"&icirc":                            true,
	"&icirc;":                           true,
	"&icy;":                             true,
	"&iecy;":                            true,
	"&iexcl":                            true,
	"&iexcl;":                           true,
	"&iff;":                             true,
	"&ifr;":                             true,
	"&igrave":                           true,
	"&igrave;":                          true,
	"&ii;":                              true,
	"&iiiint;":                          true,
	"&iiint;":                           true,
	"&iinfin;":                          true,
	"&iiota;":                           true,
	"&ijlig;":                           true,
	"&imacr;":                           true,
	"&image;":                           true,
	"&imagline;":                        true,
	"&imagpart;":                        true,
	"&imath;":                           true,
	"&imof;":                            true,
	"&imped;":                           true,
	"&in;":                              true,
	"&incare;":                          true,
	"&infin;":                           true,
	"&infintie;":                        true,
	"&inodot;":                          true,
	"&int;":                             true,
	"&intcal;":                          true,
	"&integers;":                        true,
	"&intercal;":                        true,
	"&intlarhk;":                        true,
	"&intprod;":                         true,
	"&iocy;":                            true,
	"&iogon;":                           true,
	"&iopf;":                            true,
	"&iota;":                            true,
	"&iprod;":                           true,
	"&iquest":                           true,
	"&iquest;":                          true,
	"&iscr;":                            true,
	"&isin;":                            true,
	"&isinE;":                           true,
	"&isindot;":                         true,
	"&isins;":                           true,
	"&isinsv;":                          true,
	"&isinv;":                           true,
	"&it;":                              true,
	"&itilde;":                          true,
	"&iukcy;":                           true,
	"&iuml":                             true,
	"&iuml;":                            true,
	"&jcirc;":                           true,
	"&jcy;":                             true,
	"&jfr;":                             true,
	"&jmath;":                           true,
	"&jopf;":                            true,
	"&jscr;":                            true,
	"&jsercy;":                          true,
	"&jukcy;":                           true,
	"&kappa;":                           true,
	"&kappav;":                          true,
	"&kcedil;":                          true,
	"&kcy;":                             true,
	"&kfr;":                             true,
	"&kgreen;":                          true,
	"&khcy;":                            true,
	"&kjcy;":                            true,
	"&kopf;":                            true,
	"&kscr;":                            true,
	"&lAarr;":                           true,
	"&lArr;":                            true,
	"&lAtail;":                          true,
	"&lBarr;":                           true,
	"&lE;":                              true,
	"&lEg;":                             true,
	"&lHar;":                            true,
	"&lacute;":                          true,
	"&laemptyv;":                        true,
	"&lagran;":                          true,
	"&lambda;":                          true,
	"&lang;":                            true,
	"&langd;":                           true,
	"&langle;":                          true,
	"&lap;":                             true,
	"&laquo":                            true,
	"&laquo;":                           true,
	"&larr;":                            true,
	"&larrb;":                           true,
	"&larrbfs;":                         true,
	"&larrfs;":                          true,
	"&larrhk;":                          true,
	"&larrlp;":                          true,
	"&larrpl;":                          true,
	"&larrsim;":                         true,
	"&larrtl;":                          true,
	"&lat;":                             true,
	"&latail;":                          true,
	"&late;":                            true,
	"&lates;":                           true,
	"&lbarr;":                           true,
	"&lbbrk;":                           true,
	"&lbrace;":                          true,
	"&lbrack;":                          true,
	"&lbrke;":                           true,
	"&lbrksld;":                         true,
	"&lbrkslu;":                         true,
	"&lcaron;":                          true,
	"&lcedil;":                          true,
	"&lceil;":                           true,
	"&lcub;":                            true,
	"&lcy;":                             true,
	"&ldca;":                            true,
	"&ldquo;":                           true,
	"&ldquor;":                          true,
	"&ldrdhar;":                         true,
	"&ldrushar;":                        true,
	"&ldsh;":                            true,
	"&le;":                              true,
	"&leftarrow;":                       true,
	"&leftarrowtail;":                   true,
	"&leftharpoondown;":                 true,
	"&leftharpoonup;":                   true,
	"&leftleftarrows;":                  true,
	"&leftrightarrow;":                  true,
	"&leftrightarrows;":                 true,
	"&leftrightharpoons;":               true,
	"&leftrightsquigarrow;":             true,
	"&leftthreetimes;":                  true,
	"&leg;":                             true,
	"&leq;":                             true,
	"&leqq;":                            true,
	"&leqslant;":                        true,
	"&les;":                             true,
	"&lescc;":                           true,
	"&lesdot;":                          true,
	"&lesdoto;":                         true,
	"&lesdotor;":                        true,
	"&lesg;":                            true,
	"&lesges;":                          true,
	"&lessapprox;":                      true,
	"&lessdot;":                         true,
	"&lesseqgtr;":                       true,
	"&lesseqqgtr;":                      true,
	"&lessgtr;":                         true,
	"&lesssim;":                         true,
	"&lfisht;":                          true,
	"&lfloor;":                          true,
	"&lfr;":                             true,
	"&lg;":                              true,
	"&lgE;":                             true,
	"&lhard;":                           true,
	"&lharu;":                           true,
	"&lharul;":                          true,
	"&lhblk;":                           true,
	"&ljcy;":                            true,
	"&ll;":                              true,
	"&llarr;":                           true,
	"&llcorner;":                        true,
	"&llhard;":                          true,
	"&lltri;":                           true,
	"&lmidot;":                          true,
	"&lmoust;":                          true,
	"&lmoustache;":                      true,
	"&lnE;":                             true,
	"&lnap;":                            true,
	"&lnapprox;":                        true,
	"&lne;":                             true,
	"&lneq;":                            true,
	"&lneqq;":                           true,
	"&lnsim;":                           true,
	"&loang;":                           true,
	"&loarr;":                           true,
	"&lobrk;":                           true,
	"&longleftarrow;":                   true,
	"&longleftrightarrow;":              true,
	"&longmapsto;":                      true,
	"&longrightarrow;":                  true,
	"&looparrowleft;":                   true,
	"&looparrowright;":                  true,
	"&lopar;":                           true,
	"&lopf;":                            true,
	"&loplus;":                          true,
	"&lotimes;":                         true,
	"&lowast;":                          true,
	"&lowbar;":                          true,
	"&loz;":                             true,
	"&lozenge;":                         true,
	"&lozf;":                            true,
	"&lpar;":                            true,
	"&lparlt;":                          true,
	"&lrarr;":                           true,
	"&lrcorner;":                        true,
	"&lrhar;":                           true,
	"&lrhard;":                          true,
	"&lrm;":                             true,
	"&lrtri;":                           true,
	"&lsaquo;":                          true,
	"&lscr;":                            true,
	"&lsh;":                             true,
	"&lsim;":                            true,
	"&lsime;":                           true,
	"&lsimg;":                           true,
	"&lsqb;":                            true,
	"&lsquo;":                           true,
	"&lsquor;":                          true,
	"&lstrok;":                          true,
	"&lt":                               true,
	"&lt;":                              true,
	"&ltcc;":                            true,
	"&ltcir;":                           true,
	"&ltdot;":                           true,
	"&lthree;":                          true,
	"&ltimes;":                          true,
	"&ltlarr;":                          true,
	"&ltquest;":                         true,
	"&ltrPar;":                          true,
	"&ltri;":                            true,
	"&ltrie;":                           true,
	"&ltrif;":                           true,
	"&lurdshar;":                        true,
	"&luruhar;":                         true,
	"&lvertneqq;":                       true,
	"&lvnE;":                            true,
	"&mDDot;":                           true,
	"&macr":                             true,
	"&macr;":                            true,
	"&male;":                            true,
	"&malt;":                            true,
	"&maltese;":                         true,
	"&map;":                             true,
	"&mapsto;":                          true,
	"&mapstodown;":                      true,
	"&mapstoleft;":                      true,
	"&mapstoup;":                        true,
	"&marker;":                          true,
	"&mcomma;":                          true,
	"&mcy;":                             true,
	"&mdash;":                           true,
	"&measuredangle;":                   true,
	"&mfr;":                             true,
	"&mho;":                             true,
	"&micro":                            true,
	"&micro;":                           true,
	"&mid;":                             true,
	"&midast;":                          true,
	"&midcir;":                          true,
	"&middot":                           true,
	"&middot;":                          true,
	"&minus;":                           true,
	"&minusb;":                          true,
	"&minusd;":                          true,
	"&minusdu;":                         true,
	"&mlcp;":                            true,
	"&mldr;":                            true,
	"&mnplus;":                          true,
	"&models;":                          true,
	"&mopf;":                            true,
	"&mp;":                              true,
	"&mscr;":                            true,
	"&mstpos;":                          true,
	"&mu;":                              true,
	"&multimap;":                        true,
	"&mumap;":                           true,
	"&nGg;":                             true,
	"&nGt;":                             true,
	"&nGtv;":                            true,
	"&nLeftarrow;":                      true,
	"&nLeftrightarrow;":                 true,
	"&nLl;":                             true,
	"&nLt;":                             true,
	"&nLtv;":                            true,
	"&nRightarrow;":                     true,
	"&nVDash;":                          true,
	"&nVdash;":                          true,
	"&nabla;":                           true,
	"&nacute;":                          true,
	"&nang;":                            true,
	"&nap;":                             true,
	"&napE;":                            true,
	"&napid;":                           true,
	"&napos;":                           true,
	"&napprox;":                         true,
	"&natur;":                           true,
	"&natural;":                         true,
	"&naturals;":                        true,
	"&nbsp":                             true,
	"&nbsp;":                            true,
	"&nbump;":                           true,
	"&nbumpe;":                          true,
	"&ncap;":                            true,
	"&ncaron;":                          true,
	"&ncedil;":                          true,
	"&ncong;":                           true,
	"&ncongdot;":                        true,
	"&ncup;":                            true,
	"&ncy;":                             true,
	"&ndash;":                           true,
	"&ne;":                              true,
	"&neArr;":                           true,
	"&nearhk;":                          true,
	"&nearr;":                           true,
	"&nearrow;":                         true,
	"&nedot;":                           true,
	"&nequiv;":                          true,
	"&nesear;":                          true,
	"&nesim;":                           true,
	"&nexist;":                          true,
	"&nexists;":                         true,
	"&nfr;":                             true,
	"&ngE;":                             true,
	"&nge;":                             true,
	"&ngeq;":                            true,
	"&ngeqq;":                           true,
	"&ngeqslant;":                       true,
	"&nges;":                            true,
	"&ngsim;":                           true,
	"&ngt;":                             true,
	"&ngtr;":                            true,
	"&nhArr;":                           true,
	"&nharr;":                           true,
	"&nhpar;":                           true,
	"&ni;":                              true,
	"&nis;":                             true,
	"&nisd;":                            true,
	"&niv;":                             true,
	"&njcy;":                            true,
	"&nlArr;":                           true,
	"&nlE;":                             true,
	"&nlarr;":                           true,
	"&nldr;":                            true,
	"&nle;":                             true,
	"&nleftarrow;":                      true,
	"&nleftrightarrow;":                 true,
	"&nleq;":                            true,
	"&nleqq;":                           true,
	"&nleqslant;":                       true,
	"&nles;":                            true,
	"&nless;":                           true,
	"&nlsim;":                           true,
	"&nlt;":                             true,
	"&nltri;":                           true,
	"&nltrie;":                          true,
	"&nmid;":                            true,
	"&nopf;":                            true,
	"&not":                              true,
	"&not;":                             true,
	"&notin;":                           true,
	"&notinE;":                          true,
	"&notindot;":                        true,
	"&notinva;":                         true,
	"&notinvb;":                         true,
	"&notinvc;":                         true,
	"&notni;":                           true,
	"&notniva;":                         true,
	"&notnivb;":                         true,
	"&notnivc;":                         true,
	"&npar;":                            true,
	"&nparallel;":                       true,
	"&nparsl;":                          true,
	"&npart;":                           true,
	"&npolint;":                         true,
	"&npr;":                             true,
	"&nprcue;":                          true,
	"&npre;":                            true,
	"&nprec;":                           true,
	"&npreceq;":                         true,
	"&nrArr;":                           true,
	"&nrarr;":                           true,
	"&nrarrc;":                          true,
	"&nrarrw;":                          true,
	"&nrightarrow;":                     true,
	"&nrtri;":                           true,
	"&nrtrie;":                          true,
	"&nsc;":                             true,
	"&nsccue;":                          true,
	"&nsce;":                            true,
	"&nscr;":                            true,
	"&nshortmid;":                       true,
	"&nshortparallel;":                  true,
	"&nsim;":                            true,
	"&nsime;":                           true,
	"&nsimeq;":                          true,
	"&nsmid;":                           true,
	"&nspar;":                           true,
	"&nsqsube;":                         true,
	"&nsqsupe;":                         true,
	"&nsub;":                            true,
	"&nsubE;":                           true,
	"&nsube;":                           true,
	"&nsubset;":                         true,
	"&nsubseteq;":                       true,
	"&nsubseteqq;":                      true,
	"&nsucc;":                           true,
	"&nsucceq;":                         true,
	"&nsup;":                            true,
	"&nsupE;":                           true,
	"&nsupe;":                           true,
	"&nsupset;":                         true,
	"&nsupseteq;":                       true,
	"&nsupseteqq;":                      true,
	"&ntgl;":                            true,
	"&ntilde":                           true,
	"&ntilde;":                          true,
	"&ntlg;":                            true,
	"&ntriangleleft;":                   true,
	"&ntrianglelefteq;":                 true,
	"&ntriangleright;":                  true,
	"&ntrianglerighteq;":                true,
	"&nu;":                              true,
	"&num;":                             true,
	"&numero;":                          true,
	"&numsp;":                           true,
	"&nvDash;":                          true,
	"&nvHarr;":                          true,
	"&nvap;":                            true,
	"&nvdash;":                          true,
	"&nvge;":                            true,
	"&nvgt;":                            true,
	"&nvinfin;":                         true,
	"&nvlArr;":                          true,
	"&nvle;":                            true,
	"&nvlt;":                            true,
	"&nvltrie;":                         true,
	"&nvrArr;":                          true,
	"&nvrtrie;":                         true,
	"&nvsim;":                           true,
	"&nwArr;":                           true,
	"&nwarhk;":                          true,
	"&nwarr;":                           true,
	"&nwarrow;":                         true,
	"&nwnear;":                          true,
	"&oS;":                              true,
	"&oacute":                           true,
	"&oacute;":                          true,
	"&oast;":                            true,
	"&ocir;":                            true,
	"&ocirc":                            true,
	"&ocirc;":                           true,
	"&ocy;":                             true,
	"&odash;":                           true,
	"&odblac;":                          true,
	"&odiv;":                            true,
	"&odot;":                            true,
	"&odsold;":                          true,
	"&oelig;":                           true,
	"&ofcir;":                           true,
	"&ofr;":                             true,
	"&ogon;":                            true,
	"&ograve":                           true,
	"&ograve;":                          true,
	"&ogt;":                             true,
	"&ohbar;":                           true,
	"&ohm;":                             true,
	"&oint;":                            true,
	"&olarr;":                           true,
	"&olcir;":                           true,
	"&olcross;":                         true,
	"&oline;":                           true,
	"&olt;":                             true,
	"&omacr;":                           true,
	"&omega;":                           true,
	"&omicron;":                         true,
	"&omid;":                            true,
	"&ominus;":                          true,
	"&oopf;":                            true,
	"&opar;":                            true,
	"&operp;":                           true,
	"&oplus;":                           true,
	"&or;":                              true,
	"&orarr;":                           true,
	"&ord;":                             true,
	"&order;":                           true,
	"&orderof;":                         true,
	"&ordf":                             true,
	"&ordf;":                            true,
	"&ordm":                             true,
	"&ordm;":                            true,
	"&origof;":                          true,
	"&oror;":                            true,
	"&orslope;":                         true,
	"&orv;":                             true,
	"&oscr;":                            true,
	"&oslash":                           true,
	"&oslash;":                          true,
	"&osol;":                            true,
	"&otilde":                           true,
	"&otilde;":                          true,
	"&otimes;":                          true,
	"&otimesas;":                        true,
	"&ouml":                             true,
	"&ouml;":                            true,
	"&ovbar;":                           true,
	"&par;":                             true,
	"&para":                             true,
	"&para;":                            true,
	"&parallel;":                        true,
	"&parsim;":                          true,
	"&parsl;":                           true,
	"&part;":                            true,
	"&pcy;":                             true,
	"&percnt;":                          true,
	"&period;":                          true,
	"&permil;":                          true,
	"&perp;":                            true,
	"&pertenk;":                         true,
	"&pfr;":                             true,
	"&phi;":                             true,
	"&phiv;":                            true,
	"&phmmat;":                          true,
	"&phone;":                           true,
	"&pi;":                              true,
	"&pitchfork;":                       true,
	"&piv;":                             true,
	"&planck;":                          true,
	"&planckh;":                         true,
	"&plankv;":                          true,
	"&plus;":                            true,
	"&plusacir;":                        true,
	"&plusb;":                           true,
	"&pluscir;":                         true,
	"&plusdo;":                          true,
	"&plusdu;":                          true,
	"&pluse;":                           true,
	"&plusmn":                           true,
	"&plusmn;":                          true,
	"&plussim;":                         true,
	"&plustwo;":                         true,
	"&pm;":                              true,
	"&pointint;":                        true,
	"&popf;":                            true,
	"&pound":                            true,
	"&pound;":                           true,
	"&pr;":                              true,
	"&prE;":                             true,
	"&prap;":                            true,
	"&prcue;":                           true,
	"&pre;":                             true,
	"&prec;":                            true,
	"&precapprox;":                      true,
	"&preccurlyeq;":                     true,
	"&preceq;":                          true,
	"&precnapprox;":                     true,
	"&precneqq;":                        true,
	"&precnsim;":                        true,
	"&precsim;":                         true,
	"&prime;":                           true,
	"&primes;":                          true,
	"&prnE;":                            true,
	"&prnap;":                           true,
	"&prnsim;":                          true,
	"&prod;":                            true,
	"&profalar;":                        true,
	"&profline;":                        true,
	"&profsurf;":                        true,
	"&prop;":                            true,
	"&propto;":                          true,
	"&prsim;":                           true,
	"&prurel;":                          true,
	"&pscr;":                            true,
	"&psi;":                             true,
	"&puncsp;":                          true,
	"&qfr;":                             true,
	"&qint;":                            true,
	"&qopf;":                            true,
	"&qprime;":                          true,
	"&qscr;":                            true,
	"&quaternions;":                     true,
	"&quatint;":                         true,
	"&quest;":                           true,
	"&questeq;":                         true,
	"&quot":                             true,
	"&quot;":                            true,
	"&rAarr;":                           true,
	"&rArr;":                            true,
	"&rAtail;":                          true,
	"&rBarr;":                           true,
	"&rHar;":                            true,
	"&race;":                            true,
	"&racute;":                          true,
	"&radic;":                           true,
	"&raemptyv;":                        true,
	"&rang;":                            true,
	"&rangd;":                           true,
	"&range;":                           true,
	"&rangle;":                          true,
	"&raquo":                            true,
	"&raquo;":                           true,
	"&rarr;":                            true,
	"&rarrap;":                          true,
	"&rarrb;":                           true,
	"&rarrbfs;":                         true,
	"&rarrc;":                           true,
	"&rarrfs;":                          true,
	"&rarrhk;":                          true,
	"&rarrlp;":                          true,
	"&rarrpl;":                          true,
	"&rarrsim;":                         true,
	"&rarrtl;":                          true,
	"&rarrw;":                           true,
	"&ratail;":                          true,
	"&ratio;":                           true,
	"&rationals;":                       true,
	"&rbarr;":                           true,
	"&rbbrk;":                           true,
	"&rbrace;":                          true,
	"&rbrack;":                          true,
	"&rbrke;":                           true,
	"&rbrksld;":                         true,
	"&rbrkslu;":                         true,
	"&rcaron;":                          true,
	"&rcedil;":                          true,
	"&rceil;":                           true,
	"&rcub;":                            true,
	"&rcy;":                             true,
	"&rdca;":                            true,
	"&rdldhar;":                         true,
	"&rdquo;":                           true,
	"&rdquor;":                          true,
	"&rdsh;":                            true,
	"&real;":                            true,
	"&realine;":                         true,
	"&realpart;":                        true,
	"&reals;":                           true,
	"&rect;":                            true,
	"&reg":                              true,
	"&reg;":                             true,
	"&rfisht;":                          true,
	"&rfloor;":                          true,
	"&rfr;":                             true,
	"&rhard;":                           true,
	"&rharu;":                           true,
	"&rharul;":                          true,
	"&rho;":                             true,
	"&rhov;":                            true,
	"&rightarrow;":                      true,
	"&rightarrowtail;":                  true,
	"&rightharpoondown;":                true,
	"&rightharpoonup;":                  true,
	"&rightleftarrows;":                 true,
	"&rightleftharpoons;":               true,
	"&rightrightarrows;":                true,
	"&rightsquigarrow;":                 true,
	"&rightthreetimes;":                 true,
	"&ring;":                            true,
	"&risingdotseq;":                    true,
	"&rlarr;":                           true,
	"&rlhar;":                           true,
	"&rlm;":                             true,
	"&rmoust;":                          true,
	"&rmoustache;":                      true,
	"&rnmid;":                           true,
	"&roang;":                           true,
	"&roarr;":                           true,
	"&robrk;":                           true,
	"&ropar;":                           true,
	"&ropf;":                            true,
	"&roplus;":                          true,
	"&rotimes;":                         true,
	"&rpar;":                            true,
	"&rpargt;":                          true,
	"&rppolint;":                        true,
	"&rrarr;":                           true,
	"&rsaquo;":                          true,
	"&rscr;":                            true,
	"&rsh;":                             true,
	"&rsqb;":                            true,
	"&rsquo;":                           true,
	"&rsquor;":                          true,
	"&rthree;":                          true,
	"&rtimes;":                          true,
	"&rtri;":                            true,
	"&rtrie;":                           true,
	"&rtrif;":                           true,
	"&rtriltri;":                        true,
	"&ruluhar;":                         true,
	"&rx;":                              true,
	"&sacute;":                          true,
	"&sbquo;":                           true,
	"&sc;":                              true,
	"&scE;":                             true,
	"&scap;":                            true,
	"&scaron;":                          true,
	"&sccue;":                           true,
	"&sce;":                             true,
	"&scedil;":                          true,
	"&scirc;":                           true,
	"&scnE;":                            true,
	"&scnap;":                           true,
	"&scnsim;":                          true,
	"&scpolint;":                        true,
	"&scsim;":                           true,
	"&scy;":                             true,
	"&sdot;":                            true,
	"&sdotb;":                           true,
	"&sdote;":                           true,
	"&seArr;":                           true,
	"&searhk;":                          true,
	"&searr;":                           true,
	"&searrow;":                         true,
	"&sect":                             true,
	"&sect;":                            true,
	"&semi;":                            true,
	"&seswar;":                          true,
	"&setminus;":                        true,
	"&setmn;":                           true,
	"&sext;":                            true,
	"&sfr;":                             true,
	"&sfrown;":                          true,
	"&sharp;":                           true,
	"&shchcy;":                          true,
	"&shcy;":                            true,
	"&shortmid;":                        true,
	"&shortparallel;":                   true,
	"&shy":                              true,
	"&shy;":                             true,
	"&sigma;":                           true,
	"&sigmaf;":                          true,
	"&sigmav;":                          true,
	"&sim;":                             true,
	"&simdot;":                          true,
	"&sime;":                            true,
	"&simeq;":                           true,
	"&simg;":                            true,
	"&simgE;":                           true,
	"&siml;":                            true,
	"&simlE;":                           true,
	"&simne;":                           true,
	"&simplus;":                         true,
	"&simrarr;":                         true,
	"&slarr;":                           true,
	"&smallsetminus;":                   true,
	"&smashp;":                          true,
	"&smeparsl;":                        true,
	"&smid;":                            true,
	"&smile;":                           true,
	"&smt;":                             true,
	"&smte;":                            true,
	"&smtes;":                           true,
	"&softcy;":                          true,
	"&sol;":                             true,
	"&solb;":                            true,
	"&solbar;":                          true,
	"&sopf;":                            true,
	"&spades;":                          true,
	"&spadesuit;":                       true,
	"&spar;":                            true,
	"&sqcap;":                           true,
	"&sqcaps;":                          true,
	"&sqcup;":                           true,
	"&sqcups;":                          true,
	"&sqsub;":                           true,
	"&sqsube;":                          true,
	"&sqsubset;":                        true,
	"&sqsubseteq;":                      true,
	"&sqsup;":                           true,
	"&sqsupe;":                          true,
	"&sqsupset;":                        true,
	"&sqsupseteq;":                      true,
	"&squ;":                             true,
	"&square;":                          true,
	"&squarf;":                          true,
	"&squf;":                            true,
	"&srarr;":                           true,
	"&sscr;":                            true,
	"&ssetmn;":                          true,
	"&ssmile;":                          true,
	"&sstarf;":                          true,
	"&star;":                            true,
	"&starf;":                           true,
	"&straightepsilon;":                 true,
	"&straightphi;":                     true,
	"&strns;":                           true,
	"&sub;":                             true,
	"&subE;":                            true,
	"&subdot;":                          true,
	"&sube;":                            true,
	"&subedot;":                         true,
	"&submult;":                         true,
	"&subnE;":                           true,
	"&subne;":                           true,
	"&subplus;":                         true,
	"&subrarr;":                         true,
	"&subset;":                          true,
	"&subseteq;":                        true,
	"&subseteqq;":                       true,
	"&subsetneq;":                       true,
	"&subsetneqq;":                      true,
	"&subsim;":                          true,
	"&subsub;":                          true,
	"&subsup;":                          true,
	"&succ;":                            true,
	"&succapprox;":                      true,
	"&succcurlyeq;":                     true,
	"&succeq;":                          true,
	"&succnapprox;":                     true,
	"&succneqq;":                        true,
	"&succnsim;":                        true,
	"&succsim;":                         true,
	"&sum;":                             true,
	"&sung;":                            true,
	"&sup1":                             true,
	"&sup1;":                            true,
	"&sup2":                             true,
	"&sup2;":                            true,
	"&sup3":                             true,
	"&sup3;":                            true,
	"&sup;":                             true,
	"&supE;":                            true,
	"&supdot;":                          true,
	"&supdsub;":                         true,
	"&supe;":                            true,
	"&supedot;":                         true,
	"&suphsol;":                         true,
	"&suphsub;":                         true,
	"&suplarr;":                         true,
	"&supmult;":                         true,
	"&supnE;":                           true,
	"&supne;":                           true,
	"&supplus;":                         true,
	"&supset;":                          true,
	"&supseteq;":                        true,
	"&supseteqq;":                       true,
	"&supsetneq;":                       true,
	"&supsetneqq;":                      true,
	"&supsim;":                          true,
	"&supsub;":                          true,
	"&supsup;":                          true,
	"&swArr;":                           true,
	"&swarhk;":                          true,
	"&swarr;":                           true,
	"&swarrow;":                         true,
	"&swnwar;":                          true,
	"&szlig":                            true,
	"&szlig;":                           true,
	"&target;":                          true,
	"&tau;":                             true,
	"&tbrk;":                            true,
	"&tcaron;":                          true,
	"&tcedil;":                          true,
	"&tcy;":                             true,
	"&tdot;":                            true,
	"&telrec;":                          true,
	"&tfr;":                             true,
	"&there4;":                          true,
	"&therefore;":                       true,
	"&theta;":                           true,
	"&thetasym;":                        true,
	"&thetav;":                          true,
	"&thickapprox;":                     true,
	"&thicksim;":                        true,
	"&thinsp;":                          true,
	"&thkap;":                           true,
	"&thksim;":                          true,
	"&thorn":                            true,
	"&thorn;":                           true,
	"&tilde;":                           true,
	"&times":                            true,
	"&times;":                           true,
	"&timesb;":                          true,
	"&timesbar;":                        true,
	"&timesd;":                          true,
	"&tint;":                            true,
	"&toea;":                            true,
	"&top;":                             true,
	"&topbot;":                          true,
	"&topcir;":                          true,
	"&topf;":                            true,
	"&topfork;":                         true,
	"&tosa;":                            true,
	"&tprime;":                          true,
	"&trade;":                           true,
	"&triangle;":                        true,
	"&triangledown;":                    true,
	"&triangleleft;":                    true,
	"&trianglelefteq;":                  true,
	"&triangleq;":                       true,
	"&triangleright;":                   true,
	"&trianglerighteq;":                 true,
	"&tridot;":                          true,
	"&trie;":                            true,
	"&triminus;":                        true,
	"&triplus;":                         true,
	"&trisb;":                           true,
	"&tritime;":                         true,
	"&trpezium;":                        true,
	"&tscr;":                            true,
	"&tscy;":                            true,
	"&tshcy;":                           true,
	"&tstrok;":                          true,
	"&twixt;":                           true,
	"&twoheadleftarrow;":                true,
	"&twoheadrightarrow;":               true,
	"&uArr;":                            true,
	"&uHar;":                            true,
	"&uacute":                           true,
	"&uacute;":                          true,
	"&uarr;":                            true,
	"&ubrcy;":                           true,
	"&ubreve;":                          true,
	"&ucirc":                            true,
	"&ucirc;":                           true,
	"&ucy;":                             true,
	"&udarr;":                           true,
	"&udblac;":                          true,
	"&udhar;":                           true,
	"&ufisht;":                          true,
	"&ufr;":                             true,
	"&ugrave":                           true,
	"&ugrave;":                          true,
	"&uharl;":                           true,
	"&uharr;":                           true,
	"&uhblk;":                           true,
	"&ulcorn;":                          true,
	"&ulcorner;":                        true,
	"&ulcrop;":                          true,
	"&ultri;":                           true,
	"&umacr;":                           true,
	"&uml":                              true,
	"&uml;":                             true,
	"&uogon;":                           true,
	"&uopf;":                            true,
	"&uparrow;":                         true,
	"&updownarrow;":                     true,
	"&upharpoonleft;":                   true,
	"&upharpoonright;":                  true,
	"&uplus;":                           true,
	"&upsi;":                            true,
	"&upsih;":                           true,
	"&upsilon;":                         true,
	"&upuparrows;":                      true,
	"&urcorn;":                          true,
	"&urcorner;":                        true,
	"&urcrop;":                          true,
	"&uring;":                           true,
	"&urtri;":                           true,
	"&uscr;":                            true,
	"&utdot;":                           true,
	"&utilde;":                          true,
	"&utri;":                            true,
	"&utrif;":                           true,
	"&uuarr;":                           true,
	"&uuml":                             true,
	"&uuml;":                            true,
	"&uwangle;":                         true,
	"&vArr;":                            true,
	"&vBar;":                            true,
	"&vBarv;":                           true,
	"&vDash;":                           true,
	"&vangrt;":                          true,
	"&varepsilon;":                      true,
	"&varkappa;":                        true,
	"&varnothing;":                      true,
	"&varphi;":                          true,
	"&varpi;":                           true,
	"&varpropto;":                       true,
	"&varr;":                            true,
	"&varrho;":                          true,
	"&varsigma;":                        true,
	"&varsubsetneq;":                    true,
	"&varsubsetneqq;":                   true,
	"&varsupsetneq;":                    true,
	"&varsupsetneqq;":                   true,
	"&vartheta;":                        true,
	"&vartriangleleft;":                 true,
	"&vartriangleright;":                true,
	"&vcy;":                             true,
	"&vdash;":                           true,
	"&vee;":                             true,
	"&veebar;":                          true,
	"&veeeq;":                           true,
	"&vellip;":                          true,
	"&verbar;":                          true,
	"&vert;":                            true,
	"&vfr;":                             true,
	"&vltri;":                           true,
	"&vnsub;":                           true,
	"&vnsup;":                           true,
	"&vopf;":                            true,
	"&vprop;":                           true,
	"&vrtri;":                           true,
	"&vscr;":                            true,
	"&vsubnE;":                          true,
	"&vsubne;":                          true,
	"&vsupnE;":                          true,
	"&vsupne;":                          true,
	"&vzigzag;":                         true,
	"&wcirc;":                           true,
	"&wedbar;":                          true,
	"&wedge;":                           true,
	"&wedgeq;":                          true,
	"&weierp;":                          true,
	"&wfr;":                             true,
	"&wopf;":                            true,
	"&wp;":                              true,
	"&wr;":                              true,
	"&wreath;":                          true,
	"&wscr;":                            true,
	"&xcap;":                            true,
	"&xcirc;":                           true,
	"&xcup;":                            true,
	"&xdtri;":                           true,
	"&xfr;":                             true,
	"&xhArr;":                           true,
	"&xharr;":                           true,
	"&xi;":                              true,
	"&xlArr;":                           true,
	"&xlarr;":                           true,
	"&xmap;":                            true,
	"&xnis;":                            true,
	"&xodot;":                           true,
	"&xopf;":                            true,
	"&xoplus;":                          true,
	"&xotime;":                          true,
	"&xrArr;":                           true,
	"&xrarr;":                           true,
	"&xscr;":                            true,
	"&xsqcup;":                          true,
	"&xuplus;":                          true,
	"&xutri;":                           true,
	"&xvee;":                            true,
	"&xwedge;":                          true,
	"&yacute":                           true,
	"&yacute;":                          true,
	"&yacy;":                            true,
	"&ycirc;":                           true,
	"&ycy;":                             true,
	"&yen":                              true,
	"&yen;":                             true,
	"&yfr;":                             true,
	"&yicy;":                            true,
	"&yopf;":                            true,
	"&yscr;":                            true,
	"&yucy;":                            true,
	"&yuml":                             true,
	"&yuml;":                            true,
	"&zacute;":                          true,
	"&zcaron;":                          true,
	"&zcy;":                             true,
	"&zdot;":                            true,
	"&zeetrf;":                          true,
	"&zeta;":                            true,
	"&zfr;":                             true,
	"&zhcy;":                            true,
	"&zigrarr;":                         true,
	"&zopf;":                            true,
	"&zscr;":                            true,
	"&zwj;":                             true,
	"&zwnj;":                            true,
}
