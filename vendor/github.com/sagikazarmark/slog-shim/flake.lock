{"nodes": {"devenv": {"inputs": {"flake-compat": "flake-compat", "nix": "nix", "nixpkgs": "nixpkgs", "pre-commit-hooks": "pre-commit-hooks"}, "locked": {"lastModified": 1694097209, "narHash": "sha256-gQmBjjxeSyySjbh0yQVBKApo2KWIFqqbRUvG+Fa+QpM=", "owner": "cachix", "repo": "devenv", "rev": "7a8e6a91510efe89d8dcb8e43233f93e86f6b189", "type": "github"}, "original": {"owner": "cachix", "repo": "devenv", "type": "github"}}, "flake-compat": {"flake": false, "locked": {"lastModified": 1673956053, "narHash": "sha256-4gtG9iQuiKITOjNQQeQIpoIB6b16fm+504Ch3sNKLd8=", "owner": "edols<PERSON>", "repo": "flake-compat", "rev": "35bb57c0c8d8b62bbfd284272c928ceb64ddbde9", "type": "github"}, "original": {"owner": "edols<PERSON>", "repo": "flake-compat", "type": "github"}}, "flake-parts": {"inputs": {"nixpkgs-lib": "nixpkgs-lib"}, "locked": {"lastModified": 1693611461, "narHash": "sha256-aPODl8vAgGQ0ZYFIRisxYG5MOGSkIczvu2Cd8Gb9+1Y=", "owner": "hercules-ci", "repo": "flake-parts", "rev": "7f53fdb7bdc5bb237da7fefef12d099e4fd611ca", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "flake-parts", "type": "github"}}, "flake-utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1685518550, "narHash": "sha256-o2d0KcvaXzTrPRIo0kOLV0/QXHhDQ5DTi+OxcjO8xqY=", "owner": "numtide", "repo": "flake-utils", "rev": "a1720a10a6cfe8234c0e93907ffe81be440f4cef", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "gitignore": {"inputs": {"nixpkgs": ["devenv", "pre-commit-hooks", "nixpkgs"]}, "locked": {"lastModified": 1660459072, "narHash": "sha256-8DFJjXG8zqoONA1vXtgeKXy68KdJL5UaXR8NtVMUbx8=", "owner": "hercules-ci", "repo": "gitignore.nix", "rev": "a20de23b925fd8264fd7fad6454652e142fd7f73", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "gitignore.nix", "type": "github"}}, "lowdown-src": {"flake": false, "locked": {"lastModified": 1633514407, "narHash": "sha256-<PERSON>w32tiMjdK9t3ETl5fzGrutQTzh2rufgZV4A/BbxuD4=", "owner": "kristapsdz", "repo": "lowdown", "rev": "d2c2b44ff6c27b936ec27358a2653caaef8f73b8", "type": "github"}, "original": {"owner": "kristapsdz", "repo": "lowdown", "type": "github"}}, "nix": {"inputs": {"lowdown-src": "lowdown-src", "nixpkgs": ["devenv", "nixpkgs"], "nixpkgs-regression": "nixpkgs-regression"}, "locked": {"lastModified": 1676545802, "narHash": "sha256-EK4rZ+Hd5hsvXnzSzk2ikhStJnD63odF7SzsQ8CuSPU=", "owner": "<PERSON><PERSON><PERSON>", "repo": "nix", "rev": "7c91803598ffbcfe4a55c44ac6d49b2cf07a527f", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON>", "ref": "relaxed-flakes", "repo": "nix", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1678875422, "narHash": "sha256-T3o6NcQPwXjxJMn2shz86Chch4ljXgZn746c2caGxd8=", "owner": "NixOS", "repo": "nixpkgs", "rev": "126f49a01de5b7e35a43fd43f891ecf6d3a51459", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixpkgs-unstable", "repo": "nixpkgs", "type": "github"}}, "nixpkgs-lib": {"locked": {"dir": "lib", "lastModified": 1693471703, "narHash": "sha256-0l03ZBL8P1P6z8MaSDS/MvuU8E75rVxe5eE1N6gxeTo=", "owner": "NixOS", "repo": "nixpkgs", "rev": "3e52e76b70d5508f3cec70b882a29199f4d1ee85", "type": "github"}, "original": {"dir": "lib", "owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "nixpkgs-regression": {"locked": {"lastModified": 1643052045, "narHash": "sha256-uGJ0VXIhWKGXxkeNnq4TvV3CIOkUJ3PAoLZ3HMzNVMw=", "owner": "NixOS", "repo": "nixpkgs", "rev": "215d4d0fd80ca5163643b03a33fde804a29cc1e2", "type": "github"}, "original": {"owner": "NixOS", "repo": "nixpkgs", "rev": "215d4d0fd80ca5163643b03a33fde804a29cc1e2", "type": "github"}}, "nixpkgs-stable": {"locked": {"lastModified": 1685801374, "narHash": "sha256-otaSUoFEMM+LjBI1XL/xGB5ao6IwnZOXc47qhIgJe8U=", "owner": "NixOS", "repo": "nixpkgs", "rev": "c37ca420157f4abc31e26f436c1145f8951ff373", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-23.05", "repo": "nixpkgs", "type": "github"}}, "nixpkgs_2": {"locked": {"lastModified": 1694345580, "narHash": "sha256-BbG0NUxQTz1dN/Y87yPWZc/0Kp/coJ0vM3+7sNa5kUM=", "owner": "NixOS", "repo": "nixpkgs", "rev": "f002de6834fdde9c864f33c1ec51da7df19cd832", "type": "github"}, "original": {"owner": "NixOS", "ref": "master", "repo": "nixpkgs", "type": "github"}}, "pre-commit-hooks": {"inputs": {"flake-compat": ["devenv", "flake-compat"], "flake-utils": "flake-utils", "gitignore": "gitignore", "nixpkgs": ["devenv", "nixpkgs"], "nixpkgs-stable": "nixpkgs-stable"}, "locked": {"lastModified": 1688056373, "narHash": "sha256-2+SDlNRTKsgo3LBRiMUcoEUb6sDViRNQhzJquZ4koOI=", "owner": "cachix", "repo": "pre-commit-hooks.nix", "rev": "5843cf069272d92b60c3ed9e55b7a8989c01d4c7", "type": "github"}, "original": {"owner": "cachix", "repo": "pre-commit-hooks.nix", "type": "github"}}, "root": {"inputs": {"devenv": "devenv", "flake-parts": "flake-parts", "nixpkgs": "nixpkgs_2"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}}, "root": "root", "version": 7}