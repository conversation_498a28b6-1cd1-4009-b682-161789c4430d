run:
  timeout: 10m

linters-settings:
  gci:
    sections:
      - standard
      - default
      - prefix(github.com/sagikazarmark/locafero)
  goimports:
    local-prefixes: github.com/sagikazarmark/locafero
  misspell:
    locale: US
  nolintlint:
    allow-leading-space: false # require machine-readable nolint directives (with no leading space)
    allow-unused: false # report any unused nolint directives
    require-specific: false # don't require nolint directives to be specific about which linter is being skipped
  revive:
    confidence: 0

linters:
  enable:
    - gci
    - goimports
    - misspell
    - nolintlint
    - revive
