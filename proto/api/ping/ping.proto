syntax = "proto3";

package ping;

option go_package = "github.com/howge/bid-gateway/ping";

import "google/api/annotations.proto";


//健康检查服务
service  Checker {
    rpc  Ping(PingRequest) returns (PingReply){
        option (google.api.http) = {
            get: "/bidserver/ping"
        };
    }
    
}

// 定义请求message

message PingRequest {
    // string message = 1;
}


// 响应message

message PingReply {
    string message = 1;
}