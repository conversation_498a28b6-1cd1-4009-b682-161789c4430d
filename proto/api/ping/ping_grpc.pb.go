// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: api/ping/ping.proto

package ping

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Checker_Ping_FullMethodName = "/ping.Checker/Ping"
)

// CheckerClient is the client API for Checker service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CheckerClient interface {
	Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PingReply, error)
}

type checkerClient struct {
	cc grpc.ClientConnInterface
}

func NewCheckerClient(cc grpc.ClientConnInterface) CheckerClient {
	return &checkerClient{cc}
}

func (c *checkerClient) Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PingReply, error) {
	out := new(PingReply)
	err := c.cc.Invoke(ctx, Checker_Ping_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CheckerServer is the server API for Checker service.
// All implementations must embed UnimplementedCheckerServer
// for forward compatibility
type CheckerServer interface {
	Ping(context.Context, *PingRequest) (*PingReply, error)
	mustEmbedUnimplementedCheckerServer()
}

// UnimplementedCheckerServer must be embedded to have forward compatible implementations.
type UnimplementedCheckerServer struct {
}

func (UnimplementedCheckerServer) Ping(context.Context, *PingRequest) (*PingReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (UnimplementedCheckerServer) mustEmbedUnimplementedCheckerServer() {}

// UnsafeCheckerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CheckerServer will
// result in compilation errors.
type UnsafeCheckerServer interface {
	mustEmbedUnimplementedCheckerServer()
}

func RegisterCheckerServer(s grpc.ServiceRegistrar, srv CheckerServer) {
	s.RegisterService(&Checker_ServiceDesc, srv)
}

func _Checker_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckerServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checker_Ping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckerServer).Ping(ctx, req.(*PingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Checker_ServiceDesc is the grpc.ServiceDesc for Checker service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Checker_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ping.Checker",
	HandlerType: (*CheckerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Ping",
			Handler:    _Checker_Ping_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/ping/ping.proto",
}
