syntax = "proto3";

package helloworld;

option go_package = "github.com/howge/bid-gateway/hello";

import "google/api/annotations.proto";


//定义一个服务
service  Greeter {
    rpc  SayHello(HelloRequest) returns (HelloReply){
        option (google.api.http) = {
            post: "/v1/example/echo"
            body: "*"
        };
    }
    
}

// 定义请求message

message HelloRequest {
    string name = 1;
}


// 响应message

message HelloReply {
    string message = 1;
}