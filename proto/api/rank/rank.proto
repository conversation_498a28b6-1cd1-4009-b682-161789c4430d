syntax = "proto3";

package rank;

option go_package = "github.com/howge/bid-gateway/rank";

import "google/api/annotations.proto";
// import "google/api/any.proto";


// 定义服务
service  Ranker {
    rpc QueryRank(RankRequest) returns (RankReply){
        option (google.api.http) = {
            post: "/bidserver/rank"
            body: "*"
        };
    }
    
}


//请求
message  Item {
    map<string, string> map = 1;
    float pctr = 2;
    float pcvr = 3;
    float cpc = 4;
    float cpa = 5;
    float ecpm = 6;
    float ocpc = 7;
    float ocpm = 8;
}

message Map {
    repeated Item recall = 1;
}

message Userinfo {
    string trackingId = 1;
    string version = 2;
    string uuid = 3;
    map<string, string> userMap = 4;
    map<string, string> contextMap = 5;
}

message RankRequest {
    Map map = 1;
    Userinfo userInfo = 2;
}

// 响应
message RankReply {
    repeated Item  data = 1;
}