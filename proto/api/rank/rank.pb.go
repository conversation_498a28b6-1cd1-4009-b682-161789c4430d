// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: api/rank/rank.proto

package rank

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 请求
type Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Map  map[string]string `protobuf:"bytes,1,rep,name=map,proto3" json:"map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Pctr float32           `protobuf:"fixed32,2,opt,name=pctr,proto3" json:"pctr,omitempty"`
	Pcvr float32           `protobuf:"fixed32,3,opt,name=pcvr,proto3" json:"pcvr,omitempty"`
	Cpc  float32           `protobuf:"fixed32,4,opt,name=cpc,proto3" json:"cpc,omitempty"`
	Cpa  float32           `protobuf:"fixed32,5,opt,name=cpa,proto3" json:"cpa,omitempty"`
	Ecpm float32           `protobuf:"fixed32,6,opt,name=ecpm,proto3" json:"ecpm,omitempty"`
	Ocpc float32           `protobuf:"fixed32,7,opt,name=ocpc,proto3" json:"ocpc,omitempty"`
	Ocpm float32           `protobuf:"fixed32,8,opt,name=ocpm,proto3" json:"ocpm,omitempty"`
}

func (x *Item) Reset() {
	*x = Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rank_rank_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Item) ProtoMessage() {}

func (x *Item) ProtoReflect() protoreflect.Message {
	mi := &file_api_rank_rank_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Item.ProtoReflect.Descriptor instead.
func (*Item) Descriptor() ([]byte, []int) {
	return file_api_rank_rank_proto_rawDescGZIP(), []int{0}
}

func (x *Item) GetMap() map[string]string {
	if x != nil {
		return x.Map
	}
	return nil
}

func (x *Item) GetPctr() float32 {
	if x != nil {
		return x.Pctr
	}
	return 0
}

func (x *Item) GetPcvr() float32 {
	if x != nil {
		return x.Pcvr
	}
	return 0
}

func (x *Item) GetCpc() float32 {
	if x != nil {
		return x.Cpc
	}
	return 0
}

func (x *Item) GetCpa() float32 {
	if x != nil {
		return x.Cpa
	}
	return 0
}

func (x *Item) GetEcpm() float32 {
	if x != nil {
		return x.Ecpm
	}
	return 0
}

func (x *Item) GetOcpc() float32 {
	if x != nil {
		return x.Ocpc
	}
	return 0
}

func (x *Item) GetOcpm() float32 {
	if x != nil {
		return x.Ocpm
	}
	return 0
}

type Map struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Recall []*Item `protobuf:"bytes,1,rep,name=recall,proto3" json:"recall,omitempty"`
}

func (x *Map) Reset() {
	*x = Map{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rank_rank_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Map) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Map) ProtoMessage() {}

func (x *Map) ProtoReflect() protoreflect.Message {
	mi := &file_api_rank_rank_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Map.ProtoReflect.Descriptor instead.
func (*Map) Descriptor() ([]byte, []int) {
	return file_api_rank_rank_proto_rawDescGZIP(), []int{1}
}

func (x *Map) GetRecall() []*Item {
	if x != nil {
		return x.Recall
	}
	return nil
}

type Userinfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrackingId string            `protobuf:"bytes,1,opt,name=trackingId,proto3" json:"trackingId,omitempty"`
	Version    string            `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Uuid       string            `protobuf:"bytes,3,opt,name=uuid,proto3" json:"uuid,omitempty"`
	UserMap    map[string]string `protobuf:"bytes,4,rep,name=userMap,proto3" json:"userMap,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ContextMap map[string]string `protobuf:"bytes,5,rep,name=contextMap,proto3" json:"contextMap,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Userinfo) Reset() {
	*x = Userinfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rank_rank_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Userinfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Userinfo) ProtoMessage() {}

func (x *Userinfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_rank_rank_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Userinfo.ProtoReflect.Descriptor instead.
func (*Userinfo) Descriptor() ([]byte, []int) {
	return file_api_rank_rank_proto_rawDescGZIP(), []int{2}
}

func (x *Userinfo) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

func (x *Userinfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Userinfo) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *Userinfo) GetUserMap() map[string]string {
	if x != nil {
		return x.UserMap
	}
	return nil
}

func (x *Userinfo) GetContextMap() map[string]string {
	if x != nil {
		return x.ContextMap
	}
	return nil
}

type RankRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Map      *Map      `protobuf:"bytes,1,opt,name=map,proto3" json:"map,omitempty"`
	UserInfo *Userinfo `protobuf:"bytes,2,opt,name=userInfo,proto3" json:"userInfo,omitempty"`
}

func (x *RankRequest) Reset() {
	*x = RankRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rank_rank_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RankRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankRequest) ProtoMessage() {}

func (x *RankRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rank_rank_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankRequest.ProtoReflect.Descriptor instead.
func (*RankRequest) Descriptor() ([]byte, []int) {
	return file_api_rank_rank_proto_rawDescGZIP(), []int{3}
}

func (x *RankRequest) GetMap() *Map {
	if x != nil {
		return x.Map
	}
	return nil
}

func (x *RankRequest) GetUserInfo() *Userinfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

// 响应
type RankReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*Item `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *RankReply) Reset() {
	*x = RankReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rank_rank_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RankReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankReply) ProtoMessage() {}

func (x *RankReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_rank_rank_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankReply.ProtoReflect.Descriptor instead.
func (*RankReply) Descriptor() ([]byte, []int) {
	return file_api_rank_rank_proto_rawDescGZIP(), []int{4}
}

func (x *RankReply) GetData() []*Item {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_api_rank_rank_proto protoreflect.FileDescriptor

var file_api_rank_rank_proto_rawDesc = []byte{
	0x0a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xed, 0x01, 0x0a, 0x04, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x25, 0x0a, 0x03, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x03, 0x6d, 0x61, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x63, 0x74,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x70, 0x63, 0x74, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x63, 0x76, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x70, 0x63, 0x76,
	0x72, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03,
	0x63, 0x70, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x03, 0x63, 0x70, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x63, 0x70, 0x6d, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x04, 0x65, 0x63, 0x70, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x63, 0x70,
	0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x6f, 0x63, 0x70, 0x63, 0x12, 0x12, 0x0a,
	0x04, 0x6f, 0x63, 0x70, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x6f, 0x63, 0x70,
	0x6d, 0x1a, 0x36, 0x0a, 0x08, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x29, 0x0a, 0x03, 0x4d, 0x61, 0x70,
	0x12, 0x22, 0x0a, 0x06, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0a, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x06, 0x72, 0x65,
	0x63, 0x61, 0x6c, 0x6c, 0x22, 0xca, 0x02, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x69, 0x6e, 0x66,
	0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12,
	0x35, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x4d, 0x61, 0x70, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x4d, 0x61, 0x70, 0x12, 0x3e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x4d, 0x61, 0x70, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x61, 0x6e,
	0x6b, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x4d, 0x61, 0x70, 0x1a, 0x3a, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x3d, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x56, 0x0a, 0x0b, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1b, 0x0a, 0x03, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x09, 0x2e,
	0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x4d, 0x61, 0x70, 0x52, 0x03, 0x6d, 0x61, 0x70, 0x12, 0x2a, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2b, 0x0a, 0x09, 0x52, 0x61, 0x6e,
	0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0x55, 0x0a, 0x06, 0x52, 0x61, 0x6e, 0x6b, 0x65, 0x72,
	0x12, 0x4b, 0x0a, 0x09, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x11, 0x2e,
	0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x0f, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x22, 0x0f, 0x2f, 0x62, 0x69, 0x64, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x3a, 0x01, 0x2a, 0x42, 0x23, 0x5a,
	0x21, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x68, 0x6f, 0x77, 0x67,
	0x65, 0x2f, 0x62, 0x69, 0x64, 0x2d, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x72, 0x61,
	0x6e, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_rank_rank_proto_rawDescOnce sync.Once
	file_api_rank_rank_proto_rawDescData = file_api_rank_rank_proto_rawDesc
)

func file_api_rank_rank_proto_rawDescGZIP() []byte {
	file_api_rank_rank_proto_rawDescOnce.Do(func() {
		file_api_rank_rank_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_rank_rank_proto_rawDescData)
	})
	return file_api_rank_rank_proto_rawDescData
}

var file_api_rank_rank_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_rank_rank_proto_goTypes = []interface{}{
	(*Item)(nil),        // 0: rank.Item
	(*Map)(nil),         // 1: rank.Map
	(*Userinfo)(nil),    // 2: rank.Userinfo
	(*RankRequest)(nil), // 3: rank.RankRequest
	(*RankReply)(nil),   // 4: rank.RankReply
	nil,                 // 5: rank.Item.MapEntry
	nil,                 // 6: rank.Userinfo.UserMapEntry
	nil,                 // 7: rank.Userinfo.ContextMapEntry
}
var file_api_rank_rank_proto_depIdxs = []int32{
	5, // 0: rank.Item.map:type_name -> rank.Item.MapEntry
	0, // 1: rank.Map.recall:type_name -> rank.Item
	6, // 2: rank.Userinfo.userMap:type_name -> rank.Userinfo.UserMapEntry
	7, // 3: rank.Userinfo.contextMap:type_name -> rank.Userinfo.ContextMapEntry
	1, // 4: rank.RankRequest.map:type_name -> rank.Map
	2, // 5: rank.RankRequest.userInfo:type_name -> rank.Userinfo
	0, // 6: rank.RankReply.data:type_name -> rank.Item
	3, // 7: rank.Ranker.QueryRank:input_type -> rank.RankRequest
	4, // 8: rank.Ranker.QueryRank:output_type -> rank.RankReply
	8, // [8:9] is the sub-list for method output_type
	7, // [7:8] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_rank_rank_proto_init() }
func file_api_rank_rank_proto_init() {
	if File_api_rank_rank_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_rank_rank_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rank_rank_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Map); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rank_rank_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Userinfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rank_rank_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RankRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rank_rank_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RankReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_rank_rank_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_rank_rank_proto_goTypes,
		DependencyIndexes: file_api_rank_rank_proto_depIdxs,
		MessageInfos:      file_api_rank_rank_proto_msgTypes,
	}.Build()
	File_api_rank_rank_proto = out.File
	file_api_rank_rank_proto_rawDesc = nil
	file_api_rank_rank_proto_goTypes = nil
	file_api_rank_rank_proto_depIdxs = nil
}
