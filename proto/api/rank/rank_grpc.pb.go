// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: api/rank/rank.proto

package rank

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Ranker_QueryRank_FullMethodName = "/rank.Ranker/QueryRank"
)

// RankerClient is the client API for Ranker service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RankerClient interface {
	QueryRank(ctx context.Context, in *RankRequest, opts ...grpc.CallOption) (*RankReply, error)
}

type rankerClient struct {
	cc grpc.ClientConnInterface
}

func NewRankerClient(cc grpc.ClientConnInterface) RankerClient {
	return &rankerClient{cc}
}

func (c *rankerClient) QueryRank(ctx context.Context, in *RankRequest, opts ...grpc.CallOption) (*RankReply, error) {
	out := new(RankReply)
	err := c.cc.Invoke(ctx, Ranker_QueryRank_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RankerServer is the server API for Ranker service.
// All implementations must embed UnimplementedRankerServer
// for forward compatibility
type RankerServer interface {
	QueryRank(context.Context, *RankRequest) (*RankReply, error)
	mustEmbedUnimplementedRankerServer()
}

// UnimplementedRankerServer must be embedded to have forward compatible implementations.
type UnimplementedRankerServer struct {
}

func (UnimplementedRankerServer) QueryRank(context.Context, *RankRequest) (*RankReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRank not implemented")
}
func (UnimplementedRankerServer) mustEmbedUnimplementedRankerServer() {}

// UnsafeRankerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RankerServer will
// result in compilation errors.
type UnsafeRankerServer interface {
	mustEmbedUnimplementedRankerServer()
}

func RegisterRankerServer(s grpc.ServiceRegistrar, srv RankerServer) {
	s.RegisterService(&Ranker_ServiceDesc, srv)
}

func _Ranker_QueryRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RankRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankerServer).QueryRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ranker_QueryRank_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankerServer).QueryRank(ctx, req.(*RankRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Ranker_ServiceDesc is the grpc.ServiceDesc for Ranker service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Ranker_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "rank.Ranker",
	HandlerType: (*RankerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryRank",
			Handler:    _Ranker_QueryRank_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/rank/rank.proto",
}
