# 简洁特征配置文件 - 每个特征一行
# 格式: 特征名, slot_id, 特征域, 类型, embedding_dim, 交叉特征, 描述

# 特征配置文件 - 包含特征域和slot_id定义
# Feature ID结构说明:
# feature_id = (slot_id << 48) | feature_value
# - 前16位(bit 48-63): slot_id (0-65535)
# - 后48位(bit 0-47): feature_value (0-281474976710655)
# slot_id范围分配:
#   1-999: 用户特征 (user)
#   1000-1499: 上下文特征 (context)
#   1500-1599: 行为特征 (behavior)
#   1600-1699: 物品特征 (item)
#   1700-1799: 交叉特征 (cross)
#   1800-1899: 序列特征 (sequence)

# 特征类型定义
types:
  INT64: {dtype: tf.int64, default: 0}
  FLOAT: {dtype: tf.float32, default: 0.0}
  STRING: {dtype: tf.string, default: "-1024"}

# 标签定义 - 模型输出，不需要slot_id和field
labels:
  win_label: {type: INT32, desc: "是否竞胜"}
  imp_label: {type: INT32, desc: "是否曝光"}

extra_labels:
  billing_rate: {type: FLOAT, desc: "billing_rate"}

# raw_win_price 媒体收adx钱
# price  adx收dsp钱
# bid adx给媒体报价
# dsp_bid,dsp给adx的报价
# win_price   竞价成功的钱。
extras:
  search_id: {type: STRING, desc: "检索标识"}
  win_price: {type: INT64, desc: "竞胜价格"}
  imp_price: {type: INT64, desc: "曝光价格"}
  bid: {type: INT64, desc: "出价 单位分"}
  dsp_bid: {type: INT64, desc: "客户出价"}
  raw_win_price: {type: INT64, desc: "原始竞胜价格"}
  
features:
  # 用户特征 (slot_id: 1-999)
  dsp_id: {slot_id: 1, field: user, type: INT64, desc: "DSP_ID"}
  exchange_id: {slot_id: 2, field: user, type: INT64, desc: "交易所ID"}
  week_day: {slot_id: 3, field: user, type: INT64, desc: "星期几"}
  hour: {slot_id: 4, field: user, type: INT64, desc: "小时"}
  inventory_type: {slot_id: 5, field: user, type: INT64, desc: "广告位类型"}
  adp_id: {slot_id: 6, field: user, type: INT64, desc: "广告位ID"}
  client_ip: {slot_id: 7, field: user, type: INT64, desc: "客户端IP"}
  adp_dim: {slot_id: 8, field: user, type: INT64, desc: "广告位尺寸"}
  bidfloor: {slot_id: 9, field: user, type: INT64, desc: "RTB地板价 单位:百万分之1元/千次展示"}
  sponsor_id: {slot_id: 10, field: user, type: INT64, desc: "广告主ID"}
  campaign_id: {slot_id: 11, field: user, type: INT64, desc: "活动ID"}
  strategy_id: {slot_id: 12, field: user, type: INT64, desc: "策略ID"}
  creative_id: {slot_id: 13, field: user, type: INT64, desc: "创意ID"}
  dsp_advertiser_id: {slot_id: 14, field: user, type: INT64, desc: "dsp返回的广告主ID 目前仅美团有"}
  dsp_creative_id: {slot_id: 15, field: user, type: INT64, desc: "dsp返回的创意ID"}
  creative_type: {slot_id: 16, field: user, type: INT64, desc: "创意类型"}
  media_bid_type: {slot_id: 17, field: user, type: INT64, desc: "媒体侧竞价类型"}
  # idfa: {slot_id: 18, field: user, type: INT64, desc: "ios唯一标识"}
  # idfa_md5: {slot_id: 19, field: user, type: INT64, desc: "苹果设备idfa md5加密"}
  # imei: {slot_id: 20, field: user, type: INT64, desc: "imei 原值"}
  # imei_md5: {slot_id: 21, field: user, type: INT64, desc: "imei md5 加密"}
  # oaid: {slot_id: 22, field: user, type: INT64, desc: "oaid唯一标识"}
  # oaid_md5: {slot_id: 23, field: user, type: INT64, desc: "oaid md5加密"}
  dm_platform: {slot_id: 24, field: user, type: INT64, desc: "多盟 设备平台类型：1 Android, 2 iOS, 0 未知"}
  template_id: {slot_id: 25, field: user, type: INT64, desc: "DSP广告位ID"}
  app_bundle: {slot_id: 26, field: user, type: INT64, desc: "媒体包名"}
  ad_source: {slot_id: 27, field: user, type: INT64, desc: "项目来源"}
  province: {slot_id: 28, field: user, type: INT64, desc: "省名称"}
  city: {slot_id: 29, field: user, type: INT64, desc: "市名称"}
  standard_make: {slot_id: 30, field: user, type: INT64, desc: "设备标准品牌"}
  dev_make: {slot_id: 31, field: user, type: INT64, desc: "设备制造商"}
  dev_model: {slot_id: 32, field: user, type: INT64, desc: "设备型号"}
  exp_id: {slot_id: 33, field: user, type: INT64, desc: "实验组ID"}
  dm_media_id: {slot_id: 34, field: user, type: INT64, desc: "多盟媒体ID"}
  absolute_pos: {slot_id: 35, field: user, type: INT64, desc: "信息流绝对位置"}
  dsp_ad_slot: {slot_id: 36, field: user, type: INT64, desc: "dsp广告位"}
  api_version: {slot_id: 37, field: user, type: INT64, desc: "api版本"}
  tanx_task_id: {slot_id: 38, field: user, type: INT64, desc: "tanx任务id"}
  tanx_group_id: {slot_id: 39, field: user, type: INT64, desc: "tanx一级组id"}
  tanx_ad_id: {slot_id: 40, field: user, type: INT64, desc: "tanx二级广告id"}
  client_ipv6: {slot_id: 41, field: user, type: INT64, desc: "ipv6"}
  country: {slot_id: 42, field: user, type: INT64, desc: "国家"}
  domob_bidfloor: {slot_id: 43, field: user, type: INT64, desc: "多盟底价"}
  cat_id: {slot_id: 44, field: user, type: INT64, desc: "品类id"}
  # req_id: {slot_id: 45, field: user, type: INT64, desc: "媒体请求id"}
  bid_id: {slot_id: 46, field: user, type: INT64, desc: "客户出价id"}
  schain: {slot_id: 47, field: user, type: INT64, desc: "流量来源"}
  surge_score: {slot_id: 48, field: user, type: INT64, desc: "启航质量分"}
  dsp_cost_mod: {slot_id: 49, field: user, type: INT64, desc: "客户结算类型"}
  # caid: {slot_id: 50, field: user, type: INT64, desc: "caid"}
  budget_type_v1: {slot_id: 51, field: user, type: INT64, desc: "预算类型"}
  app_name: {slot_id: 52, field: user, type: INT64, desc: "投放的项目应用名"}
  app_package_name: {slot_id: 53, field: user, type: INT64, desc: "项目包名"}
  # bid_ts: {slot_id: 54, field: user, type: INT64, desc: "出价时间"}
  # profit_rate: {slot_id: 55, field: user, type: INT64, desc: "利润率"}
  # profit_flag: {slot_id: 56, field: user, type: INT64, desc: "利润率优化策略"}
  # est_ctcvr: {slot_id: 57, field: user, type: INT64, desc: "cvr"}
  # budget_type: {slot_id: 58, field: user, type: INT64, desc: "预算类型"}
  # interval_bid: {slot_id: 59, field: user, type: INT64, desc: "出价区间key"}
  # exp_bid: {slot_id: 60, field: user, type: INT64, desc: "模型出价/正常出价"}

# DSP 出价相关特征（单独分类）
dsp_bid:
  field: user
  type: INT64
  desc: "客户出价"

//这个yaml里面的。
features下面的字段是特征。 然后加一个dsp_bid
//你线上一这个yaml为准。
//后面大的分类就是
features
user_featues
这样。