# intput0特征预测测试说明

## 概述

这个项目已经修改为支持简单的 `intput0` 数值输入预测，对应您提供的JSON格式：
```json
{
  "inputs": {
    "intput0": [13792273858822192]
  },
  "model_spec": {
    "name": "model",
    "signature_name": "serving_default"
  }
}
```

## 编译步骤

### 1. 编译Linux版本
```bash
# 在项目根目录执行
./build_linux.sh
```

编译完成后会在 `build/linux/` 目录下生成：
- `simple-predict`: 简单预测测试工具
- `bid-gateway`: 主服务程序
- `config/`: 配置文件目录

### 2. 上传到Linux服务器
将整个 `build/linux/` 目录上传到您的Linux服务器。

## 测试步骤

### 前提条件
确保您的Linux服务器上：
1. TensorFlow Serving正在运行，监听8500端口
2. 模型已经加载到TensorFlow Serving中

### 基本测试命令
```bash
# 进入上传的目录
cd build/linux

# 给执行权限
chmod +x simple-predict

# 基本测试（使用默认参数）
./simple-predict

# 指定参数测试
./simple-predict -input=13792273858822192 -model=model -endpoint=localhost:8500
```

### 参数说明
- `-input`: intput0的输入值（int64类型）
- `-model`: 模型名称
- `-endpoint`: TensorFlow Serving的地址和端口
- `-config`: 配置文件路径（可选）

### 测试示例

#### 示例1：使用默认参数
```bash
./simple-predict
```

#### 示例2：自定义输入值
```bash
./simple-predict -input=999999999999999
```

#### 示例3：指定远程TFS服务器
```bash
./simple-predict -input=13792273858822192 -model=my_model -endpoint=*************:8500
```

#### 示例4：完整参数
```bash
./simple-predict \
  -input=13792273858822192 \
  -model=model \
  -endpoint=localhost:8500 \
  -config=config/prod.yaml
```

## 预期输出

成功运行时，您应该看到类似以下的输出：

```
=== intput0特征预测测试 ===
配置文件: config/prod.yaml
输入值: 13792273858822192
模型名: model
TFS端点: localhost:8500
========================

发送intput0预测请求:
  输入值: 13792273858822192
  模型名: model
  TFS端点: localhost:8500
  对应JSON: {"inputs": {"intput0": [13792273858822192]}, "model_spec": {"name": "model", "signature_name": "serving_default"}}

预测结果:
  返回项目数: 1
  项目 1:
    Pctr: 0.123456
    Pcvr: 0.000000
    Ecpm: 0.000000
    Map: map[input_value:13792273858822192 model_name:model output_name:output]
    ---

=== 测试完成 ===
```

## 故障排除

### 1. 连接失败
如果看到连接错误：
```
tfsering connection error: localhost:8500
```
检查：
- TensorFlow Serving是否正在运行
- 端口8500是否正确
- 网络连接是否正常

### 2. 模型不存在
如果看到模型错误，检查：
- 模型名称是否正确
- 模型是否已加载到TensorFlow Serving

### 3. 权限问题
如果无法执行：
```bash
chmod +x simple-predict
```

## 代码说明

### 核心修改
1. **新增 `DoSimplePredict` 函数** (`internal/tfs/predict.go`)
   - 专门处理 `intput0` 输入
   - 构造int64类型的TensorProto
   - 使用 `serving_default` 签名

2. **新增 `QuerySimpleRank` 方法** (`service/rank.go`)
   - 处理简单预测请求
   - 解析多种输出类型（float, double, int64）
   - 详细的日志记录

3. **简单预测工具** (`cmd/simple_predict/main.go`)
   - 命令行参数支持
   - 完整的测试流程
   - 详细的输出显示

### 与原有代码的区别
- 原有代码：复杂的TFRecord特征工程
- 新代码：简单的数值输入，直接构造TensorProto
- 保持：gRPC客户端连接和基础架构不变

## 下一步
测试成功后，您可以：
1. 集成到现有的服务中
2. 根据实际模型输出调整结果处理逻辑
3. 添加更多的输入特征支持
