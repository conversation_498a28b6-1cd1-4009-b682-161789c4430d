package service

import (
	"bid-gateway/internal/tfs"
	"bid-gateway/proto/api/rank"
	"context"
	"errors"
	"time"

	log "github.com/sirupsen/logrus"
)

type RankService struct {
	rank.UnimplementedRankerServer
}

func NewRankService() *RankService {
	return &RankService{}
}

func (rs *RankService) QueryRank(ctx context.Context, in *rank.RankRequest) (*rank.RankReply, error) {
	// 预处理请求 参看data.json
	var items []*rank.Item

	m := in.GetMap()
	u := in.GetUserInfo()

	//请求不存在recall
	if m == nil || m.GetRecall() == nil || len(m.Recall) == 0 {
		items = append(items, &rank.Item{
			Map:  map[string]string{},
			Pctr: 0.0,
			Pcvr: 0.0,
			Cpc:  0.0,
			Cpa:  0.0,
			Ocpc: 0.0,
			Ocpm: 0.0,
		})
	}
	for _, el := range m.GetRecall() {
		items = append(items, &rank.Item{
			Map:  el.Map,
			Pctr: 0.0,
			Pcvr: 0.0,
			Cpc:  0.0,
			Cpa:  0.0,
			Ocpc: 0.0,
			Ocpm: 0.0,
		})
	}
	// 验证请求合法性
	if u == nil || len(items) == 0 {
		log.WithFields(log.Fields{
			"req":       in,
			"user_info": u,
			"item_size": 0,
		}).Error("predict error, bad request")
		return nil, errors.New("请求为空或不合法")
	}

	start := time.Now()
	resp, err := tfs.DoPredict(in, items)
	elapsed := time.Since(start)

	if err != nil {
		log.WithFields(log.Fields{
			"req":   in,
			"error": err.Error(),
		}).Error("predict error")
		return nil, err
	}

	if resp == nil {
		log.WithFields(log.Fields{
			"req": in,
		}).Error("predict error, response is nil")
		return nil, errors.New("response is nil")
	}
	o1 := resp.Outputs["output1"]
	o2 := resp.Outputs["output2"]

	var scores1 []float32
	var scores2 []float32
	if o1 != nil {
		scores1 = o1.GetFloatVal()
	}
	if o2 != nil {
		scores2 = o2.GetFloatVal()
	}

	is := len(items)

	if (scores1 == nil && scores2 == nil) || (scores1 != nil && len(scores1) != is) || (scores2 != nil && len(scores2) != is) {
		log.WithFields(log.Fields{
			"req": in,
		}).Error("predict error, scores is null or error")
	} else {
		for i := 0; i < is; i++ {
			item := items[i]
			pctr := float32(0.0)
			if scores1 != nil && i < len(scores1) {
				pctr = scores1[i]
			}
			pcvr := float32(0.0)
			if scores2 != nil && i < len(scores2) {
				pcvr = scores2[i]
			}
			cpc := item.GetCpc()
			cpa := item.GetCpa()
			ecpm := float32(0.0)
			if cpa > 0 {
				ecpm = pctr * pcvr * cpa * 1000
			} else {
				ecpm = pctr * cpc * 1000
			}
			item.Pctr = pctr
			item.Pcvr = pcvr
			item.Ecpm = ecpm
		}
	}

	log.WithFields(log.Fields{
		"tag":        in.GetUserInfo().GetVersion(),
		"uuid":       in.GetUserInfo().GetUuid(),
		"total_time": elapsed.Milliseconds(),
		"item_size":  is,
	}).Info("predict success")

	log.WithFields(log.Fields{
		"tag":        in.GetUserInfo().GetVersion(),
		"uuid":       in.GetUserInfo().GetUuid(),
		"total_time": elapsed.Milliseconds(),
		"item_size":  is,
		"req":        in,
		"result":     items,
	}).Debug("predict success")

	// log.Infof("process request:[%s], rlt:[%s]", in, items)
	return &rank.RankReply{Data: items}, nil
}
