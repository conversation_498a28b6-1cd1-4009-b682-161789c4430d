package service

import (
	"bid-gateway/internal/tfs"
	"bid-gateway/proto/api/rank"
	"context"
	"errors"
	"fmt"
	"time"

	log "github.com/sirupsen/logrus"
	types "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto"
)

type RankService struct {
	rank.UnimplementedRankerServer
}

func NewRankService() *RankService {
	return &RankService{}
}

func (rs *RankService) QueryRank(ctx context.Context, in *rank.RankRequest) (*rank.RankReply, error) {
	// 预处理请求 参看data.json
	var items []*rank.Item

	m := in.GetMap()
	u := in.GetUserInfo()

	//请求不存在recall
	if m == nil || m.GetRecall() == nil || len(m.Recall) == 0 {
		items = append(items, &rank.Item{
			Map:  map[string]string{},
			Pctr: 0.0,
			Pcvr: 0.0,
			Cpc:  0.0,
			Cpa:  0.0,
			Ocpc: 0.0,
			Ocpm: 0.0,
		})
	}
	for _, el := range m.GetRecall() {
		items = append(items, &rank.Item{
			Map:  el.Map,
			Pctr: 0.0,
			Pcvr: 0.0,
			Cpc:  0.0,
			Cpa:  0.0,
			Ocpc: 0.0,
			Ocpm: 0.0,
		})
	}
	// 验证请求合法性
	if u == nil || len(items) == 0 {
		log.WithFields(log.Fields{
			"req":       in,
			"user_info": u,
			"item_size": 0,
		}).Error("predict error, bad request")
		return nil, errors.New("请求为空或不合法")
	}

	start := time.Now()
	resp, err := tfs.DoPredict(in, items)
	elapsed := time.Since(start)

	if err != nil {
		log.WithFields(log.Fields{
			"req":   in,
			"error": err.Error(),
		}).Error("predict error")
		return nil, err
	}

	if resp == nil {
		log.WithFields(log.Fields{
			"req": in,
		}).Error("predict error, response is nil")
		return nil, errors.New("response is nil")
	}
	o1 := resp.Outputs["output1"]
	o2 := resp.Outputs["output2"]

	var scores1 []float32
	var scores2 []float32
	if o1 != nil {
		scores1 = o1.GetFloatVal()
	}
	if o2 != nil {
		scores2 = o2.GetFloatVal()
	}

	is := len(items)

	if (scores1 == nil && scores2 == nil) || (scores1 != nil && len(scores1) != is) || (scores2 != nil && len(scores2) != is) {
		log.WithFields(log.Fields{
			"req": in,
		}).Error("predict error, scores is null or error")
	} else {
		for i := 0; i < is; i++ {
			item := items[i]
			pctr := float32(0.0)
			if scores1 != nil && i < len(scores1) {
				pctr = scores1[i]
			}
			pcvr := float32(0.0)
			if scores2 != nil && i < len(scores2) {
				pcvr = scores2[i]
			}
			cpc := item.GetCpc()
			cpa := item.GetCpa()
			ecpm := float32(0.0)
			if cpa > 0 {
				ecpm = pctr * pcvr * cpa * 1000
			} else {
				ecpm = pctr * cpc * 1000
			}
			item.Pctr = pctr
			item.Pcvr = pcvr
			item.Ecpm = ecpm
		}
	}

	log.WithFields(log.Fields{
		"tag":        in.GetUserInfo().GetVersion(),
		"uuid":       in.GetUserInfo().GetUuid(),
		"total_time": elapsed.Milliseconds(),
		"item_size":  is,
	}).Info("predict success")

	log.WithFields(log.Fields{
		"tag":        in.GetUserInfo().GetVersion(),
		"uuid":       in.GetUserInfo().GetUuid(),
		"total_time": elapsed.Milliseconds(),
		"item_size":  is,
		"req":        in,
		"result":     items,
	}).Debug("predict success")

	// log.Infof("process request:[%s], rlt:[%s]", in, items)
	return &rank.RankReply{Data: items}, nil
}

// QuerySimpleRank 处理intput0特征的预测请求
// 专门用于处理 {"inputs": {"intput0": [13792273858822192]}} 格式的请求
func (rs *RankService) QuerySimpleRank(ctx context.Context, inputValue int64, modelName string) (*rank.RankReply, error) {
	start := time.Now()

	log.WithFields(log.Fields{
		"input_value": inputValue,
		"model_name":  modelName,
	}).Info("开始处理intput0特征预测请求")

	// 调用简单预测函数
	resp, err := tfs.DoSimplePredict(inputValue, modelName)
	elapsed := time.Since(start)

	if err != nil {
		log.WithFields(log.Fields{
			"input_value": inputValue,
			"model_name":  modelName,
			"error":       err.Error(),
			"elapsed_ms":  elapsed.Milliseconds(),
		}).Error("intput0预测请求失败")
		return nil, err
	}

	if resp == nil {
		log.WithFields(log.Fields{
			"input_value": inputValue,
			"model_name":  modelName,
		}).Error("intput0预测响应为空")
		return nil, errors.New("response is nil")
	}

	// 处理响应结果
	var items []*rank.Item

	// 记录所有输出
	log.WithFields(log.Fields{
		"output_count": len(resp.Outputs),
		"model_spec":   resp.ModelSpec,
	}).Info("收到模型响应")

	// 处理模型输出
	for outputName, outputTensor := range resp.Outputs {
		log.WithFields(log.Fields{
			"output_name":  outputName,
			"output_type":  outputTensor.Dtype.String(),
			"tensor_shape": outputTensor.TensorShape,
		}).Info("处理模型输出")

		// 创建结果项
		item := &rank.Item{
			Map: map[string]string{
				"input_value": fmt.Sprintf("%d", inputValue),
				"output_name": outputName,
				"model_name":  modelName,
			},
			Pctr: 0.0,
			Pcvr: 0.0,
			Cpc:  0.0,
			Cpa:  0.0,
			Ocpc: 0.0,
			Ocpm: 0.0,
		}

		// 根据输出类型处理结果
		switch outputTensor.Dtype {
		case types.DataType_DT_FLOAT:
			if floatVals := outputTensor.GetFloatVal(); floatVals != nil && len(floatVals) > 0 {
				item.Pctr = floatVals[0]
				log.WithFields(log.Fields{
					"output_name": outputName,
					"float_value": floatVals[0],
				}).Info("获取到float输出")
			}
		case types.DataType_DT_DOUBLE:
			if doubleVals := outputTensor.GetDoubleVal(); doubleVals != nil && len(doubleVals) > 0 {
				item.Pctr = float32(doubleVals[0])
				log.WithFields(log.Fields{
					"output_name":  outputName,
					"double_value": doubleVals[0],
				}).Info("获取到double输出")
			}
		case types.DataType_DT_INT64:
			if int64Vals := outputTensor.GetInt64Val(); int64Vals != nil && len(int64Vals) > 0 {
				item.Pctr = float32(int64Vals[0])
				log.WithFields(log.Fields{
					"output_name": outputName,
					"int64_value": int64Vals[0],
				}).Info("获取到int64输出")
			}
		}

		items = append(items, item)
	}

	// 如果没有输出，创建一个默认项
	if len(items) == 0 {
		items = append(items, &rank.Item{
			Map: map[string]string{
				"input_value": fmt.Sprintf("%d", inputValue),
				"model_name":  modelName,
				"status":      "no_output",
			},
			Pctr: 0.0,
			Pcvr: 0.0,
			Cpc:  0.0,
			Cpa:  0.0,
			Ocpc: 0.0,
			Ocpm: 0.0,
		})
		log.Warn("模型没有返回输出，创建默认结果项")
	}

	log.WithFields(log.Fields{
		"input_value": inputValue,
		"model_name":  modelName,
		"total_time":  elapsed.Milliseconds(),
		"item_size":   len(items),
	}).Info("intput0预测请求成功完成")

	return &rank.RankReply{Data: items}, nil
}
