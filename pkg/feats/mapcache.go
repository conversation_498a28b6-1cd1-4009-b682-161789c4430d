package feats

import (
	"errors"
	"log"
	"strings"
	"sync"
)

type MapCache struct {
	m *sync.Map
}

func InitMapCache() *MapCache {
	return &MapCache{
		m: &sync.Map{},
	}
}

func (mc *MapCache) Get(key string) (map[string]string, error) {
	if s, ok := mc.m.Load(key); ok {
		return s.(map[string]string), nil
	} else {
		log.Printf("feat:%s is not in schema", key)
		return nil, nil
	}
}

func (mc *MapCache) GetPrefix(prefix string) ([]string, error) {
	l := []string{}
	mc.m.Range(func(key, value any) bool {
		if strings.Contains(key.(string), prefix) {
			s := key.(string)
			l = append(l, strings.Split(s, ":")[2])
		}
		return true
	})
	return l, nil
}

func (mc *MapCache) Add(key string, value map[string]string) error {
	mc.m.Store(key, value)
	return nil
}

func (mc *MapCache) Update(key string, value map[string]string) error {
	s, err := mc.Get(key)
	if err != nil {
		return errors.New("获取值失败")
	}
	if ok := mc.m.CompareAndSwap(key, s, value); !ok {
		return errors.New("更新失败")
	}
	return nil
}

func (mc *MapCache) Delete(key string) error {
	mc.m.Delete(key)
	return nil
}

func (mc *MapCache) List() (map[string]any, error) {
	m := make(map[string]any)
	mc.m.Range(func(key, value any) bool {
		m[key.(string)] = value
		return true
	})
	return m, nil
}
