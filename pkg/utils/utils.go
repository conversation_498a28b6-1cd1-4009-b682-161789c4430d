package utils

import (
	"fmt"
	"strconv"
	"strings"
)

func GetTfList(str, sep string, length int, dv string) []any {
	// 使用给定的分隔符分割字符串
	rapper := strings.SplitN(FillNa(str), sep, length)
	// 如果分割后的数组长度小于给定长度，使用默认值填充
	for i := len(rapper); i < length; i++ {
		rapper = append(rapper, dv)
	}

	var result []any

	for _, val := range rapper {
		result = append(result, val)
	}

	return result
}

func FillNa(x string) string {
	// reg := regexp.MustCompile(`^\s*$`)
	// if reg.MatchString(x) {
	// 	return "_"
	// }
	// return x
	if len(x) == 0 || x == " " {
		return "_"
	}
	return x
}

type BaseType interface {
	string | float32 | int64
}

func AnySliceTo[T BaseType](s []any) []T {
	var t []T
	for i, v := range s {
		if value, ok := v.(T); ok {
			t = append(t, value)
		} else {
			fmt.Println("v的值是:", v)
			fmt.Printf("element at index %d is not of type string", i)

			// t = append(t, T) 存放零值
		}
	}
	return t
}

// func ParseLong(s []any, dv int64) int64 {
// 	ret, err := strconv.ParseInt(s, 10, 64)
// 	if err != nil {
// 		return dv
// 	}
// 	return ret
// }

// func ParseFloat(s []any, dv float64) []any {
// 	var result []any
// 	for _, val := range s {
// 		ret, err := strconv.ParseFloat(val.(string), 64)
// 		if err != nil {
// 			ret = dv
// 		}
// 		result = append(result, ret)
// 	}
// 	return result
// }

func ParseFloat(s string, dv float32) float32 {
	if ret, err := strconv.ParseFloat(s, 32); err == nil {
		return float32(ret)
	} else {
		return dv
	}

}

func ParseLong(s string, dv int64) int64 {
	if ret, err := strconv.ParseInt(s, 10, 64); err == nil {
		return ret
	} else {
		return dv
	}
}
