package utils

import (
	"sync"
)

type Pool[T Item[T]] struct {
	p sync.Pool
}

type Default[T any] interface {
	Default() T
}

type Reset interface {
	Reset()
}

type Item[T any] interface {
	Reset
	Default[T]
}

func NewPool[T Item[T]]() *Pool[T] {
	return &Pool[T]{
		p: sync.Pool{New: func() any {
			var a T
			return a.Default()
		}},
	}
}

func (p *Pool[T]) Get() T {
	return p.p.Get().(T)
}

func (p *Pool[T]) Put(t T) {
	t.Reset()
	p.p.Put(t)
}
