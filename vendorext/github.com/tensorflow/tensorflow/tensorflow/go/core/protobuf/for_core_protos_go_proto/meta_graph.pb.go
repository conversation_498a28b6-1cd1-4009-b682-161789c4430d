// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: tensorflow/core/protobuf/meta_graph.proto

package for_core_protos_go_proto

import (
	graph_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/graph_go_proto"
	op_def_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/op_def_go_proto"
	tensor_shape_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto"
	types_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// NOTE: This protocol buffer is evolving, and will go through revisions in the
// coming months.
//
// Protocol buffer containing the following which are necessary to restart
// training, run inference. It can be used to serialize/de-serialize memory
// objects necessary for running computation in a graph when crossing the
// process boundary. It can be used for long term storage of graphs,
// cross-language execution of graphs, etc.
//
//	MetaInfoDef
//	GraphDef
//	SaverDef
//	CollectionDef
//	TensorInfo
//	SignatureDef
type MetaGraphDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MetaInfoDef *MetaGraphDef_MetaInfoDef `protobuf:"bytes,1,opt,name=meta_info_def,json=metaInfoDef,proto3" json:"meta_info_def,omitempty"`
	// GraphDef.
	GraphDef *graph_go_proto.GraphDef `protobuf:"bytes,2,opt,name=graph_def,json=graphDef,proto3" json:"graph_def,omitempty"`
	// SaverDef.
	SaverDef *SaverDef `protobuf:"bytes,3,opt,name=saver_def,json=saverDef,proto3" json:"saver_def,omitempty"`
	// collection_def: Map from collection name to collections.
	// See CollectionDef section for details.
	CollectionDef map[string]*CollectionDef `protobuf:"bytes,4,rep,name=collection_def,json=collectionDef,proto3" json:"collection_def,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// signature_def: Map from user supplied key for a signature to a single
	// SignatureDef.
	SignatureDef map[string]*SignatureDef `protobuf:"bytes,5,rep,name=signature_def,json=signatureDef,proto3" json:"signature_def,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Asset file def to be used with the defined graph.
	AssetFileDef []*AssetFileDef `protobuf:"bytes,6,rep,name=asset_file_def,json=assetFileDef,proto3" json:"asset_file_def,omitempty"`
	// Extra information about the structure of functions and stateful objects.
	ObjectGraphDef *SavedObjectGraph `protobuf:"bytes,7,opt,name=object_graph_def,json=objectGraphDef,proto3" json:"object_graph_def,omitempty"`
}

func (x *MetaGraphDef) Reset() {
	*x = MetaGraphDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MetaGraphDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaGraphDef) ProtoMessage() {}

func (x *MetaGraphDef) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaGraphDef.ProtoReflect.Descriptor instead.
func (*MetaGraphDef) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_meta_graph_proto_rawDescGZIP(), []int{0}
}

func (x *MetaGraphDef) GetMetaInfoDef() *MetaGraphDef_MetaInfoDef {
	if x != nil {
		return x.MetaInfoDef
	}
	return nil
}

func (x *MetaGraphDef) GetGraphDef() *graph_go_proto.GraphDef {
	if x != nil {
		return x.GraphDef
	}
	return nil
}

func (x *MetaGraphDef) GetSaverDef() *SaverDef {
	if x != nil {
		return x.SaverDef
	}
	return nil
}

func (x *MetaGraphDef) GetCollectionDef() map[string]*CollectionDef {
	if x != nil {
		return x.CollectionDef
	}
	return nil
}

func (x *MetaGraphDef) GetSignatureDef() map[string]*SignatureDef {
	if x != nil {
		return x.SignatureDef
	}
	return nil
}

func (x *MetaGraphDef) GetAssetFileDef() []*AssetFileDef {
	if x != nil {
		return x.AssetFileDef
	}
	return nil
}

func (x *MetaGraphDef) GetObjectGraphDef() *SavedObjectGraph {
	if x != nil {
		return x.ObjectGraphDef
	}
	return nil
}

// CollectionDef should cover most collections.
// To add a user-defined collection, do one of the following:
//  1. For simple data types, such as string, int, float:
//     tf.add_to_collection("your_collection_name", your_simple_value)
//     strings will be stored as bytes_list.
//
// 2. For Protobuf types, there are three ways to add them:
//
//  1. tf.add_to_collection("your_collection_name",
//     your_proto.SerializeToString())
//
//     collection_def {
//     key: "user_defined_bytes_collection"
//     value {
//     bytes_list {
//     value: "queue_name: \"test_queue\"\n"
//     }
//     }
//     }
//
//     or
//
//  2. tf.add_to_collection("your_collection_name", str(your_proto))
//
//     collection_def {
//     key: "user_defined_string_collection"
//     value {
//     bytes_list {
//     value: "\n\ntest_queue"
//     }
//     }
//     }
//
//     or
//
//  3. any_buf = any_pb2.Any()
//     tf.add_to_collection("your_collection_name",
//     any_buf.Pack(your_proto))
//
//     collection_def {
//     key: "user_defined_any_collection"
//     value {
//     any_list {
//     value {
//     type_url: "type.googleapis.com/tensorflow.QueueRunnerDef"
//     value: "\n\ntest_queue"
//     }
//     }
//     }
//     }
//
//  3. For Python objects, implement to_proto() and from_proto(), and register
//     them in the following manner:
//     ops.register_proto_function("your_collection_name",
//     proto_type,
//     to_proto=YourPythonObject.to_proto,
//     from_proto=YourPythonObject.from_proto)
//     These functions will be invoked to serialize and de-serialize the
//     collection. For example,
//     ops.register_proto_function(ops.GraphKeys.GLOBAL_VARIABLES,
//     proto_type=variable_pb2.VariableDef,
//     to_proto=Variable.to_proto,
//     from_proto=Variable.from_proto)
type CollectionDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Kind:
	//
	//	*CollectionDef_NodeList_
	//	*CollectionDef_BytesList_
	//	*CollectionDef_Int64List_
	//	*CollectionDef_FloatList_
	//	*CollectionDef_AnyList_
	Kind isCollectionDef_Kind `protobuf_oneof:"kind"`
}

func (x *CollectionDef) Reset() {
	*x = CollectionDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionDef) ProtoMessage() {}

func (x *CollectionDef) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionDef.ProtoReflect.Descriptor instead.
func (*CollectionDef) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_meta_graph_proto_rawDescGZIP(), []int{1}
}

func (m *CollectionDef) GetKind() isCollectionDef_Kind {
	if m != nil {
		return m.Kind
	}
	return nil
}

func (x *CollectionDef) GetNodeList() *CollectionDef_NodeList {
	if x, ok := x.GetKind().(*CollectionDef_NodeList_); ok {
		return x.NodeList
	}
	return nil
}

func (x *CollectionDef) GetBytesList() *CollectionDef_BytesList {
	if x, ok := x.GetKind().(*CollectionDef_BytesList_); ok {
		return x.BytesList
	}
	return nil
}

func (x *CollectionDef) GetInt64List() *CollectionDef_Int64List {
	if x, ok := x.GetKind().(*CollectionDef_Int64List_); ok {
		return x.Int64List
	}
	return nil
}

func (x *CollectionDef) GetFloatList() *CollectionDef_FloatList {
	if x, ok := x.GetKind().(*CollectionDef_FloatList_); ok {
		return x.FloatList
	}
	return nil
}

func (x *CollectionDef) GetAnyList() *CollectionDef_AnyList {
	if x, ok := x.GetKind().(*CollectionDef_AnyList_); ok {
		return x.AnyList
	}
	return nil
}

type isCollectionDef_Kind interface {
	isCollectionDef_Kind()
}

type CollectionDef_NodeList_ struct {
	NodeList *CollectionDef_NodeList `protobuf:"bytes,1,opt,name=node_list,json=nodeList,proto3,oneof"`
}

type CollectionDef_BytesList_ struct {
	BytesList *CollectionDef_BytesList `protobuf:"bytes,2,opt,name=bytes_list,json=bytesList,proto3,oneof"`
}

type CollectionDef_Int64List_ struct {
	Int64List *CollectionDef_Int64List `protobuf:"bytes,3,opt,name=int64_list,json=int64List,proto3,oneof"`
}

type CollectionDef_FloatList_ struct {
	FloatList *CollectionDef_FloatList `protobuf:"bytes,4,opt,name=float_list,json=floatList,proto3,oneof"`
}

type CollectionDef_AnyList_ struct {
	AnyList *CollectionDef_AnyList `protobuf:"bytes,5,opt,name=any_list,json=anyList,proto3,oneof"`
}

func (*CollectionDef_NodeList_) isCollectionDef_Kind() {}

func (*CollectionDef_BytesList_) isCollectionDef_Kind() {}

func (*CollectionDef_Int64List_) isCollectionDef_Kind() {}

func (*CollectionDef_FloatList_) isCollectionDef_Kind() {}

func (*CollectionDef_AnyList_) isCollectionDef_Kind() {}

// Information about a Tensor necessary for feeding or retrieval.
type TensorInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Encoding:
	//
	//	*TensorInfo_Name
	//	*TensorInfo_CooSparse_
	//	*TensorInfo_CompositeTensor_
	Encoding isTensorInfo_Encoding   `protobuf_oneof:"encoding"`
	Dtype    types_go_proto.DataType `protobuf:"varint,2,opt,name=dtype,proto3,enum=tensorflow.DataType" json:"dtype,omitempty"`
	// The static shape should be recorded here, to the extent that it can
	// be known in advance.  In the case of a SparseTensor, this field describes
	// the logical shape of the represented tensor (aka dense_shape).
	TensorShape *tensor_shape_go_proto.TensorShapeProto `protobuf:"bytes,3,opt,name=tensor_shape,json=tensorShape,proto3" json:"tensor_shape,omitempty"`
}

func (x *TensorInfo) Reset() {
	*x = TensorInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TensorInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorInfo) ProtoMessage() {}

func (x *TensorInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorInfo.ProtoReflect.Descriptor instead.
func (*TensorInfo) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_meta_graph_proto_rawDescGZIP(), []int{2}
}

func (m *TensorInfo) GetEncoding() isTensorInfo_Encoding {
	if m != nil {
		return m.Encoding
	}
	return nil
}

func (x *TensorInfo) GetName() string {
	if x, ok := x.GetEncoding().(*TensorInfo_Name); ok {
		return x.Name
	}
	return ""
}

func (x *TensorInfo) GetCooSparse() *TensorInfo_CooSparse {
	if x, ok := x.GetEncoding().(*TensorInfo_CooSparse_); ok {
		return x.CooSparse
	}
	return nil
}

func (x *TensorInfo) GetCompositeTensor() *TensorInfo_CompositeTensor {
	if x, ok := x.GetEncoding().(*TensorInfo_CompositeTensor_); ok {
		return x.CompositeTensor
	}
	return nil
}

func (x *TensorInfo) GetDtype() types_go_proto.DataType {
	if x != nil {
		return x.Dtype
	}
	return types_go_proto.DataType_DT_INVALID
}

func (x *TensorInfo) GetTensorShape() *tensor_shape_go_proto.TensorShapeProto {
	if x != nil {
		return x.TensorShape
	}
	return nil
}

type isTensorInfo_Encoding interface {
	isTensorInfo_Encoding()
}

type TensorInfo_Name struct {
	// For dense `Tensor`s, the name of the tensor in the graph.
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type TensorInfo_CooSparse_ struct {
	// There are many possible encodings of sparse matrices
	// (https://en.wikipedia.org/wiki/Sparse_matrix).  Currently, TensorFlow
	// uses only the COO encoding.  This is supported and documented in the
	// SparseTensor Python class.
	CooSparse *TensorInfo_CooSparse `protobuf:"bytes,4,opt,name=coo_sparse,json=cooSparse,proto3,oneof"`
}

type TensorInfo_CompositeTensor_ struct {
	// Generic encoding for CompositeTensors.
	CompositeTensor *TensorInfo_CompositeTensor `protobuf:"bytes,5,opt,name=composite_tensor,json=compositeTensor,proto3,oneof"`
}

func (*TensorInfo_Name) isTensorInfo_Encoding() {}

func (*TensorInfo_CooSparse_) isTensorInfo_Encoding() {}

func (*TensorInfo_CompositeTensor_) isTensorInfo_Encoding() {}

// SignatureDef defines the signature of a computation supported by a TensorFlow
// graph.
//
// For example, a model with two loss computations, sharing a single input,
// might have the following signature_def map, in a MetaGraphDef message.
//
// Note that across the two SignatureDefs "loss_A" and "loss_B", the input key,
// output key, and method_name are identical, and will be used by system(s) that
// implement or rely upon this particular loss method. The output tensor names
// differ, demonstrating how different outputs can exist for the same method.
//
//	signature_def {
//	  key: "loss_A"
//	  value {
//	    inputs {
//	      key: "input"
//	      value {
//	        name: "input:0"
//	        dtype: DT_STRING
//	        tensor_shape: ...
//	      }
//	    }
//	    outputs {
//	      key: "loss_output"
//	      value {
//	        name: "loss_output_A:0"
//	        dtype: DT_FLOAT
//	        tensor_shape: ...
//	      }
//	    }
//	    method_name: "some/package/compute_loss"
//	  }
//	  ...
//	}
//
//	signature_def {
//	  key: "loss_B"
//	  value {
//	    inputs {
//	      key: "input"
//	      value {
//	        name: "input:0"
//	        dtype: DT_STRING
//	        tensor_shape: ...
//	      }
//	    }
//	    outputs {
//	      key: "loss_output"
//	      value {
//	        name: "loss_output_B:0"
//	        dtype: DT_FLOAT
//	        tensor_shape: ...
//	      }
//	    }
//	    method_name: "some/package/compute_loss"
//	  }
//	  ...
//	}
type SignatureDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Named input parameters.
	Inputs map[string]*TensorInfo `protobuf:"bytes,1,rep,name=inputs,proto3" json:"inputs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Named output parameters.
	Outputs map[string]*TensorInfo `protobuf:"bytes,2,rep,name=outputs,proto3" json:"outputs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Extensible method_name information enabling third-party users to mark a
	// SignatureDef as supporting a particular method. This enables producers and
	// consumers of SignatureDefs, e.g. a model definition library and a serving
	// library to have a clear hand-off regarding the semantics of a computation.
	//
	// Note that multiple SignatureDefs in a single MetaGraphDef may have the same
	// method_name. This is commonly used to support multi-headed computation,
	// where a single graph computation may return multiple results.
	MethodName string `protobuf:"bytes,3,opt,name=method_name,json=methodName,proto3" json:"method_name,omitempty"`
}

func (x *SignatureDef) Reset() {
	*x = SignatureDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignatureDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignatureDef) ProtoMessage() {}

func (x *SignatureDef) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignatureDef.ProtoReflect.Descriptor instead.
func (*SignatureDef) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_meta_graph_proto_rawDescGZIP(), []int{3}
}

func (x *SignatureDef) GetInputs() map[string]*TensorInfo {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *SignatureDef) GetOutputs() map[string]*TensorInfo {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *SignatureDef) GetMethodName() string {
	if x != nil {
		return x.MethodName
	}
	return ""
}

// An asset file def for a single file or a set of sharded files with the same
// name.
type AssetFileDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The tensor to bind the asset filename to.
	TensorInfo *TensorInfo `protobuf:"bytes,1,opt,name=tensor_info,json=tensorInfo,proto3" json:"tensor_info,omitempty"`
	// The filename within an assets directory. Note: does not include the path
	// prefix, i.e. directories. For an asset at /tmp/path/vocab.txt, the filename
	// would be "vocab.txt".
	Filename string `protobuf:"bytes,2,opt,name=filename,proto3" json:"filename,omitempty"`
}

func (x *AssetFileDef) Reset() {
	*x = AssetFileDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetFileDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetFileDef) ProtoMessage() {}

func (x *AssetFileDef) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetFileDef.ProtoReflect.Descriptor instead.
func (*AssetFileDef) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_meta_graph_proto_rawDescGZIP(), []int{4}
}

func (x *AssetFileDef) GetTensorInfo() *TensorInfo {
	if x != nil {
		return x.TensorInfo
	}
	return nil
}

func (x *AssetFileDef) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

// Meta information regarding the graph to be exported.  To be used by users
// of this protocol buffer to encode information regarding their meta graph.
type MetaGraphDef_MetaInfoDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User specified Version string. Can be the name of the model and revision,
	// steps this model has been trained to, etc.
	MetaGraphVersion string `protobuf:"bytes,1,opt,name=meta_graph_version,json=metaGraphVersion,proto3" json:"meta_graph_version,omitempty"`
	// A copy of the OpDefs used by the producer of this graph_def.
	// Descriptions and Ops not used in graph_def are stripped out.
	StrippedOpList *op_def_go_proto.OpList `protobuf:"bytes,2,opt,name=stripped_op_list,json=strippedOpList,proto3" json:"stripped_op_list,omitempty"`
	// A serialized protobuf. Can be the time this meta graph is created, or
	// modified, or name of the model.
	AnyInfo *anypb.Any `protobuf:"bytes,3,opt,name=any_info,json=anyInfo,proto3" json:"any_info,omitempty"`
	// User supplied tag(s) on the meta_graph and included graph_def.
	//
	// MetaGraphDefs should be tagged with their capabilities or use-cases.
	// Examples: "train", "serve", "gpu", "tpu", etc.
	// These tags enable loaders to access the MetaGraph(s) appropriate for a
	// specific use-case or runtime environment.
	Tags []string `protobuf:"bytes,4,rep,name=tags,proto3" json:"tags,omitempty"`
	// The __version__ string of the tensorflow build used to write this graph.
	// This will be populated by the framework, which will overwrite any user
	// supplied value.
	TensorflowVersion string `protobuf:"bytes,5,opt,name=tensorflow_version,json=tensorflowVersion,proto3" json:"tensorflow_version,omitempty"`
	// The __git_version__ string of the tensorflow build used to write this
	// graph. This will be populated by the framework, which will overwrite any
	// user supplied value.
	TensorflowGitVersion string `protobuf:"bytes,6,opt,name=tensorflow_git_version,json=tensorflowGitVersion,proto3" json:"tensorflow_git_version,omitempty"`
	// A flag to denote whether default-valued attrs have been stripped from
	// the nodes in this graph_def.
	StrippedDefaultAttrs bool `protobuf:"varint,7,opt,name=stripped_default_attrs,json=strippedDefaultAttrs,proto3" json:"stripped_default_attrs,omitempty"`
	// FunctionDef name to aliases mapping.
	FunctionAliases map[string]string `protobuf:"bytes,8,rep,name=function_aliases,json=functionAliases,proto3" json:"function_aliases,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *MetaGraphDef_MetaInfoDef) Reset() {
	*x = MetaGraphDef_MetaInfoDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MetaGraphDef_MetaInfoDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaGraphDef_MetaInfoDef) ProtoMessage() {}

func (x *MetaGraphDef_MetaInfoDef) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaGraphDef_MetaInfoDef.ProtoReflect.Descriptor instead.
func (*MetaGraphDef_MetaInfoDef) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_meta_graph_proto_rawDescGZIP(), []int{0, 0}
}

func (x *MetaGraphDef_MetaInfoDef) GetMetaGraphVersion() string {
	if x != nil {
		return x.MetaGraphVersion
	}
	return ""
}

func (x *MetaGraphDef_MetaInfoDef) GetStrippedOpList() *op_def_go_proto.OpList {
	if x != nil {
		return x.StrippedOpList
	}
	return nil
}

func (x *MetaGraphDef_MetaInfoDef) GetAnyInfo() *anypb.Any {
	if x != nil {
		return x.AnyInfo
	}
	return nil
}

func (x *MetaGraphDef_MetaInfoDef) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *MetaGraphDef_MetaInfoDef) GetTensorflowVersion() string {
	if x != nil {
		return x.TensorflowVersion
	}
	return ""
}

func (x *MetaGraphDef_MetaInfoDef) GetTensorflowGitVersion() string {
	if x != nil {
		return x.TensorflowGitVersion
	}
	return ""
}

func (x *MetaGraphDef_MetaInfoDef) GetStrippedDefaultAttrs() bool {
	if x != nil {
		return x.StrippedDefaultAttrs
	}
	return false
}

func (x *MetaGraphDef_MetaInfoDef) GetFunctionAliases() map[string]string {
	if x != nil {
		return x.FunctionAliases
	}
	return nil
}

// NodeList is used for collecting nodes in graph. For example
//
//	collection_def {
//	  key: "summaries"
//	  value {
//	    node_list {
//	      value: "input_producer/ScalarSummary:0"
//	      value: "shuffle_batch/ScalarSummary:0"
//	      value: "ImageSummary:0"
//	    }
//	  }
type CollectionDef_NodeList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []string `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *CollectionDef_NodeList) Reset() {
	*x = CollectionDef_NodeList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionDef_NodeList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionDef_NodeList) ProtoMessage() {}

func (x *CollectionDef_NodeList) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionDef_NodeList.ProtoReflect.Descriptor instead.
func (*CollectionDef_NodeList) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_meta_graph_proto_rawDescGZIP(), []int{1, 0}
}

func (x *CollectionDef_NodeList) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

// BytesList is used for collecting strings and serialized protobufs. For
// example:
//
//	collection_def {
//	  key: "trainable_variables"
//	  value {
//	    bytes_list {
//	      value: "\n\017conv1/weights:0\022\024conv1/weights/Assign
//	             \032\024conv1/weights/read:0"
//	      value: "\n\016conv1/biases:0\022\023conv1/biases/Assign\032
//	             \023conv1/biases/read:0"
//	    }
//	  }
//	}
type CollectionDef_BytesList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value [][]byte `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *CollectionDef_BytesList) Reset() {
	*x = CollectionDef_BytesList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionDef_BytesList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionDef_BytesList) ProtoMessage() {}

func (x *CollectionDef_BytesList) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionDef_BytesList.ProtoReflect.Descriptor instead.
func (*CollectionDef_BytesList) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_meta_graph_proto_rawDescGZIP(), []int{1, 1}
}

func (x *CollectionDef_BytesList) GetValue() [][]byte {
	if x != nil {
		return x.Value
	}
	return nil
}

// Int64List is used for collecting int, int64 and long values.
type CollectionDef_Int64List struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []int64 `protobuf:"varint,1,rep,packed,name=value,proto3" json:"value,omitempty"`
}

func (x *CollectionDef_Int64List) Reset() {
	*x = CollectionDef_Int64List{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionDef_Int64List) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionDef_Int64List) ProtoMessage() {}

func (x *CollectionDef_Int64List) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionDef_Int64List.ProtoReflect.Descriptor instead.
func (*CollectionDef_Int64List) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_meta_graph_proto_rawDescGZIP(), []int{1, 2}
}

func (x *CollectionDef_Int64List) GetValue() []int64 {
	if x != nil {
		return x.Value
	}
	return nil
}

// FloatList is used for collecting float values.
type CollectionDef_FloatList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []float32 `protobuf:"fixed32,1,rep,packed,name=value,proto3" json:"value,omitempty"`
}

func (x *CollectionDef_FloatList) Reset() {
	*x = CollectionDef_FloatList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionDef_FloatList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionDef_FloatList) ProtoMessage() {}

func (x *CollectionDef_FloatList) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionDef_FloatList.ProtoReflect.Descriptor instead.
func (*CollectionDef_FloatList) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_meta_graph_proto_rawDescGZIP(), []int{1, 3}
}

func (x *CollectionDef_FloatList) GetValue() []float32 {
	if x != nil {
		return x.Value
	}
	return nil
}

// AnyList is used for collecting Any protos.
type CollectionDef_AnyList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []*anypb.Any `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *CollectionDef_AnyList) Reset() {
	*x = CollectionDef_AnyList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionDef_AnyList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionDef_AnyList) ProtoMessage() {}

func (x *CollectionDef_AnyList) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionDef_AnyList.ProtoReflect.Descriptor instead.
func (*CollectionDef_AnyList) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_meta_graph_proto_rawDescGZIP(), []int{1, 4}
}

func (x *CollectionDef_AnyList) GetValue() []*anypb.Any {
	if x != nil {
		return x.Value
	}
	return nil
}

// For sparse tensors, The COO encoding stores a triple of values, indices,
// and shape.
type TensorInfo_CooSparse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The shape of the values Tensor is [?].  Its dtype must be the dtype of
	// the SparseTensor as a whole, given in the enclosing TensorInfo.
	ValuesTensorName string `protobuf:"bytes,1,opt,name=values_tensor_name,json=valuesTensorName,proto3" json:"values_tensor_name,omitempty"`
	// The indices Tensor must have dtype int64 and shape [?, ?].
	IndicesTensorName string `protobuf:"bytes,2,opt,name=indices_tensor_name,json=indicesTensorName,proto3" json:"indices_tensor_name,omitempty"`
	// The dynamic logical shape represented by the SparseTensor is recorded in
	// the Tensor referenced here.  It must have dtype int64 and shape [?].
	DenseShapeTensorName string `protobuf:"bytes,3,opt,name=dense_shape_tensor_name,json=denseShapeTensorName,proto3" json:"dense_shape_tensor_name,omitempty"`
}

func (x *TensorInfo_CooSparse) Reset() {
	*x = TensorInfo_CooSparse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TensorInfo_CooSparse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorInfo_CooSparse) ProtoMessage() {}

func (x *TensorInfo_CooSparse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorInfo_CooSparse.ProtoReflect.Descriptor instead.
func (*TensorInfo_CooSparse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_meta_graph_proto_rawDescGZIP(), []int{2, 0}
}

func (x *TensorInfo_CooSparse) GetValuesTensorName() string {
	if x != nil {
		return x.ValuesTensorName
	}
	return ""
}

func (x *TensorInfo_CooSparse) GetIndicesTensorName() string {
	if x != nil {
		return x.IndicesTensorName
	}
	return ""
}

func (x *TensorInfo_CooSparse) GetDenseShapeTensorName() string {
	if x != nil {
		return x.DenseShapeTensorName
	}
	return ""
}

// Generic encoding for composite tensors.
type TensorInfo_CompositeTensor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The serialized TypeSpec for the composite tensor.
	TypeSpec *TypeSpecProto `protobuf:"bytes,1,opt,name=type_spec,json=typeSpec,proto3" json:"type_spec,omitempty"`
	// A TensorInfo for each flattened component tensor.
	Components []*TensorInfo `protobuf:"bytes,2,rep,name=components,proto3" json:"components,omitempty"`
}

func (x *TensorInfo_CompositeTensor) Reset() {
	*x = TensorInfo_CompositeTensor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TensorInfo_CompositeTensor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorInfo_CompositeTensor) ProtoMessage() {}

func (x *TensorInfo_CompositeTensor) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorInfo_CompositeTensor.ProtoReflect.Descriptor instead.
func (*TensorInfo_CompositeTensor) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_meta_graph_proto_rawDescGZIP(), []int{2, 1}
}

func (x *TensorInfo_CompositeTensor) GetTypeSpec() *TypeSpecProto {
	if x != nil {
		return x.TypeSpec
	}
	return nil
}

func (x *TensorInfo_CompositeTensor) GetComponents() []*TensorInfo {
	if x != nil {
		return x.Components
	}
	return nil
}

var File_tensorflow_core_protobuf_meta_graph_proto protoreflect.FileDescriptor

var file_tensorflow_core_protobuf_meta_graph_proto_rawDesc = []byte{
	0x0a, 0x29, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x5f,
	0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63,
	0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x67, 0x72,
	0x61, 0x70, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65,
	0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x6f, 0x70, 0x5f, 0x64, 0x65, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2c, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f,
	0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x5f, 0x73, 0x68, 0x61, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x73, 0x61, 0x76, 0x65, 0x64, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x67, 0x72,
	0x61, 0x70, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x73, 0x61, 0x76, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa9, 0x09, 0x0a, 0x0c, 0x4d, 0x65, 0x74, 0x61, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x44, 0x65, 0x66, 0x12, 0x48, 0x0a, 0x0d, 0x6d, 0x65, 0x74, 0x61, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x47, 0x72, 0x61, 0x70, 0x68, 0x44, 0x65, 0x66, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66,
	0x6f, 0x44, 0x65, 0x66, 0x52, 0x0b, 0x6d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65,
	0x66, 0x12, 0x31, 0x0a, 0x09, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x44, 0x65, 0x66, 0x52, 0x08, 0x67, 0x72, 0x61, 0x70,
	0x68, 0x44, 0x65, 0x66, 0x12, 0x31, 0x0a, 0x09, 0x73, 0x61, 0x76, 0x65, 0x72, 0x5f, 0x64, 0x65,
	0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x72, 0x44, 0x65, 0x66, 0x52, 0x08, 0x73,
	0x61, 0x76, 0x65, 0x72, 0x44, 0x65, 0x66, 0x12, 0x52, 0x0a, 0x0e, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x4d, 0x65, 0x74,
	0x61, 0x47, 0x72, 0x61, 0x70, 0x68, 0x44, 0x65, 0x66, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x4f, 0x0a, 0x0d, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x4d, 0x65, 0x74, 0x61, 0x47, 0x72, 0x61, 0x70, 0x68, 0x44, 0x65, 0x66, 0x2e, 0x53, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x44, 0x65, 0x66, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x44, 0x65, 0x66, 0x12, 0x3e, 0x0a, 0x0e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0c,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x12, 0x46, 0x0a, 0x10,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x64, 0x65, 0x66,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x64, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x52, 0x0e, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x47, 0x72, 0x61, 0x70,
	0x68, 0x44, 0x65, 0x66, 0x1a, 0x83, 0x04, 0x0a, 0x0b, 0x4d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66,
	0x6f, 0x44, 0x65, 0x66, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x67, 0x72, 0x61,
	0x70, 0x68, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x6d, 0x65, 0x74, 0x61, 0x47, 0x72, 0x61, 0x70, 0x68, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x69, 0x70, 0x70, 0x65, 0x64, 0x5f, 0x6f,
	0x70, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x4f, 0x70, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x0e, 0x73, 0x74, 0x72, 0x69, 0x70, 0x70, 0x65, 0x64, 0x4f, 0x70, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x2f, 0x0a, 0x08, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x07, 0x61, 0x6e, 0x79, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2d, 0x0a, 0x12, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x16, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x67, 0x69, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x47, 0x69, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x16, 0x73, 0x74,
	0x72, 0x69, 0x70, 0x70, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x61,
	0x74, 0x74, 0x72, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x73, 0x74, 0x72, 0x69,
	0x70, 0x70, 0x65, 0x64, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x41, 0x74, 0x74, 0x72, 0x73,
	0x12, 0x64, 0x0a, 0x10, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6c, 0x69,
	0x61, 0x73, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x47, 0x72, 0x61, 0x70,
	0x68, 0x44, 0x65, 0x66, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x66,
	0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41,
	0x6c, 0x69, 0x61, 0x73, 0x65, 0x73, 0x1a, 0x42, 0x0a, 0x14, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x5b, 0x0a, 0x12, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x59, 0x0a, 0x11, 0x53, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x44, 0x65, 0x66, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2e,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x44, 0x65, 0x66, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xb6, 0x04, 0x0a, 0x0d, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x66, 0x12, 0x41, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x66, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x08, 0x6e,
	0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x44, 0x0a, 0x0a, 0x62, 0x79, 0x74, 0x65, 0x73,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x2e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x48, 0x00, 0x52, 0x09, 0x62, 0x79, 0x74, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x44, 0x0a,
	0x0a, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x2e, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x44, 0x0a, 0x0a, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x66, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x09,
	0x66, 0x6c, 0x6f, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3e, 0x0a, 0x08, 0x61, 0x6e, 0x79,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x07, 0x61, 0x6e, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x20, 0x0a, 0x08, 0x4e, 0x6f, 0x64,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0x21, 0x0a, 0x09, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0x25,
	0x0a, 0x09, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x02, 0x10, 0x01, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0x25, 0x0a, 0x09, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x18, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x02, 0x42, 0x02, 0x10, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0x35, 0x0a, 0x07,
	0x41, 0x6e, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x22, 0xda, 0x04, 0x0a, 0x0a,
	0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x41, 0x0a, 0x0a, 0x63, 0x6f, 0x6f, 0x5f, 0x73, 0x70, 0x61, 0x72, 0x73, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x43, 0x6f, 0x6f,
	0x53, 0x70, 0x61, 0x72, 0x73, 0x65, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6f, 0x53, 0x70, 0x61,
	0x72, 0x73, 0x65, 0x12, 0x53, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65,
	0x5f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x54,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x65, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x2a, 0x0a, 0x05, 0x64, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x64,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x73,
	0x68, 0x61, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53, 0x68,
	0x61, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x0b, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x53, 0x68, 0x61, 0x70, 0x65, 0x1a, 0xa0, 0x01, 0x0a, 0x09, 0x43, 0x6f, 0x6f, 0x53, 0x70, 0x61,
	0x72, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x5f, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x65, 0x73, 0x5f, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x69, 0x6e, 0x64, 0x69, 0x63, 0x65, 0x73, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x35, 0x0a, 0x17, 0x64, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x70, 0x65,
	0x5f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x14, 0x64, 0x65, 0x6e, 0x73, 0x65, 0x53, 0x68, 0x61, 0x70, 0x65, 0x54, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x81, 0x01, 0x0a, 0x0f, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x36, 0x0a, 0x09,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x79, 0x70,
	0x65, 0x53, 0x70, 0x65, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x08, 0x74, 0x79, 0x70, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x12, 0x36, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x0a, 0x0a, 0x08,
	0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x22, 0xd5, 0x02, 0x0a, 0x0c, 0x53, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x44, 0x65, 0x66, 0x12, 0x3c, 0x0a, 0x06, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x44, 0x65, 0x66, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x3f, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x44,
	0x65, 0x66, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x51, 0x0a, 0x0b, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x52, 0x0a, 0x0c,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x63, 0x0a, 0x0c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x66,
	0x12, 0x37, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x87, 0x01, 0x0a, 0x18, 0x6f, 0x72, 0x67, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f,
	0x72, 0x6b, 0x42, 0x0f, 0x4d, 0x65, 0x74, 0x61, 0x47, 0x72, 0x61, 0x70, 0x68, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x73, 0x50, 0x01, 0x5a, 0x55, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2f, 0x67, 0x6f, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x66, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x73, 0x5f, 0x67, 0x6f, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0xf8, 0x01, 0x01, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tensorflow_core_protobuf_meta_graph_proto_rawDescOnce sync.Once
	file_tensorflow_core_protobuf_meta_graph_proto_rawDescData = file_tensorflow_core_protobuf_meta_graph_proto_rawDesc
)

func file_tensorflow_core_protobuf_meta_graph_proto_rawDescGZIP() []byte {
	file_tensorflow_core_protobuf_meta_graph_proto_rawDescOnce.Do(func() {
		file_tensorflow_core_protobuf_meta_graph_proto_rawDescData = protoimpl.X.CompressGZIP(file_tensorflow_core_protobuf_meta_graph_proto_rawDescData)
	})
	return file_tensorflow_core_protobuf_meta_graph_proto_rawDescData
}

var file_tensorflow_core_protobuf_meta_graph_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_tensorflow_core_protobuf_meta_graph_proto_goTypes = []interface{}{
	(*MetaGraphDef)(nil),               // 0: tensorflow.MetaGraphDef
	(*CollectionDef)(nil),              // 1: tensorflow.CollectionDef
	(*TensorInfo)(nil),                 // 2: tensorflow.TensorInfo
	(*SignatureDef)(nil),               // 3: tensorflow.SignatureDef
	(*AssetFileDef)(nil),               // 4: tensorflow.AssetFileDef
	(*MetaGraphDef_MetaInfoDef)(nil),   // 5: tensorflow.MetaGraphDef.MetaInfoDef
	nil,                                // 6: tensorflow.MetaGraphDef.CollectionDefEntry
	nil,                                // 7: tensorflow.MetaGraphDef.SignatureDefEntry
	nil,                                // 8: tensorflow.MetaGraphDef.MetaInfoDef.FunctionAliasesEntry
	(*CollectionDef_NodeList)(nil),     // 9: tensorflow.CollectionDef.NodeList
	(*CollectionDef_BytesList)(nil),    // 10: tensorflow.CollectionDef.BytesList
	(*CollectionDef_Int64List)(nil),    // 11: tensorflow.CollectionDef.Int64List
	(*CollectionDef_FloatList)(nil),    // 12: tensorflow.CollectionDef.FloatList
	(*CollectionDef_AnyList)(nil),      // 13: tensorflow.CollectionDef.AnyList
	(*TensorInfo_CooSparse)(nil),       // 14: tensorflow.TensorInfo.CooSparse
	(*TensorInfo_CompositeTensor)(nil), // 15: tensorflow.TensorInfo.CompositeTensor
	nil,                                // 16: tensorflow.SignatureDef.InputsEntry
	nil,                                // 17: tensorflow.SignatureDef.OutputsEntry
	(*graph_go_proto.GraphDef)(nil),    // 18: tensorflow.GraphDef
	(*SaverDef)(nil),                   // 19: tensorflow.SaverDef
	(*SavedObjectGraph)(nil),           // 20: tensorflow.SavedObjectGraph
	(types_go_proto.DataType)(0),       // 21: tensorflow.DataType
	(*tensor_shape_go_proto.TensorShapeProto)(nil), // 22: tensorflow.TensorShapeProto
	(*op_def_go_proto.OpList)(nil),                 // 23: tensorflow.OpList
	(*anypb.Any)(nil),                              // 24: google.protobuf.Any
	(*TypeSpecProto)(nil),                          // 25: tensorflow.TypeSpecProto
}
var file_tensorflow_core_protobuf_meta_graph_proto_depIdxs = []int32{
	5,  // 0: tensorflow.MetaGraphDef.meta_info_def:type_name -> tensorflow.MetaGraphDef.MetaInfoDef
	18, // 1: tensorflow.MetaGraphDef.graph_def:type_name -> tensorflow.GraphDef
	19, // 2: tensorflow.MetaGraphDef.saver_def:type_name -> tensorflow.SaverDef
	6,  // 3: tensorflow.MetaGraphDef.collection_def:type_name -> tensorflow.MetaGraphDef.CollectionDefEntry
	7,  // 4: tensorflow.MetaGraphDef.signature_def:type_name -> tensorflow.MetaGraphDef.SignatureDefEntry
	4,  // 5: tensorflow.MetaGraphDef.asset_file_def:type_name -> tensorflow.AssetFileDef
	20, // 6: tensorflow.MetaGraphDef.object_graph_def:type_name -> tensorflow.SavedObjectGraph
	9,  // 7: tensorflow.CollectionDef.node_list:type_name -> tensorflow.CollectionDef.NodeList
	10, // 8: tensorflow.CollectionDef.bytes_list:type_name -> tensorflow.CollectionDef.BytesList
	11, // 9: tensorflow.CollectionDef.int64_list:type_name -> tensorflow.CollectionDef.Int64List
	12, // 10: tensorflow.CollectionDef.float_list:type_name -> tensorflow.CollectionDef.FloatList
	13, // 11: tensorflow.CollectionDef.any_list:type_name -> tensorflow.CollectionDef.AnyList
	14, // 12: tensorflow.TensorInfo.coo_sparse:type_name -> tensorflow.TensorInfo.CooSparse
	15, // 13: tensorflow.TensorInfo.composite_tensor:type_name -> tensorflow.TensorInfo.CompositeTensor
	21, // 14: tensorflow.TensorInfo.dtype:type_name -> tensorflow.DataType
	22, // 15: tensorflow.TensorInfo.tensor_shape:type_name -> tensorflow.TensorShapeProto
	16, // 16: tensorflow.SignatureDef.inputs:type_name -> tensorflow.SignatureDef.InputsEntry
	17, // 17: tensorflow.SignatureDef.outputs:type_name -> tensorflow.SignatureDef.OutputsEntry
	2,  // 18: tensorflow.AssetFileDef.tensor_info:type_name -> tensorflow.TensorInfo
	23, // 19: tensorflow.MetaGraphDef.MetaInfoDef.stripped_op_list:type_name -> tensorflow.OpList
	24, // 20: tensorflow.MetaGraphDef.MetaInfoDef.any_info:type_name -> google.protobuf.Any
	8,  // 21: tensorflow.MetaGraphDef.MetaInfoDef.function_aliases:type_name -> tensorflow.MetaGraphDef.MetaInfoDef.FunctionAliasesEntry
	1,  // 22: tensorflow.MetaGraphDef.CollectionDefEntry.value:type_name -> tensorflow.CollectionDef
	3,  // 23: tensorflow.MetaGraphDef.SignatureDefEntry.value:type_name -> tensorflow.SignatureDef
	24, // 24: tensorflow.CollectionDef.AnyList.value:type_name -> google.protobuf.Any
	25, // 25: tensorflow.TensorInfo.CompositeTensor.type_spec:type_name -> tensorflow.TypeSpecProto
	2,  // 26: tensorflow.TensorInfo.CompositeTensor.components:type_name -> tensorflow.TensorInfo
	2,  // 27: tensorflow.SignatureDef.InputsEntry.value:type_name -> tensorflow.TensorInfo
	2,  // 28: tensorflow.SignatureDef.OutputsEntry.value:type_name -> tensorflow.TensorInfo
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_tensorflow_core_protobuf_meta_graph_proto_init() }
func file_tensorflow_core_protobuf_meta_graph_proto_init() {
	if File_tensorflow_core_protobuf_meta_graph_proto != nil {
		return
	}
	file_tensorflow_core_protobuf_saved_object_graph_proto_init()
	file_tensorflow_core_protobuf_saver_proto_init()
	file_tensorflow_core_protobuf_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MetaGraphDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TensorInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignatureDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetFileDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MetaGraphDef_MetaInfoDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionDef_NodeList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionDef_BytesList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionDef_Int64List); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionDef_FloatList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionDef_AnyList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TensorInfo_CooSparse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TensorInfo_CompositeTensor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*CollectionDef_NodeList_)(nil),
		(*CollectionDef_BytesList_)(nil),
		(*CollectionDef_Int64List_)(nil),
		(*CollectionDef_FloatList_)(nil),
		(*CollectionDef_AnyList_)(nil),
	}
	file_tensorflow_core_protobuf_meta_graph_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*TensorInfo_Name)(nil),
		(*TensorInfo_CooSparse_)(nil),
		(*TensorInfo_CompositeTensor_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tensorflow_core_protobuf_meta_graph_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_core_protobuf_meta_graph_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_protobuf_meta_graph_proto_depIdxs,
		MessageInfos:      file_tensorflow_core_protobuf_meta_graph_proto_msgTypes,
	}.Build()
	File_tensorflow_core_protobuf_meta_graph_proto = out.File
	file_tensorflow_core_protobuf_meta_graph_proto_rawDesc = nil
	file_tensorflow_core_protobuf_meta_graph_proto_goTypes = nil
	file_tensorflow_core_protobuf_meta_graph_proto_depIdxs = nil
}
