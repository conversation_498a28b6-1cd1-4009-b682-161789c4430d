// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: tensorflow/core/protobuf/debug_event.proto

package for_core_protos_go_proto

import (
	tensor_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Available modes for extracting debugging information from a Tensor.
// TODO(cais): Document the detailed column names and semantics in a separate
// markdown file once the implementation settles.
type TensorDebugMode int32

const (
	TensorDebugMode_UNSPECIFIED TensorDebugMode = 0
	// Only records what tensors are computed, eagerly or in graphs.
	// No information regarding the value of the tensor is available.
	TensorDebugMode_NO_TENSOR TensorDebugMode = 1
	// A minimalist health summary for float-type tensors.
	// Contains information only about the presence/absence of pathological
	// values including Infinity and NaN.
	// Applicable only to float dtypes.
	TensorDebugMode_CURT_HEALTH TensorDebugMode = 2
	// A concise health summary for float-type tensors.
	// Contains more information that CURT_HEALTH.
	// Infinity and NaN are treated differently.
	// Applicable only to float and integer dtypes.
	TensorDebugMode_CONCISE_HEALTH TensorDebugMode = 3
	// A detailed health summary.
	// Contains further detailed information than `CONCISE_HEALTH`.
	// Information about device, dtype and shape are included.
	// Counts for various types of values (Infinity, NaN, negative, zero,
	// positive) are included.
	// Applicable to float, integer and boolean dtypes.
	TensorDebugMode_FULL_HEALTH TensorDebugMode = 4
	// Provides full runtime shape information, up to a maximum rank, beyond
	// which the dimension sizes are truncated.
	TensorDebugMode_SHAPE TensorDebugMode = 5
	// Full numeric summary.
	// Including device, dtype, shape, counts of various types of values
	// (Infinity, NaN, negative, zero, positive), and summary statistics
	// (minimum, maximum, mean and variance).
	// Applicable to float, integer and boolean dtypes.
	TensorDebugMode_FULL_NUMERICS TensorDebugMode = 6
	// Full tensor value.
	TensorDebugMode_FULL_TENSOR TensorDebugMode = 7
	// Reduce the elements of a tensor to a rank-1 tensor of shape [3], in which
	//   - the 1st element is -inf if any element of the tensor is -inf,
	//     or zero otherwise.
	//   - the 2nd element is +inf if any element of the tensor is +inf,
	//     or zero otherwise.
	//   - the 3rd element is nan if any element of the tensor is nan, or zero
	//     otherwise.
	TensorDebugMode_REDUCE_INF_NAN_THREE_SLOTS TensorDebugMode = 8
)

// Enum value maps for TensorDebugMode.
var (
	TensorDebugMode_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "NO_TENSOR",
		2: "CURT_HEALTH",
		3: "CONCISE_HEALTH",
		4: "FULL_HEALTH",
		5: "SHAPE",
		6: "FULL_NUMERICS",
		7: "FULL_TENSOR",
		8: "REDUCE_INF_NAN_THREE_SLOTS",
	}
	TensorDebugMode_value = map[string]int32{
		"UNSPECIFIED":                0,
		"NO_TENSOR":                  1,
		"CURT_HEALTH":                2,
		"CONCISE_HEALTH":             3,
		"FULL_HEALTH":                4,
		"SHAPE":                      5,
		"FULL_NUMERICS":              6,
		"FULL_TENSOR":                7,
		"REDUCE_INF_NAN_THREE_SLOTS": 8,
	}
)

func (x TensorDebugMode) Enum() *TensorDebugMode {
	p := new(TensorDebugMode)
	*p = x
	return p
}

func (x TensorDebugMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TensorDebugMode) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_core_protobuf_debug_event_proto_enumTypes[0].Descriptor()
}

func (TensorDebugMode) Type() protoreflect.EnumType {
	return &file_tensorflow_core_protobuf_debug_event_proto_enumTypes[0]
}

func (x TensorDebugMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TensorDebugMode.Descriptor instead.
func (TensorDebugMode) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_debug_event_proto_rawDescGZIP(), []int{0}
}

// An Event related to the debugging of a TensorFlow program.
type DebugEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Timestamp in seconds (with microsecond precision).
	WallTime float64 `protobuf:"fixed64,1,opt,name=wall_time,json=wallTime,proto3" json:"wall_time,omitempty"`
	// Step of training (if available).
	Step int64 `protobuf:"varint,2,opt,name=step,proto3" json:"step,omitempty"`
	// Types that are assignable to What:
	//
	//	*DebugEvent_DebugMetadata
	//	*DebugEvent_SourceFile
	//	*DebugEvent_StackFrameWithId
	//	*DebugEvent_GraphOpCreation
	//	*DebugEvent_DebuggedGraph
	//	*DebugEvent_Execution
	//	*DebugEvent_GraphExecutionTrace
	//	*DebugEvent_GraphId
	//	*DebugEvent_DebuggedDevice
	What isDebugEvent_What `protobuf_oneof:"what"`
}

func (x *DebugEvent) Reset() {
	*x = DebugEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebugEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugEvent) ProtoMessage() {}

func (x *DebugEvent) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebugEvent.ProtoReflect.Descriptor instead.
func (*DebugEvent) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_debug_event_proto_rawDescGZIP(), []int{0}
}

func (x *DebugEvent) GetWallTime() float64 {
	if x != nil {
		return x.WallTime
	}
	return 0
}

func (x *DebugEvent) GetStep() int64 {
	if x != nil {
		return x.Step
	}
	return 0
}

func (m *DebugEvent) GetWhat() isDebugEvent_What {
	if m != nil {
		return m.What
	}
	return nil
}

func (x *DebugEvent) GetDebugMetadata() *DebugMetadata {
	if x, ok := x.GetWhat().(*DebugEvent_DebugMetadata); ok {
		return x.DebugMetadata
	}
	return nil
}

func (x *DebugEvent) GetSourceFile() *SourceFile {
	if x, ok := x.GetWhat().(*DebugEvent_SourceFile); ok {
		return x.SourceFile
	}
	return nil
}

func (x *DebugEvent) GetStackFrameWithId() *StackFrameWithId {
	if x, ok := x.GetWhat().(*DebugEvent_StackFrameWithId); ok {
		return x.StackFrameWithId
	}
	return nil
}

func (x *DebugEvent) GetGraphOpCreation() *GraphOpCreation {
	if x, ok := x.GetWhat().(*DebugEvent_GraphOpCreation); ok {
		return x.GraphOpCreation
	}
	return nil
}

func (x *DebugEvent) GetDebuggedGraph() *DebuggedGraph {
	if x, ok := x.GetWhat().(*DebugEvent_DebuggedGraph); ok {
		return x.DebuggedGraph
	}
	return nil
}

func (x *DebugEvent) GetExecution() *Execution {
	if x, ok := x.GetWhat().(*DebugEvent_Execution); ok {
		return x.Execution
	}
	return nil
}

func (x *DebugEvent) GetGraphExecutionTrace() *GraphExecutionTrace {
	if x, ok := x.GetWhat().(*DebugEvent_GraphExecutionTrace); ok {
		return x.GraphExecutionTrace
	}
	return nil
}

func (x *DebugEvent) GetGraphId() string {
	if x, ok := x.GetWhat().(*DebugEvent_GraphId); ok {
		return x.GraphId
	}
	return ""
}

func (x *DebugEvent) GetDebuggedDevice() *DebuggedDevice {
	if x, ok := x.GetWhat().(*DebugEvent_DebuggedDevice); ok {
		return x.DebuggedDevice
	}
	return nil
}

type isDebugEvent_What interface {
	isDebugEvent_What()
}

type DebugEvent_DebugMetadata struct {
	// Metadata related to this debugging data.
	DebugMetadata *DebugMetadata `protobuf:"bytes,3,opt,name=debug_metadata,json=debugMetadata,proto3,oneof"`
}

type DebugEvent_SourceFile struct {
	// The content of a source file.
	SourceFile *SourceFile `protobuf:"bytes,4,opt,name=source_file,json=sourceFile,proto3,oneof"`
}

type DebugEvent_StackFrameWithId struct {
	// A stack frame (filename, line number and column number, function name and
	// code string) with ID.
	StackFrameWithId *StackFrameWithId `protobuf:"bytes,6,opt,name=stack_frame_with_id,json=stackFrameWithId,proto3,oneof"`
}

type DebugEvent_GraphOpCreation struct {
	// The creation of an op within a graph (e.g., a FuncGraph compiled from
	// a Python function).
	GraphOpCreation *GraphOpCreation `protobuf:"bytes,7,opt,name=graph_op_creation,json=graphOpCreation,proto3,oneof"`
}

type DebugEvent_DebuggedGraph struct {
	// Information about a debugged graph.
	DebuggedGraph *DebuggedGraph `protobuf:"bytes,8,opt,name=debugged_graph,json=debuggedGraph,proto3,oneof"`
}

type DebugEvent_Execution struct {
	// Execution of an op or a Graph (e.g., a tf.function).
	Execution *Execution `protobuf:"bytes,9,opt,name=execution,proto3,oneof"`
}

type DebugEvent_GraphExecutionTrace struct {
	// A graph execution trace: Contains information about the intermediate
	// tensors computed during the graph execution.
	GraphExecutionTrace *GraphExecutionTrace `protobuf:"bytes,10,opt,name=graph_execution_trace,json=graphExecutionTrace,proto3,oneof"`
}

type DebugEvent_GraphId struct {
	// The ID of the graph (i.e., FuncGraph) executed here: applicable only
	// to the execution of a FuncGraph.
	GraphId string `protobuf:"bytes,11,opt,name=graph_id,json=graphId,proto3,oneof"`
}

type DebugEvent_DebuggedDevice struct {
	// A device on which debugger-instrumented ops and/or tensors reside.
	DebuggedDevice *DebuggedDevice `protobuf:"bytes,12,opt,name=debugged_device,json=debuggedDevice,proto3,oneof"`
}

func (*DebugEvent_DebugMetadata) isDebugEvent_What() {}

func (*DebugEvent_SourceFile) isDebugEvent_What() {}

func (*DebugEvent_StackFrameWithId) isDebugEvent_What() {}

func (*DebugEvent_GraphOpCreation) isDebugEvent_What() {}

func (*DebugEvent_DebuggedGraph) isDebugEvent_What() {}

func (*DebugEvent_Execution) isDebugEvent_What() {}

func (*DebugEvent_GraphExecutionTrace) isDebugEvent_What() {}

func (*DebugEvent_GraphId) isDebugEvent_What() {}

func (*DebugEvent_DebuggedDevice) isDebugEvent_What() {}

// Metadata about the debugger and the debugged TensorFlow program.
type DebugMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Version of TensorFlow.
	TensorflowVersion string `protobuf:"bytes,1,opt,name=tensorflow_version,json=tensorflowVersion,proto3" json:"tensorflow_version,omitempty"`
	// Version of the DebugEvent file format.
	// Has a format of "debug.Event:<number>", e.g., "debug.Event:1".
	FileVersion string `protobuf:"bytes,2,opt,name=file_version,json=fileVersion,proto3" json:"file_version,omitempty"`
	// A unique ID for the current run of tfdbg.
	// A run of tfdbg is defined as a TensorFlow job instrumented by tfdbg.
	// Multiple hosts in a distributed TensorFlow job instrumented by tfdbg
	// have the same ID.
	TfdbgRunId string `protobuf:"bytes,3,opt,name=tfdbg_run_id,json=tfdbgRunId,proto3" json:"tfdbg_run_id,omitempty"`
}

func (x *DebugMetadata) Reset() {
	*x = DebugMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebugMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugMetadata) ProtoMessage() {}

func (x *DebugMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebugMetadata.ProtoReflect.Descriptor instead.
func (*DebugMetadata) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_debug_event_proto_rawDescGZIP(), []int{1}
}

func (x *DebugMetadata) GetTensorflowVersion() string {
	if x != nil {
		return x.TensorflowVersion
	}
	return ""
}

func (x *DebugMetadata) GetFileVersion() string {
	if x != nil {
		return x.FileVersion
	}
	return ""
}

func (x *DebugMetadata) GetTfdbgRunId() string {
	if x != nil {
		return x.TfdbgRunId
	}
	return ""
}

// Content of a source file involved in the execution of the debugged TensorFlow
// program.
type SourceFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Path to the file.
	FilePath string `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	// Name of the host on which the file is located.
	HostName string `protobuf:"bytes,2,opt,name=host_name,json=hostName,proto3" json:"host_name,omitempty"`
	// Line-by-line content of the file.
	Lines []string `protobuf:"bytes,3,rep,name=lines,proto3" json:"lines,omitempty"`
}

func (x *SourceFile) Reset() {
	*x = SourceFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceFile) ProtoMessage() {}

func (x *SourceFile) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceFile.ProtoReflect.Descriptor instead.
func (*SourceFile) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_debug_event_proto_rawDescGZIP(), []int{2}
}

func (x *SourceFile) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *SourceFile) GetHostName() string {
	if x != nil {
		return x.HostName
	}
	return ""
}

func (x *SourceFile) GetLines() []string {
	if x != nil {
		return x.Lines
	}
	return nil
}

// A stack frame with ID.
type StackFrameWithId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A unique ID for the stack frame: A UUID-like string.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Stack frame, i.e., a frame of a stack trace, containing information
	// regarding the file name, line number, function name, code content
	// of the line, and column number (if available).
	FileLineCol *GraphDebugInfo_FileLineCol `protobuf:"bytes,2,opt,name=file_line_col,json=fileLineCol,proto3" json:"file_line_col,omitempty"`
}

func (x *StackFrameWithId) Reset() {
	*x = StackFrameWithId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StackFrameWithId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StackFrameWithId) ProtoMessage() {}

func (x *StackFrameWithId) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StackFrameWithId.ProtoReflect.Descriptor instead.
func (*StackFrameWithId) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_debug_event_proto_rawDescGZIP(), []int{3}
}

func (x *StackFrameWithId) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StackFrameWithId) GetFileLineCol() *GraphDebugInfo_FileLineCol {
	if x != nil {
		return x.FileLineCol
	}
	return nil
}

// Code location information: A stack trace with host-name information.
// Instead of encoding the detailed stack trace, this proto refers to IDs of
// stack frames stored as `StackFrameWithId` protos.
type CodeLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Host name on which the source files are located.
	HostName string `protobuf:"bytes,1,opt,name=host_name,json=hostName,proto3" json:"host_name,omitempty"`
	// ID to a stack frame, each of which is pointed to
	// by a unique ID. The ordering of the frames is consistent with Python's
	// `traceback.extract_tb()`.
	StackFrameIds []string `protobuf:"bytes,2,rep,name=stack_frame_ids,json=stackFrameIds,proto3" json:"stack_frame_ids,omitempty"`
}

func (x *CodeLocation) Reset() {
	*x = CodeLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CodeLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeLocation) ProtoMessage() {}

func (x *CodeLocation) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeLocation.ProtoReflect.Descriptor instead.
func (*CodeLocation) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_debug_event_proto_rawDescGZIP(), []int{4}
}

func (x *CodeLocation) GetHostName() string {
	if x != nil {
		return x.HostName
	}
	return ""
}

func (x *CodeLocation) GetStackFrameIds() []string {
	if x != nil {
		return x.StackFrameIds
	}
	return nil
}

// The creation of an op in a TensorFlow Graph (e.g., FuncGraph in TF2).
type GraphOpCreation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type of the op (e.g., "MatMul").
	OpType string `protobuf:"bytes,1,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	// Name of the op (e.g., "Dense/MatMul_1").
	OpName string `protobuf:"bytes,2,opt,name=op_name,json=opName,proto3" json:"op_name,omitempty"`
	// Name of the graph that the op is a part of (if available).
	GraphName string `protobuf:"bytes,3,opt,name=graph_name,json=graphName,proto3" json:"graph_name,omitempty"`
	// Unique ID of the graph (generated by debugger).
	// This is the ID of the immediately-enclosing graph.
	GraphId string `protobuf:"bytes,4,opt,name=graph_id,json=graphId,proto3" json:"graph_id,omitempty"`
	// Name of the device that the op is assigned to (if available).
	DeviceName string `protobuf:"bytes,5,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`
	// Names of the input tensors to the op.
	InputNames []string `protobuf:"bytes,6,rep,name=input_names,json=inputNames,proto3" json:"input_names,omitempty"`
	// Number of output tensors emitted by the op.
	NumOutputs int32 `protobuf:"varint,7,opt,name=num_outputs,json=numOutputs,proto3" json:"num_outputs,omitempty"`
	// The unique ID for code location (stack trace) of the op's creation.
	CodeLocation *CodeLocation `protobuf:"bytes,8,opt,name=code_location,json=codeLocation,proto3" json:"code_location,omitempty"`
	// Unique IDs for the output tensors of this op.
	OutputTensorIds []int32 `protobuf:"varint,9,rep,packed,name=output_tensor_ids,json=outputTensorIds,proto3" json:"output_tensor_ids,omitempty"`
}

func (x *GraphOpCreation) Reset() {
	*x = GraphOpCreation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GraphOpCreation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GraphOpCreation) ProtoMessage() {}

func (x *GraphOpCreation) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GraphOpCreation.ProtoReflect.Descriptor instead.
func (*GraphOpCreation) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_debug_event_proto_rawDescGZIP(), []int{5}
}

func (x *GraphOpCreation) GetOpType() string {
	if x != nil {
		return x.OpType
	}
	return ""
}

func (x *GraphOpCreation) GetOpName() string {
	if x != nil {
		return x.OpName
	}
	return ""
}

func (x *GraphOpCreation) GetGraphName() string {
	if x != nil {
		return x.GraphName
	}
	return ""
}

func (x *GraphOpCreation) GetGraphId() string {
	if x != nil {
		return x.GraphId
	}
	return ""
}

func (x *GraphOpCreation) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *GraphOpCreation) GetInputNames() []string {
	if x != nil {
		return x.InputNames
	}
	return nil
}

func (x *GraphOpCreation) GetNumOutputs() int32 {
	if x != nil {
		return x.NumOutputs
	}
	return 0
}

func (x *GraphOpCreation) GetCodeLocation() *CodeLocation {
	if x != nil {
		return x.CodeLocation
	}
	return nil
}

func (x *GraphOpCreation) GetOutputTensorIds() []int32 {
	if x != nil {
		return x.OutputTensorIds
	}
	return nil
}

// A debugger-instrumented graph.
type DebuggedGraph struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// An ID for the graph.
	// This can be used up to look up graph names. Generated by the debugger.
	GraphId string `protobuf:"bytes,1,opt,name=graph_id,json=graphId,proto3" json:"graph_id,omitempty"`
	// Name of the graph (if available).
	GraphName string `protobuf:"bytes,2,opt,name=graph_name,json=graphName,proto3" json:"graph_name,omitempty"`
	// Names of the instrumented ops. This can be used to look up op name
	// based on the numeric-summary tensors (2nd column).
	InstrumentedOps []string `protobuf:"bytes,3,rep,name=instrumented_ops,json=instrumentedOps,proto3" json:"instrumented_ops,omitempty"`
	// Original (uninstrumented) GraphDef (if available).
	OriginalGraphDef []byte `protobuf:"bytes,4,opt,name=original_graph_def,json=originalGraphDef,proto3" json:"original_graph_def,omitempty"`
	// An encoded version of a GraphDef.
	// This graph may include the debugger-inserted ops.
	InstrumentedGraphDef []byte `protobuf:"bytes,5,opt,name=instrumented_graph_def,json=instrumentedGraphDef,proto3" json:"instrumented_graph_def,omitempty"`
	// IDs of the immediate enclosing context (graph), if any.
	OuterContextId string `protobuf:"bytes,6,opt,name=outer_context_id,json=outerContextId,proto3" json:"outer_context_id,omitempty"`
}

func (x *DebuggedGraph) Reset() {
	*x = DebuggedGraph{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebuggedGraph) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebuggedGraph) ProtoMessage() {}

func (x *DebuggedGraph) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebuggedGraph.ProtoReflect.Descriptor instead.
func (*DebuggedGraph) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_debug_event_proto_rawDescGZIP(), []int{6}
}

func (x *DebuggedGraph) GetGraphId() string {
	if x != nil {
		return x.GraphId
	}
	return ""
}

func (x *DebuggedGraph) GetGraphName() string {
	if x != nil {
		return x.GraphName
	}
	return ""
}

func (x *DebuggedGraph) GetInstrumentedOps() []string {
	if x != nil {
		return x.InstrumentedOps
	}
	return nil
}

func (x *DebuggedGraph) GetOriginalGraphDef() []byte {
	if x != nil {
		return x.OriginalGraphDef
	}
	return nil
}

func (x *DebuggedGraph) GetInstrumentedGraphDef() []byte {
	if x != nil {
		return x.InstrumentedGraphDef
	}
	return nil
}

func (x *DebuggedGraph) GetOuterContextId() string {
	if x != nil {
		return x.OuterContextId
	}
	return ""
}

// A device on which ops and/or tensors are instrumented by the debugger.
type DebuggedDevice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name of the device.
	DeviceName string `protobuf:"bytes,1,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`
	// A debugger-generated ID for the device. Guaranteed to be unique within
	// the scope of the debugged TensorFlow program, including single-host and
	// multi-host settings.
	// TODO(cais): Test the uniqueness guarantee in multi-host settings.
	DeviceId int32 `protobuf:"varint,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
}

func (x *DebuggedDevice) Reset() {
	*x = DebuggedDevice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebuggedDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebuggedDevice) ProtoMessage() {}

func (x *DebuggedDevice) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebuggedDevice.ProtoReflect.Descriptor instead.
func (*DebuggedDevice) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_debug_event_proto_rawDescGZIP(), []int{7}
}

func (x *DebuggedDevice) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *DebuggedDevice) GetDeviceId() int32 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

// Data relating to the eager execution of an op or a Graph.
// For a op that generates N output tensors (N >= 0), only one
// Execution proto will be used to describe the execution event.
type Execution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Op type (e.g., "MatMul").
	// In the case of a Graph, this is the name of the Graph.
	OpType string `protobuf:"bytes,1,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	// Number of output tensors.
	NumOutputs int32 `protobuf:"varint,2,opt,name=num_outputs,json=numOutputs,proto3" json:"num_outputs,omitempty"`
	// The graph that's executed: applicable only to the eager
	// execution of a FuncGraph.
	GraphId string `protobuf:"bytes,3,opt,name=graph_id,json=graphId,proto3" json:"graph_id,omitempty"`
	// IDs of the input tensors (if available).
	InputTensorIds []int64 `protobuf:"varint,4,rep,packed,name=input_tensor_ids,json=inputTensorIds,proto3" json:"input_tensor_ids,omitempty"`
	// IDs of the output tensors (if availbable).
	// If specified, must have the same length as tensor_protos.
	OutputTensorIds []int64 `protobuf:"varint,5,rep,packed,name=output_tensor_ids,json=outputTensorIds,proto3" json:"output_tensor_ids,omitempty"`
	// Type of the tensor value encapsulated in this proto.
	TensorDebugMode TensorDebugMode `protobuf:"varint,6,opt,name=tensor_debug_mode,json=tensorDebugMode,proto3,enum=tensorflow.TensorDebugMode" json:"tensor_debug_mode,omitempty"`
	// Output Tensor values in the type described by `tensor_value_type`.
	// The length of this should match `num_outputs`.
	TensorProtos []*tensor_go_proto.TensorProto `protobuf:"bytes,7,rep,name=tensor_protos,json=tensorProtos,proto3" json:"tensor_protos,omitempty"`
	// Stack trace of the eager execution.
	CodeLocation *CodeLocation `protobuf:"bytes,8,opt,name=code_location,json=codeLocation,proto3" json:"code_location,omitempty"`
	// Debugged-generated IDs of the devices on which the output tensors reside.
	// To look up details about the device (e.g., name), cross-reference this
	// field with the DebuggedDevice messages.
	OutputTensorDeviceIds []int32 `protobuf:"varint,9,rep,packed,name=output_tensor_device_ids,json=outputTensorDeviceIds,proto3" json:"output_tensor_device_ids,omitempty"`
}

func (x *Execution) Reset() {
	*x = Execution{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Execution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Execution) ProtoMessage() {}

func (x *Execution) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Execution.ProtoReflect.Descriptor instead.
func (*Execution) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_debug_event_proto_rawDescGZIP(), []int{8}
}

func (x *Execution) GetOpType() string {
	if x != nil {
		return x.OpType
	}
	return ""
}

func (x *Execution) GetNumOutputs() int32 {
	if x != nil {
		return x.NumOutputs
	}
	return 0
}

func (x *Execution) GetGraphId() string {
	if x != nil {
		return x.GraphId
	}
	return ""
}

func (x *Execution) GetInputTensorIds() []int64 {
	if x != nil {
		return x.InputTensorIds
	}
	return nil
}

func (x *Execution) GetOutputTensorIds() []int64 {
	if x != nil {
		return x.OutputTensorIds
	}
	return nil
}

func (x *Execution) GetTensorDebugMode() TensorDebugMode {
	if x != nil {
		return x.TensorDebugMode
	}
	return TensorDebugMode_UNSPECIFIED
}

func (x *Execution) GetTensorProtos() []*tensor_go_proto.TensorProto {
	if x != nil {
		return x.TensorProtos
	}
	return nil
}

func (x *Execution) GetCodeLocation() *CodeLocation {
	if x != nil {
		return x.CodeLocation
	}
	return nil
}

func (x *Execution) GetOutputTensorDeviceIds() []int32 {
	if x != nil {
		return x.OutputTensorDeviceIds
	}
	return nil
}

// Data relating to an execution of a Graph (e.g., an eager execution of a
// FuncGraph).
// The values of the intermediate tensors computed in the graph are recorded
// in this proto. A graph execution may correspond to one or more pieces of
// `GraphExecutionTrace`, depending on whether the instrumented tensor values
// are summarized in an aggregated or separate fashion.
type GraphExecutionTrace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique ID of the context that the executed op(s) belong to (e.g., a
	// compiled concrete tf.function).
	TfdbgContextId string `protobuf:"bytes,1,opt,name=tfdbg_context_id,json=tfdbgContextId,proto3" json:"tfdbg_context_id,omitempty"`
	// Name of the op (applicable only in the case of the `FULL_TENSOR` trace
	// level).
	OpName string `protobuf:"bytes,2,opt,name=op_name,json=opName,proto3" json:"op_name,omitempty"`
	// Output slot of the tensor (applicable only in the case of the `FULL_TENSOR`
	// trace level).
	OutputSlot int32 `protobuf:"varint,3,opt,name=output_slot,json=outputSlot,proto3" json:"output_slot,omitempty"`
	// Type of the tensor value encapsulated in this proto.
	TensorDebugMode TensorDebugMode `protobuf:"varint,4,opt,name=tensor_debug_mode,json=tensorDebugMode,proto3,enum=tensorflow.TensorDebugMode" json:"tensor_debug_mode,omitempty"`
	// Tensor value in the type described by `tensor_value_type`.
	// This tensor may summarize the value of a single intermediate op of the
	// graph, or those of multiple intermediate tensors.
	TensorProto *tensor_go_proto.TensorProto `protobuf:"bytes,5,opt,name=tensor_proto,json=tensorProto,proto3" json:"tensor_proto,omitempty"`
	// Name of the device that the op belongs to.
	DeviceName string `protobuf:"bytes,6,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`
}

func (x *GraphExecutionTrace) Reset() {
	*x = GraphExecutionTrace{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GraphExecutionTrace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GraphExecutionTrace) ProtoMessage() {}

func (x *GraphExecutionTrace) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_debug_event_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GraphExecutionTrace.ProtoReflect.Descriptor instead.
func (*GraphExecutionTrace) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_debug_event_proto_rawDescGZIP(), []int{9}
}

func (x *GraphExecutionTrace) GetTfdbgContextId() string {
	if x != nil {
		return x.TfdbgContextId
	}
	return ""
}

func (x *GraphExecutionTrace) GetOpName() string {
	if x != nil {
		return x.OpName
	}
	return ""
}

func (x *GraphExecutionTrace) GetOutputSlot() int32 {
	if x != nil {
		return x.OutputSlot
	}
	return 0
}

func (x *GraphExecutionTrace) GetTensorDebugMode() TensorDebugMode {
	if x != nil {
		return x.TensorDebugMode
	}
	return TensorDebugMode_UNSPECIFIED
}

func (x *GraphExecutionTrace) GetTensorProto() *tensor_go_proto.TensorProto {
	if x != nil {
		return x.TensorProto
	}
	return nil
}

func (x *GraphExecutionTrace) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

var File_tensorflow_core_protobuf_debug_event_proto protoreflect.FileDescriptor

var file_tensorflow_core_protobuf_debug_event_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x62, 0x75, 0x67,
	0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x1a, 0x26, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77,
	0x6f, 0x72, 0x6b, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x67, 0x72, 0x61, 0x70, 0x68,
	0x5f, 0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x94, 0x05, 0x0a, 0x0a, 0x44, 0x65, 0x62, 0x75, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x77, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x08, 0x77, 0x61, 0x6c, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x74, 0x65,
	0x70, 0x12, 0x42, 0x0a, 0x0e, 0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0d, 0x64, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x12, 0x4d, 0x0a, 0x13, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x5f,
	0x77, 0x69, 0x74, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x61, 0x63, 0x6b,
	0x46, 0x72, 0x61, 0x6d, 0x65, 0x57, 0x69, 0x74, 0x68, 0x49, 0x64, 0x48, 0x00, 0x52, 0x10, 0x73,
	0x74, 0x61, 0x63, 0x6b, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x57, 0x69, 0x74, 0x68, 0x49, 0x64, 0x12,
	0x49, 0x0a, 0x11, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x6f, 0x70, 0x5f, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x4f, 0x70, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0f, 0x67, 0x72, 0x61, 0x70, 0x68,
	0x4f, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x0e, 0x64, 0x65,
	0x62, 0x75, 0x67, 0x67, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x44, 0x65, 0x62, 0x75, 0x67, 0x67, 0x65, 0x64, 0x47, 0x72, 0x61, 0x70, 0x68, 0x48, 0x00, 0x52,
	0x0d, 0x64, 0x65, 0x62, 0x75, 0x67, 0x67, 0x65, 0x64, 0x47, 0x72, 0x61, 0x70, 0x68, 0x12, 0x35,
	0x0a, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x55, 0x0a, 0x15, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x72, 0x61, 0x63, 0x65, 0x48, 0x00, 0x52, 0x13, 0x67, 0x72, 0x61, 0x70, 0x68, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x08,
	0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x07, 0x67, 0x72, 0x61, 0x70, 0x68, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0f, 0x64, 0x65, 0x62,
	0x75, 0x67, 0x67, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x44, 0x65, 0x62, 0x75, 0x67, 0x67, 0x65, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x48, 0x00,
	0x52, 0x0e, 0x64, 0x65, 0x62, 0x75, 0x67, 0x67, 0x65, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x42, 0x06, 0x0a, 0x04, 0x77, 0x68, 0x61, 0x74, 0x22, 0x83, 0x01, 0x0a, 0x0d, 0x44, 0x65, 0x62,
	0x75, 0x67, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x12, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x66, 0x69, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0c,
	0x74, 0x66, 0x64, 0x62, 0x67, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x74, 0x66, 0x64, 0x62, 0x67, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x22, 0x5c,
	0x0a, 0x0a, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x6f, 0x73,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x22, 0x6e, 0x0a, 0x10,
	0x53, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x57, 0x69, 0x74, 0x68, 0x49, 0x64,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x4a, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x63, 0x6f,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x44, 0x65, 0x62, 0x75, 0x67, 0x49,
	0x6e, 0x66, 0x6f, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6c, 0x52,
	0x0b, 0x66, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6c, 0x22, 0x53, 0x0a, 0x0c,
	0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09,
	0x68, 0x6f, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x68, 0x6f, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x74, 0x61,
	0x63, 0x6b, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x64,
	0x73, 0x22, 0xcb, 0x02, 0x0a, 0x0f, 0x47, 0x72, 0x61, 0x70, 0x68, 0x4f, 0x70, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x6f, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x70, 0x68,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x61,
	0x70, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x61, 0x70, 0x68, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x75, 0x6d, 0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x63, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0f,
	0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x49, 0x64, 0x73, 0x22,
	0x82, 0x02, 0x0a, 0x0d, 0x44, 0x65, 0x62, 0x75, 0x67, 0x67, 0x65, 0x64, 0x47, 0x72, 0x61, 0x70,
	0x68, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x61, 0x70, 0x68, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x67, 0x72, 0x61, 0x70, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x69,
	0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x70, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x65, 0x64, 0x4f, 0x70, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x61, 0x6c, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x10, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x47, 0x72, 0x61, 0x70,
	0x68, 0x44, 0x65, 0x66, 0x12, 0x34, 0x0a, 0x16, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x14, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x65, 0x64, 0x47, 0x72, 0x61, 0x70, 0x68, 0x44, 0x65, 0x66, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x75,
	0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x0e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x67, 0x65, 0x64,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x22, 0xb5, 0x03, 0x0a, 0x09, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6e,
	0x75, 0x6d, 0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x19, 0x0a, 0x08,
	0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x67, 0x72, 0x61, 0x70, 0x68, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x5f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0e, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x49, 0x64,
	0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0f, 0x6f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x49, 0x64, 0x73, 0x12, 0x47, 0x0a,
	0x11, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x65, 0x62, 0x75,
	0x67, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x65, 0x62,
	0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x3c, 0x0a, 0x0d, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x0c, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x63, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x18, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x05, 0x52, 0x15, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x22, 0x9f, 0x02, 0x0a,
	0x13, 0x47, 0x72, 0x61, 0x70, 0x68, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x72, 0x61, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x66, 0x64, 0x62, 0x67, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x74, 0x66, 0x64, 0x62, 0x67, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x6f, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x47, 0x0a, 0x11, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x5f, 0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x0f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x52, 0x0b, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x0a,
	0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x2a, 0xb6,
	0x01, 0x0a, 0x0f, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x5f, 0x54, 0x45, 0x4e, 0x53, 0x4f, 0x52,
	0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x55, 0x52, 0x54, 0x5f, 0x48, 0x45, 0x41, 0x4c, 0x54,
	0x48, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x4f, 0x4e, 0x43, 0x49, 0x53, 0x45, 0x5f, 0x48,
	0x45, 0x41, 0x4c, 0x54, 0x48, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x55, 0x4c, 0x4c, 0x5f,
	0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x48, 0x41, 0x50,
	0x45, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x4e, 0x55, 0x4d, 0x45,
	0x52, 0x49, 0x43, 0x53, 0x10, 0x06, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x54,
	0x45, 0x4e, 0x53, 0x4f, 0x52, 0x10, 0x07, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x44, 0x55, 0x43,
	0x45, 0x5f, 0x49, 0x4e, 0x46, 0x5f, 0x4e, 0x41, 0x4e, 0x5f, 0x54, 0x48, 0x52, 0x45, 0x45, 0x5f,
	0x53, 0x4c, 0x4f, 0x54, 0x53, 0x10, 0x08, 0x42, 0x83, 0x01, 0x0a, 0x13, 0x6f, 0x72, 0x67, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x42,
	0x10, 0x44, 0x65, 0x62, 0x75, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x73, 0x50, 0x01, 0x5a, 0x55, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2f, 0x67, 0x6f, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x66, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x73, 0x5f, 0x67, 0x6f, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0xf8, 0x01, 0x01, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tensorflow_core_protobuf_debug_event_proto_rawDescOnce sync.Once
	file_tensorflow_core_protobuf_debug_event_proto_rawDescData = file_tensorflow_core_protobuf_debug_event_proto_rawDesc
)

func file_tensorflow_core_protobuf_debug_event_proto_rawDescGZIP() []byte {
	file_tensorflow_core_protobuf_debug_event_proto_rawDescOnce.Do(func() {
		file_tensorflow_core_protobuf_debug_event_proto_rawDescData = protoimpl.X.CompressGZIP(file_tensorflow_core_protobuf_debug_event_proto_rawDescData)
	})
	return file_tensorflow_core_protobuf_debug_event_proto_rawDescData
}

var file_tensorflow_core_protobuf_debug_event_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tensorflow_core_protobuf_debug_event_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_tensorflow_core_protobuf_debug_event_proto_goTypes = []interface{}{
	(TensorDebugMode)(0),                // 0: tensorflow.TensorDebugMode
	(*DebugEvent)(nil),                  // 1: tensorflow.DebugEvent
	(*DebugMetadata)(nil),               // 2: tensorflow.DebugMetadata
	(*SourceFile)(nil),                  // 3: tensorflow.SourceFile
	(*StackFrameWithId)(nil),            // 4: tensorflow.StackFrameWithId
	(*CodeLocation)(nil),                // 5: tensorflow.CodeLocation
	(*GraphOpCreation)(nil),             // 6: tensorflow.GraphOpCreation
	(*DebuggedGraph)(nil),               // 7: tensorflow.DebuggedGraph
	(*DebuggedDevice)(nil),              // 8: tensorflow.DebuggedDevice
	(*Execution)(nil),                   // 9: tensorflow.Execution
	(*GraphExecutionTrace)(nil),         // 10: tensorflow.GraphExecutionTrace
	(*GraphDebugInfo_FileLineCol)(nil),  // 11: tensorflow.GraphDebugInfo.FileLineCol
	(*tensor_go_proto.TensorProto)(nil), // 12: tensorflow.TensorProto
}
var file_tensorflow_core_protobuf_debug_event_proto_depIdxs = []int32{
	2,  // 0: tensorflow.DebugEvent.debug_metadata:type_name -> tensorflow.DebugMetadata
	3,  // 1: tensorflow.DebugEvent.source_file:type_name -> tensorflow.SourceFile
	4,  // 2: tensorflow.DebugEvent.stack_frame_with_id:type_name -> tensorflow.StackFrameWithId
	6,  // 3: tensorflow.DebugEvent.graph_op_creation:type_name -> tensorflow.GraphOpCreation
	7,  // 4: tensorflow.DebugEvent.debugged_graph:type_name -> tensorflow.DebuggedGraph
	9,  // 5: tensorflow.DebugEvent.execution:type_name -> tensorflow.Execution
	10, // 6: tensorflow.DebugEvent.graph_execution_trace:type_name -> tensorflow.GraphExecutionTrace
	8,  // 7: tensorflow.DebugEvent.debugged_device:type_name -> tensorflow.DebuggedDevice
	11, // 8: tensorflow.StackFrameWithId.file_line_col:type_name -> tensorflow.GraphDebugInfo.FileLineCol
	5,  // 9: tensorflow.GraphOpCreation.code_location:type_name -> tensorflow.CodeLocation
	0,  // 10: tensorflow.Execution.tensor_debug_mode:type_name -> tensorflow.TensorDebugMode
	12, // 11: tensorflow.Execution.tensor_protos:type_name -> tensorflow.TensorProto
	5,  // 12: tensorflow.Execution.code_location:type_name -> tensorflow.CodeLocation
	0,  // 13: tensorflow.GraphExecutionTrace.tensor_debug_mode:type_name -> tensorflow.TensorDebugMode
	12, // 14: tensorflow.GraphExecutionTrace.tensor_proto:type_name -> tensorflow.TensorProto
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_tensorflow_core_protobuf_debug_event_proto_init() }
func file_tensorflow_core_protobuf_debug_event_proto_init() {
	if File_tensorflow_core_protobuf_debug_event_proto != nil {
		return
	}
	file_tensorflow_core_protobuf_graph_debug_info_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tensorflow_core_protobuf_debug_event_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebugEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_debug_event_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebugMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_debug_event_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SourceFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_debug_event_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StackFrameWithId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_debug_event_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CodeLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_debug_event_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GraphOpCreation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_debug_event_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebuggedGraph); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_debug_event_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebuggedDevice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_debug_event_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Execution); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_debug_event_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GraphExecutionTrace); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_tensorflow_core_protobuf_debug_event_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*DebugEvent_DebugMetadata)(nil),
		(*DebugEvent_SourceFile)(nil),
		(*DebugEvent_StackFrameWithId)(nil),
		(*DebugEvent_GraphOpCreation)(nil),
		(*DebugEvent_DebuggedGraph)(nil),
		(*DebugEvent_Execution)(nil),
		(*DebugEvent_GraphExecutionTrace)(nil),
		(*DebugEvent_GraphId)(nil),
		(*DebugEvent_DebuggedDevice)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tensorflow_core_protobuf_debug_event_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_core_protobuf_debug_event_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_protobuf_debug_event_proto_depIdxs,
		EnumInfos:         file_tensorflow_core_protobuf_debug_event_proto_enumTypes,
		MessageInfos:      file_tensorflow_core_protobuf_debug_event_proto_msgTypes,
	}.Build()
	File_tensorflow_core_protobuf_debug_event_proto = out.File
	file_tensorflow_core_protobuf_debug_event_proto_rawDesc = nil
	file_tensorflow_core_protobuf_debug_event_proto_goTypes = nil
	file_tensorflow_core_protobuf_debug_event_proto_depIdxs = nil
}
