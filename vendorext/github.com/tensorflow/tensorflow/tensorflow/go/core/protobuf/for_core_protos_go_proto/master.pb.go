// Copyright 2016 The TensorFlow Authors. All Rights Reserved.
//
//Licensed under the Apache License, Version 2.0 (the "License");
//you may not use this file except in compliance with the License.
//You may obtain a copy of the License at
//
//http://www.apache.org/licenses/LICENSE-2.0
//
//Unless required by applicable law or agreed to in writing, software
//distributed under the License is distributed on an "AS IS" BASIS,
//WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//See the License for the specific language governing permissions and
//limitations under the License.
//==============================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: tensorflow/core/protobuf/master.proto

package for_core_protos_go_proto

import (
	device_attributes_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/device_attributes_go_proto"
	graph_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/graph_go_proto"
	tensor_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The initial graph definition.
	GraphDef *graph_go_proto.GraphDef `protobuf:"bytes,1,opt,name=graph_def,json=graphDef,proto3" json:"graph_def,omitempty"`
	// Configuration options.
	Config *ConfigProto `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
	// The target string used from the client's perspective.
	Target string `protobuf:"bytes,3,opt,name=target,proto3" json:"target,omitempty"`
}

func (x *CreateSessionRequest) Reset() {
	*x = CreateSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSessionRequest) ProtoMessage() {}

func (x *CreateSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSessionRequest.ProtoReflect.Descriptor instead.
func (*CreateSessionRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{0}
}

func (x *CreateSessionRequest) GetGraphDef() *graph_go_proto.GraphDef {
	if x != nil {
		return x.GraphDef
	}
	return nil
}

func (x *CreateSessionRequest) GetConfig() *ConfigProto {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *CreateSessionRequest) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

type CreateSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The session handle to be used in subsequent calls for the created session.
	//
	// The client must arrange to call CloseSession with this returned
	// session handle to close the session.
	SessionHandle string `protobuf:"bytes,1,opt,name=session_handle,json=sessionHandle,proto3" json:"session_handle,omitempty"`
	// The initial version number for the graph, to be used in the next call
	// to ExtendSession.
	GraphVersion int64 `protobuf:"varint,2,opt,name=graph_version,json=graphVersion,proto3" json:"graph_version,omitempty"`
}

func (x *CreateSessionResponse) Reset() {
	*x = CreateSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSessionResponse) ProtoMessage() {}

func (x *CreateSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSessionResponse.ProtoReflect.Descriptor instead.
func (*CreateSessionResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{1}
}

func (x *CreateSessionResponse) GetSessionHandle() string {
	if x != nil {
		return x.SessionHandle
	}
	return ""
}

func (x *CreateSessionResponse) GetGraphVersion() int64 {
	if x != nil {
		return x.GraphVersion
	}
	return 0
}

type ExtendSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// REQUIRED: session_handle must be returned by a CreateSession call
	// to the same master service.
	SessionHandle string `protobuf:"bytes,1,opt,name=session_handle,json=sessionHandle,proto3" json:"session_handle,omitempty"`
	// REQUIRED: The nodes to be added to the session's graph. If any node has
	// the same name as an existing node, the operation will fail with
	// ILLEGAL_ARGUMENT.
	GraphDef *graph_go_proto.GraphDef `protobuf:"bytes,2,opt,name=graph_def,json=graphDef,proto3" json:"graph_def,omitempty"`
	// REQUIRED: The version number of the graph to be extended. This will be
	// tested against the current server-side version number, and the operation
	// will fail with FAILED_PRECONDITION if they do not match.
	CurrentGraphVersion int64 `protobuf:"varint,3,opt,name=current_graph_version,json=currentGraphVersion,proto3" json:"current_graph_version,omitempty"`
}

func (x *ExtendSessionRequest) Reset() {
	*x = ExtendSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtendSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtendSessionRequest) ProtoMessage() {}

func (x *ExtendSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtendSessionRequest.ProtoReflect.Descriptor instead.
func (*ExtendSessionRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{2}
}

func (x *ExtendSessionRequest) GetSessionHandle() string {
	if x != nil {
		return x.SessionHandle
	}
	return ""
}

func (x *ExtendSessionRequest) GetGraphDef() *graph_go_proto.GraphDef {
	if x != nil {
		return x.GraphDef
	}
	return nil
}

func (x *ExtendSessionRequest) GetCurrentGraphVersion() int64 {
	if x != nil {
		return x.CurrentGraphVersion
	}
	return 0
}

type ExtendSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The new version number for the extended graph, to be used in the next call
	// to ExtendSession.
	NewGraphVersion int64 `protobuf:"varint,4,opt,name=new_graph_version,json=newGraphVersion,proto3" json:"new_graph_version,omitempty"`
}

func (x *ExtendSessionResponse) Reset() {
	*x = ExtendSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtendSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtendSessionResponse) ProtoMessage() {}

func (x *ExtendSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtendSessionResponse.ProtoReflect.Descriptor instead.
func (*ExtendSessionResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{3}
}

func (x *ExtendSessionResponse) GetNewGraphVersion() int64 {
	if x != nil {
		return x.NewGraphVersion
	}
	return 0
}

type RunStepRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// REQUIRED: session_handle must be returned by a CreateSession call
	// to the same master service.
	SessionHandle string `protobuf:"bytes,1,opt,name=session_handle,json=sessionHandle,proto3" json:"session_handle,omitempty"`
	// Tensors to be fed in the step. Each feed is a named tensor.
	Feed []*NamedTensorProto `protobuf:"bytes,2,rep,name=feed,proto3" json:"feed,omitempty"`
	// Fetches. A list of tensor names. The caller expects a tensor to
	// be returned for each fetch[i] (see RunStepResponse.tensor). The
	// order of specified fetches does not change the execution order.
	Fetch []string `protobuf:"bytes,3,rep,name=fetch,proto3" json:"fetch,omitempty"`
	// Target Nodes. A list of node names. The named nodes will be run
	// to but their outputs will not be fetched.
	Target []string `protobuf:"bytes,4,rep,name=target,proto3" json:"target,omitempty"`
	// Options for the run call.
	Options *RunOptions `protobuf:"bytes,5,opt,name=options,proto3" json:"options,omitempty"`
	// Partial run handle (optional). If specified, this will be a partial run
	// execution, run up to the specified fetches.
	PartialRunHandle string `protobuf:"bytes,6,opt,name=partial_run_handle,json=partialRunHandle,proto3" json:"partial_run_handle,omitempty"`
	// If true then some errors, e.g., execution errors that have long
	// error messages, may return an OK RunStepResponse with the actual
	// error saved in the status_code/status_error_message fields of the
	// response body. This is a workaround since the RPC subsystem may
	// truncate long metadata messages.
	StoreErrorsInResponseBody bool `protobuf:"varint,7,opt,name=store_errors_in_response_body,json=storeErrorsInResponseBody,proto3" json:"store_errors_in_response_body,omitempty"`
	// Unique identifier for this request. Every RunStepRequest must
	// have a unique request_id, and retried RunStepRequest must have
	// the same request_id. If request_id is zero, retry detection is disabled.
	RequestId int64 `protobuf:"varint,8,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *RunStepRequest) Reset() {
	*x = RunStepRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunStepRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunStepRequest) ProtoMessage() {}

func (x *RunStepRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunStepRequest.ProtoReflect.Descriptor instead.
func (*RunStepRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{4}
}

func (x *RunStepRequest) GetSessionHandle() string {
	if x != nil {
		return x.SessionHandle
	}
	return ""
}

func (x *RunStepRequest) GetFeed() []*NamedTensorProto {
	if x != nil {
		return x.Feed
	}
	return nil
}

func (x *RunStepRequest) GetFetch() []string {
	if x != nil {
		return x.Fetch
	}
	return nil
}

func (x *RunStepRequest) GetTarget() []string {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *RunStepRequest) GetOptions() *RunOptions {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *RunStepRequest) GetPartialRunHandle() string {
	if x != nil {
		return x.PartialRunHandle
	}
	return ""
}

func (x *RunStepRequest) GetStoreErrorsInResponseBody() bool {
	if x != nil {
		return x.StoreErrorsInResponseBody
	}
	return false
}

func (x *RunStepRequest) GetRequestId() int64 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

type RunStepResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// NOTE: The order of the returned tensors may or may not match
	// the fetch order specified in RunStepRequest.
	Tensor []*NamedTensorProto `protobuf:"bytes,1,rep,name=tensor,proto3" json:"tensor,omitempty"`
	// Returned metadata if requested in the options.
	Metadata *RunMetadata `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// If store_errors_in_response_body is true in the request, then
	// optionally the server may return an OK status for the RPC and
	// fill the true status into the fields below, to allow for messages
	// that are too long to fit in metadata.
	StatusCode         Code   `protobuf:"varint,3,opt,name=status_code,json=statusCode,proto3,enum=tensorflow.error.Code" json:"status_code,omitempty"`
	StatusErrorMessage string `protobuf:"bytes,4,opt,name=status_error_message,json=statusErrorMessage,proto3" json:"status_error_message,omitempty"`
}

func (x *RunStepResponse) Reset() {
	*x = RunStepResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunStepResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunStepResponse) ProtoMessage() {}

func (x *RunStepResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunStepResponse.ProtoReflect.Descriptor instead.
func (*RunStepResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{5}
}

func (x *RunStepResponse) GetTensor() []*NamedTensorProto {
	if x != nil {
		return x.Tensor
	}
	return nil
}

func (x *RunStepResponse) GetMetadata() *RunMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *RunStepResponse) GetStatusCode() Code {
	if x != nil {
		return x.StatusCode
	}
	return Code_OK
}

func (x *RunStepResponse) GetStatusErrorMessage() string {
	if x != nil {
		return x.StatusErrorMessage
	}
	return ""
}

type PartialRunSetupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// REQUIRED: session_handle must be returned by a CreateSession call
	// to the same master service.
	SessionHandle string `protobuf:"bytes,1,opt,name=session_handle,json=sessionHandle,proto3" json:"session_handle,omitempty"`
	// Tensors to be fed in future steps.
	Feed []string `protobuf:"bytes,2,rep,name=feed,proto3" json:"feed,omitempty"`
	// Fetches. A list of tensor names. The caller expects a tensor to be returned
	// for each fetch[i] (see RunStepResponse.tensor), for corresponding partial
	// RunStepRequests. The order of specified fetches does not change the
	// execution order.
	Fetch []string `protobuf:"bytes,3,rep,name=fetch,proto3" json:"fetch,omitempty"`
	// Target Nodes. A list of node names. The named nodes will be run in future
	// steps, but their outputs will not be fetched.
	Target []string `protobuf:"bytes,4,rep,name=target,proto3" json:"target,omitempty"`
	// Unique identifier for this request. Every PartialRunSetupRequest must
	// have a unique request_id, and retried PartialRunSetupRequest must have
	// the same request_id. If request_id is zero, retry detection is disabled.
	RequestId int64 `protobuf:"varint,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *PartialRunSetupRequest) Reset() {
	*x = PartialRunSetupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartialRunSetupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartialRunSetupRequest) ProtoMessage() {}

func (x *PartialRunSetupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartialRunSetupRequest.ProtoReflect.Descriptor instead.
func (*PartialRunSetupRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{6}
}

func (x *PartialRunSetupRequest) GetSessionHandle() string {
	if x != nil {
		return x.SessionHandle
	}
	return ""
}

func (x *PartialRunSetupRequest) GetFeed() []string {
	if x != nil {
		return x.Feed
	}
	return nil
}

func (x *PartialRunSetupRequest) GetFetch() []string {
	if x != nil {
		return x.Fetch
	}
	return nil
}

func (x *PartialRunSetupRequest) GetTarget() []string {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *PartialRunSetupRequest) GetRequestId() int64 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

type PartialRunSetupResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique handle corresponding to the ongoing partial run call setup by
	// the invocation to PartialRunSetup. This handle may be passed to
	// RunStepRequest to send and receive tensors for this partial run.
	PartialRunHandle string `protobuf:"bytes,1,opt,name=partial_run_handle,json=partialRunHandle,proto3" json:"partial_run_handle,omitempty"`
}

func (x *PartialRunSetupResponse) Reset() {
	*x = PartialRunSetupResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartialRunSetupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartialRunSetupResponse) ProtoMessage() {}

func (x *PartialRunSetupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartialRunSetupResponse.ProtoReflect.Descriptor instead.
func (*PartialRunSetupResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{7}
}

func (x *PartialRunSetupResponse) GetPartialRunHandle() string {
	if x != nil {
		return x.PartialRunHandle
	}
	return ""
}

type CloseSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// REQUIRED: session_handle must be returned by a CreateSession call
	// to the same master service.
	SessionHandle string `protobuf:"bytes,1,opt,name=session_handle,json=sessionHandle,proto3" json:"session_handle,omitempty"`
}

func (x *CloseSessionRequest) Reset() {
	*x = CloseSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseSessionRequest) ProtoMessage() {}

func (x *CloseSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseSessionRequest.ProtoReflect.Descriptor instead.
func (*CloseSessionRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{8}
}

func (x *CloseSessionRequest) GetSessionHandle() string {
	if x != nil {
		return x.SessionHandle
	}
	return ""
}

type CloseSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CloseSessionResponse) Reset() {
	*x = CloseSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseSessionResponse) ProtoMessage() {}

func (x *CloseSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseSessionResponse.ProtoReflect.Descriptor instead.
func (*CloseSessionResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{9}
}

// Reset() allows misbehaving or slow sessions to be aborted and closed, and
// causes their resources eventually to be released.  Reset() does not wait
// for the computations in old sessions to cease; it merely starts the
// process of tearing them down.  However, if a new session is started after
// a Reset(), the new session is isolated from changes that old sessions
// (started prior to the Reset()) may continue to make to resources, provided
// all those resources are in containers listed in "containers".
//
// Old sessions may continue to have side-effects on resources not in
// containers listed in "containers", and thus may affect future
// sessions' results in ways that are hard to predict.  Thus, if well-defined
// behavior is desired, is it recommended that all containers be listed in
// "containers".  Similarly, if a device_filter is specified, results may be
// hard to predict.
type ResetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of container names, which may be empty.
	//
	// If 'container' is not empty, releases resources in the given
	// containers in all devices.
	//
	// If 'container' is empty, releases resources in the default
	// container in all devices.
	Container []string `protobuf:"bytes,1,rep,name=container,proto3" json:"container,omitempty"`
	// When any filters are present, only devices that match the filters
	// will be reset. Each filter can be partially specified,
	// e.g. "/job:ps" "/job:worker/replica:3", etc.
	DeviceFilters []string `protobuf:"bytes,2,rep,name=device_filters,json=deviceFilters,proto3" json:"device_filters,omitempty"`
}

func (x *ResetRequest) Reset() {
	*x = ResetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetRequest) ProtoMessage() {}

func (x *ResetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetRequest.ProtoReflect.Descriptor instead.
func (*ResetRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{10}
}

func (x *ResetRequest) GetContainer() []string {
	if x != nil {
		return x.Container
	}
	return nil
}

func (x *ResetRequest) GetDeviceFilters() []string {
	if x != nil {
		return x.DeviceFilters
	}
	return nil
}

type ResetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ResetResponse) Reset() {
	*x = ResetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetResponse) ProtoMessage() {}

func (x *ResetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetResponse.ProtoReflect.Descriptor instead.
func (*ResetResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{11}
}

type ListDevicesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional: session_handle must be returned by a CreateSession call to the
	// same master service.
	//
	// When session_handle is empty, the ClusterSpec provided when the master was
	// started is used to compute the available devices. If the session_handle is
	// provided but not recognized, an error is returned. Finally, if a valid
	// session_handle is provided, the cluster configuration for that session is
	// used when computing the response.
	SessionHandle string `protobuf:"bytes,1,opt,name=session_handle,json=sessionHandle,proto3" json:"session_handle,omitempty"`
}

func (x *ListDevicesRequest) Reset() {
	*x = ListDevicesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDevicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDevicesRequest) ProtoMessage() {}

func (x *ListDevicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDevicesRequest.ProtoReflect.Descriptor instead.
func (*ListDevicesRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{12}
}

func (x *ListDevicesRequest) GetSessionHandle() string {
	if x != nil {
		return x.SessionHandle
	}
	return ""
}

type ListDevicesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LocalDevice  []*device_attributes_go_proto.DeviceAttributes `protobuf:"bytes,1,rep,name=local_device,json=localDevice,proto3" json:"local_device,omitempty"`
	RemoteDevice []*device_attributes_go_proto.DeviceAttributes `protobuf:"bytes,2,rep,name=remote_device,json=remoteDevice,proto3" json:"remote_device,omitempty"`
}

func (x *ListDevicesResponse) Reset() {
	*x = ListDevicesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDevicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDevicesResponse) ProtoMessage() {}

func (x *ListDevicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDevicesResponse.ProtoReflect.Descriptor instead.
func (*ListDevicesResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{13}
}

func (x *ListDevicesResponse) GetLocalDevice() []*device_attributes_go_proto.DeviceAttributes {
	if x != nil {
		return x.LocalDevice
	}
	return nil
}

func (x *ListDevicesResponse) GetRemoteDevice() []*device_attributes_go_proto.DeviceAttributes {
	if x != nil {
		return x.RemoteDevice
	}
	return nil
}

type MakeCallableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// REQUIRED: session_handle must be returned by a CreateSession call
	// to the same master service.
	SessionHandle string `protobuf:"bytes,1,opt,name=session_handle,json=sessionHandle,proto3" json:"session_handle,omitempty"`
	// Options that define the behavior of the created callable.
	Options *CallableOptions `protobuf:"bytes,2,opt,name=options,proto3" json:"options,omitempty"`
	// Unique identifier for this request. Every MakeCallableRequest must
	// have a unique request_id, and retried MakeCallableRequest must have
	// the same request_id. If request_id is zero, retry detection is disabled.
	RequestId int64 `protobuf:"varint,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *MakeCallableRequest) Reset() {
	*x = MakeCallableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MakeCallableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeCallableRequest) ProtoMessage() {}

func (x *MakeCallableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeCallableRequest.ProtoReflect.Descriptor instead.
func (*MakeCallableRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{14}
}

func (x *MakeCallableRequest) GetSessionHandle() string {
	if x != nil {
		return x.SessionHandle
	}
	return ""
}

func (x *MakeCallableRequest) GetOptions() *CallableOptions {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *MakeCallableRequest) GetRequestId() int64 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

type MakeCallableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A handle to the created callable.
	Handle int64 `protobuf:"varint,1,opt,name=handle,proto3" json:"handle,omitempty"`
}

func (x *MakeCallableResponse) Reset() {
	*x = MakeCallableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MakeCallableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeCallableResponse) ProtoMessage() {}

func (x *MakeCallableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeCallableResponse.ProtoReflect.Descriptor instead.
func (*MakeCallableResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{15}
}

func (x *MakeCallableResponse) GetHandle() int64 {
	if x != nil {
		return x.Handle
	}
	return 0
}

type RunCallableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// REQUIRED: session_handle must be returned by a CreateSession call
	// to the same master service.
	SessionHandle string `protobuf:"bytes,1,opt,name=session_handle,json=sessionHandle,proto3" json:"session_handle,omitempty"`
	// REQUIRED: handle must be returned by a MakeCallable call to the same
	// master service.
	Handle int64 `protobuf:"varint,2,opt,name=handle,proto3" json:"handle,omitempty"`
	// Values of the tensors passed as arguments to the callable, in the order
	// defined in the CallableOptions.feed field passed to MakeCallable.
	Feed []*tensor_go_proto.TensorProto `protobuf:"bytes,3,rep,name=feed,proto3" json:"feed,omitempty"`
	// Unique identifier for this request. Every RunCallableRequest must
	// have a unique request_id, and retried RunCallableRequest must have
	// the same request_id. If request_id is zero, retry detection is disabled.
	RequestId int64 `protobuf:"varint,4,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *RunCallableRequest) Reset() {
	*x = RunCallableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunCallableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunCallableRequest) ProtoMessage() {}

func (x *RunCallableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunCallableRequest.ProtoReflect.Descriptor instead.
func (*RunCallableRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{16}
}

func (x *RunCallableRequest) GetSessionHandle() string {
	if x != nil {
		return x.SessionHandle
	}
	return ""
}

func (x *RunCallableRequest) GetHandle() int64 {
	if x != nil {
		return x.Handle
	}
	return 0
}

func (x *RunCallableRequest) GetFeed() []*tensor_go_proto.TensorProto {
	if x != nil {
		return x.Feed
	}
	return nil
}

func (x *RunCallableRequest) GetRequestId() int64 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

type RunCallableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Values of the tensors returned by the callable, in the order defined in the
	// CallableOptions.fetch field passed to MakeCallable.
	Fetch []*tensor_go_proto.TensorProto `protobuf:"bytes,1,rep,name=fetch,proto3" json:"fetch,omitempty"`
	// Returned metadata if requested in the options.
	Metadata *RunMetadata `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *RunCallableResponse) Reset() {
	*x = RunCallableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunCallableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunCallableResponse) ProtoMessage() {}

func (x *RunCallableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunCallableResponse.ProtoReflect.Descriptor instead.
func (*RunCallableResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{17}
}

func (x *RunCallableResponse) GetFetch() []*tensor_go_proto.TensorProto {
	if x != nil {
		return x.Fetch
	}
	return nil
}

func (x *RunCallableResponse) GetMetadata() *RunMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type ReleaseCallableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// REQUIRED: session_handle must be returned by a CreateSession call
	// to the same master service.
	SessionHandle string `protobuf:"bytes,1,opt,name=session_handle,json=sessionHandle,proto3" json:"session_handle,omitempty"`
	// REQUIRED: handle must be returned by a MakeCallable call to the same
	// master service.
	Handle int64 `protobuf:"varint,2,opt,name=handle,proto3" json:"handle,omitempty"`
}

func (x *ReleaseCallableRequest) Reset() {
	*x = ReleaseCallableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleaseCallableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseCallableRequest) ProtoMessage() {}

func (x *ReleaseCallableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseCallableRequest.ProtoReflect.Descriptor instead.
func (*ReleaseCallableRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{18}
}

func (x *ReleaseCallableRequest) GetSessionHandle() string {
	if x != nil {
		return x.SessionHandle
	}
	return ""
}

func (x *ReleaseCallableRequest) GetHandle() int64 {
	if x != nil {
		return x.Handle
	}
	return 0
}

type ReleaseCallableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReleaseCallableResponse) Reset() {
	*x = ReleaseCallableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleaseCallableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseCallableResponse) ProtoMessage() {}

func (x *ReleaseCallableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_master_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseCallableResponse.ProtoReflect.Descriptor instead.
func (*ReleaseCallableResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_master_proto_rawDescGZIP(), []int{19}
}

var File_tensorflow_core_protobuf_master_proto protoreflect.FileDescriptor

var file_tensorflow_core_protobuf_master_proto_rawDesc = []byte{
	0x0a, 0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x6d, 0x61, 0x73, 0x74, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x1a, 0x31, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f,
	0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72,
	0x6b, 0x2f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66,
	0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x92, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31,
	0x0a, 0x09, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x44, 0x65, 0x66, 0x52, 0x08, 0x67, 0x72, 0x61, 0x70, 0x68, 0x44, 0x65,
	0x66, 0x12, 0x2f, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x22, 0x63, 0x0a, 0x15, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x68,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x67, 0x72,
	0x61, 0x70, 0x68, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x67, 0x72, 0x61, 0x70, 0x68, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0xa4, 0x01, 0x0a, 0x14, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12,
	0x31, 0x0a, 0x09, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x47, 0x72, 0x61, 0x70, 0x68, 0x44, 0x65, 0x66, 0x52, 0x08, 0x67, 0x72, 0x61, 0x70, 0x68, 0x44,
	0x65, 0x66, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72,
	0x61, 0x70, 0x68, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x13, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x61, 0x70, 0x68, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x43, 0x0a, 0x15, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2a, 0x0a, 0x11, 0x6e, 0x65, 0x77, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6e, 0x65, 0x77, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xd8, 0x02, 0x0a, 0x0e,
	0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25,
	0x0a, 0x0e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x66, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x52, 0x04, 0x66, 0x65, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x66, 0x65, 0x74, 0x63, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x30, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x75, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x07,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x61, 0x72, 0x74, 0x69,
	0x61, 0x6c, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x75, 0x6e, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x40, 0x0a, 0x1d, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0xe7, 0x01, 0x0a, 0x0f, 0x52, 0x75, 0x6e, 0x53, 0x74,
	0x65, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x54, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x12, 0x33, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x52, 0x75, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30,
	0x0a, 0x14, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x22, 0xa0, 0x01, 0x0a, 0x16, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x75, 0x6e, 0x53,
	0x65, 0x74, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x04, 0x66, 0x65, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x65, 0x74, 0x63, 0x68, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x66, 0x65, 0x74, 0x63, 0x68, 0x12, 0x16, 0x0a, 0x06,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x22, 0x47, 0x0a, 0x17, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x75,
	0x6e, 0x53, 0x65, 0x74, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c,
	0x0a, 0x12, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x68, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x61, 0x72, 0x74,
	0x69, 0x61, 0x6c, 0x52, 0x75, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x22, 0x3c, 0x0a, 0x13,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x68,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x6c,
	0x6f, 0x73, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x53, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0x0f, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3b, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25,
	0x0a, 0x0e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x22, 0x99, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a,
	0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x73, 0x52, 0x0b, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x41,
	0x0a, 0x0d, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x73, 0x52, 0x0c, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x22, 0x92, 0x01, 0x0a, 0x13, 0x4d, 0x61, 0x6b, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x12, 0x35, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43,
	0x61, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x07,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x2e, 0x0a, 0x14, 0x4d, 0x61, 0x6b, 0x65, 0x43, 0x61,
	0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x22, 0x9f, 0x01, 0x0a, 0x12, 0x52, 0x75, 0x6e, 0x43, 0x61,
	0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a,
	0x0e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x2b, 0x0a, 0x04,
	0x66, 0x65, 0x65, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x52, 0x04, 0x66, 0x65, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x79, 0x0a, 0x13, 0x52, 0x75, 0x6e, 0x43,
	0x61, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2d, 0x0a, 0x05, 0x66, 0x65, 0x74, 0x63, 0x68, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x05, 0x66, 0x65, 0x74, 0x63, 0x68, 0x12, 0x33,
	0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x75,
	0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x22, 0x57, 0x0a, 0x16, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x43, 0x61,
	0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a,
	0x0e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x22, 0x19, 0x0a, 0x17,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x92, 0x01, 0x0a, 0x1a, 0x6f, 0x72, 0x67, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x64, 0x69, 0x73, 0x74, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x18, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x64, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x73,
	0x50, 0x01, 0x5a, 0x55, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f,
	0x67, 0x6f, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x66, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73,
	0x5f, 0x67, 0x6f, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0xf8, 0x01, 0x01, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tensorflow_core_protobuf_master_proto_rawDescOnce sync.Once
	file_tensorflow_core_protobuf_master_proto_rawDescData = file_tensorflow_core_protobuf_master_proto_rawDesc
)

func file_tensorflow_core_protobuf_master_proto_rawDescGZIP() []byte {
	file_tensorflow_core_protobuf_master_proto_rawDescOnce.Do(func() {
		file_tensorflow_core_protobuf_master_proto_rawDescData = protoimpl.X.CompressGZIP(file_tensorflow_core_protobuf_master_proto_rawDescData)
	})
	return file_tensorflow_core_protobuf_master_proto_rawDescData
}

var file_tensorflow_core_protobuf_master_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_tensorflow_core_protobuf_master_proto_goTypes = []interface{}{
	(*CreateSessionRequest)(nil),                        // 0: tensorflow.CreateSessionRequest
	(*CreateSessionResponse)(nil),                       // 1: tensorflow.CreateSessionResponse
	(*ExtendSessionRequest)(nil),                        // 2: tensorflow.ExtendSessionRequest
	(*ExtendSessionResponse)(nil),                       // 3: tensorflow.ExtendSessionResponse
	(*RunStepRequest)(nil),                              // 4: tensorflow.RunStepRequest
	(*RunStepResponse)(nil),                             // 5: tensorflow.RunStepResponse
	(*PartialRunSetupRequest)(nil),                      // 6: tensorflow.PartialRunSetupRequest
	(*PartialRunSetupResponse)(nil),                     // 7: tensorflow.PartialRunSetupResponse
	(*CloseSessionRequest)(nil),                         // 8: tensorflow.CloseSessionRequest
	(*CloseSessionResponse)(nil),                        // 9: tensorflow.CloseSessionResponse
	(*ResetRequest)(nil),                                // 10: tensorflow.ResetRequest
	(*ResetResponse)(nil),                               // 11: tensorflow.ResetResponse
	(*ListDevicesRequest)(nil),                          // 12: tensorflow.ListDevicesRequest
	(*ListDevicesResponse)(nil),                         // 13: tensorflow.ListDevicesResponse
	(*MakeCallableRequest)(nil),                         // 14: tensorflow.MakeCallableRequest
	(*MakeCallableResponse)(nil),                        // 15: tensorflow.MakeCallableResponse
	(*RunCallableRequest)(nil),                          // 16: tensorflow.RunCallableRequest
	(*RunCallableResponse)(nil),                         // 17: tensorflow.RunCallableResponse
	(*ReleaseCallableRequest)(nil),                      // 18: tensorflow.ReleaseCallableRequest
	(*ReleaseCallableResponse)(nil),                     // 19: tensorflow.ReleaseCallableResponse
	(*graph_go_proto.GraphDef)(nil),                     // 20: tensorflow.GraphDef
	(*ConfigProto)(nil),                                 // 21: tensorflow.ConfigProto
	(*NamedTensorProto)(nil),                            // 22: tensorflow.NamedTensorProto
	(*RunOptions)(nil),                                  // 23: tensorflow.RunOptions
	(*RunMetadata)(nil),                                 // 24: tensorflow.RunMetadata
	(Code)(0),                                           // 25: tensorflow.error.Code
	(*device_attributes_go_proto.DeviceAttributes)(nil), // 26: tensorflow.DeviceAttributes
	(*CallableOptions)(nil),                             // 27: tensorflow.CallableOptions
	(*tensor_go_proto.TensorProto)(nil),                 // 28: tensorflow.TensorProto
}
var file_tensorflow_core_protobuf_master_proto_depIdxs = []int32{
	20, // 0: tensorflow.CreateSessionRequest.graph_def:type_name -> tensorflow.GraphDef
	21, // 1: tensorflow.CreateSessionRequest.config:type_name -> tensorflow.ConfigProto
	20, // 2: tensorflow.ExtendSessionRequest.graph_def:type_name -> tensorflow.GraphDef
	22, // 3: tensorflow.RunStepRequest.feed:type_name -> tensorflow.NamedTensorProto
	23, // 4: tensorflow.RunStepRequest.options:type_name -> tensorflow.RunOptions
	22, // 5: tensorflow.RunStepResponse.tensor:type_name -> tensorflow.NamedTensorProto
	24, // 6: tensorflow.RunStepResponse.metadata:type_name -> tensorflow.RunMetadata
	25, // 7: tensorflow.RunStepResponse.status_code:type_name -> tensorflow.error.Code
	26, // 8: tensorflow.ListDevicesResponse.local_device:type_name -> tensorflow.DeviceAttributes
	26, // 9: tensorflow.ListDevicesResponse.remote_device:type_name -> tensorflow.DeviceAttributes
	27, // 10: tensorflow.MakeCallableRequest.options:type_name -> tensorflow.CallableOptions
	28, // 11: tensorflow.RunCallableRequest.feed:type_name -> tensorflow.TensorProto
	28, // 12: tensorflow.RunCallableResponse.fetch:type_name -> tensorflow.TensorProto
	24, // 13: tensorflow.RunCallableResponse.metadata:type_name -> tensorflow.RunMetadata
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_tensorflow_core_protobuf_master_proto_init() }
func file_tensorflow_core_protobuf_master_proto_init() {
	if File_tensorflow_core_protobuf_master_proto != nil {
		return
	}
	file_tensorflow_core_protobuf_config_proto_init()
	file_tensorflow_core_protobuf_error_codes_proto_init()
	file_tensorflow_core_protobuf_named_tensor_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tensorflow_core_protobuf_master_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtendSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtendSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunStepRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunStepResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartialRunSetupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartialRunSetupResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDevicesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDevicesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MakeCallableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MakeCallableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunCallableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunCallableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleaseCallableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_master_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleaseCallableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tensorflow_core_protobuf_master_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_core_protobuf_master_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_protobuf_master_proto_depIdxs,
		MessageInfos:      file_tensorflow_core_protobuf_master_proto_msgTypes,
	}.Build()
	File_tensorflow_core_protobuf_master_proto = out.File
	file_tensorflow_core_protobuf_master_proto_rawDesc = nil
	file_tensorflow_core_protobuf_master_proto_goTypes = nil
	file_tensorflow_core_protobuf_master_proto_depIdxs = nil
}
