// Copyright 2016 The TensorFlow Authors. All Rights Reserved.
//
//Licensed under the Apache License, Version 2.0 (the "License");
//you may not use this file except in compliance with the License.
//You may obtain a copy of the License at
//
//http://www.apache.org/licenses/LICENSE-2.0
//
//Unless required by applicable law or agreed to in writing, software
//distributed under the License is distributed on an "AS IS" BASIS,
//WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//See the License for the specific language governing permissions and
//limitations under the License.
//==============================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: tensorflow/core/protobuf/worker.proto

package for_core_protos_go_proto

import (
	cost_graph_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/cost_graph_go_proto"
	device_attributes_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/device_attributes_go_proto"
	graph_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/graph_go_proto"
	step_stats_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/step_stats_go_proto"
	tensor_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto"
	tensor_shape_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto"
	types_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetStatusRequest) Reset() {
	*x = GetStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStatusRequest) ProtoMessage() {}

func (x *GetStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStatusRequest.ProtoReflect.Descriptor instead.
func (*GetStatusRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{0}
}

type GetStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceAttributes []*device_attributes_go_proto.DeviceAttributes `protobuf:"bytes,1,rep,name=device_attributes,json=deviceAttributes,proto3" json:"device_attributes,omitempty"`
}

func (x *GetStatusResponse) Reset() {
	*x = GetStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStatusResponse) ProtoMessage() {}

func (x *GetStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStatusResponse.ProtoReflect.Descriptor instead.
func (*GetStatusResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{1}
}

func (x *GetStatusResponse) GetDeviceAttributes() []*device_attributes_go_proto.DeviceAttributes {
	if x != nil {
		return x.DeviceAttributes
	}
	return nil
}

type CreateWorkerSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Sessions are identified by a given handle.
	SessionHandle string `protobuf:"bytes,1,opt,name=session_handle,json=sessionHandle,proto3" json:"session_handle,omitempty"`
	// Defines the configuration of a TensorFlow worker.
	ServerDef *ServerDef `protobuf:"bytes,2,opt,name=server_def,json=serverDef,proto3" json:"server_def,omitempty"`
	// If true, any resources such as Variables used in the session will not be
	// shared with other sessions.
	IsolateSessionState bool `protobuf:"varint,3,opt,name=isolate_session_state,json=isolateSessionState,proto3" json:"isolate_session_state,omitempty"`
	// The device attributes of all the devices in the cluster.
	ClusterDeviceAttributes []*device_attributes_go_proto.DeviceAttributes `protobuf:"bytes,4,rep,name=cluster_device_attributes,json=clusterDeviceAttributes,proto3" json:"cluster_device_attributes,omitempty"`
	// The master task name from which the request is sent.
	MasterTask string `protobuf:"bytes,5,opt,name=master_task,json=masterTask,proto3" json:"master_task,omitempty"`
	// The incarnation ID of the master task local CPU device.
	// If the target worker already has a WorkerSession created previously with
	// the same master task name but a different incarnation, it usually indicates
	// that the previous master failed before deleting the WorkerSession on the
	// worker. To prevent memory leaks, the worker should garbage collect the old
	// WorkerSessions.
	MasterIncarnation int64 `protobuf:"varint,6,opt,name=master_incarnation,json=masterIncarnation,proto3" json:"master_incarnation,omitempty"`
	// Configures coordination service within worker sessions.
	CoordinationServiceConfig *CoordinationServiceConfig `protobuf:"bytes,7,opt,name=coordination_service_config,json=coordinationServiceConfig,proto3" json:"coordination_service_config,omitempty"`
}

func (x *CreateWorkerSessionRequest) Reset() {
	*x = CreateWorkerSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWorkerSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkerSessionRequest) ProtoMessage() {}

func (x *CreateWorkerSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkerSessionRequest.ProtoReflect.Descriptor instead.
func (*CreateWorkerSessionRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{2}
}

func (x *CreateWorkerSessionRequest) GetSessionHandle() string {
	if x != nil {
		return x.SessionHandle
	}
	return ""
}

func (x *CreateWorkerSessionRequest) GetServerDef() *ServerDef {
	if x != nil {
		return x.ServerDef
	}
	return nil
}

func (x *CreateWorkerSessionRequest) GetIsolateSessionState() bool {
	if x != nil {
		return x.IsolateSessionState
	}
	return false
}

func (x *CreateWorkerSessionRequest) GetClusterDeviceAttributes() []*device_attributes_go_proto.DeviceAttributes {
	if x != nil {
		return x.ClusterDeviceAttributes
	}
	return nil
}

func (x *CreateWorkerSessionRequest) GetMasterTask() string {
	if x != nil {
		return x.MasterTask
	}
	return ""
}

func (x *CreateWorkerSessionRequest) GetMasterIncarnation() int64 {
	if x != nil {
		return x.MasterIncarnation
	}
	return 0
}

func (x *CreateWorkerSessionRequest) GetCoordinationServiceConfig() *CoordinationServiceConfig {
	if x != nil {
		return x.CoordinationServiceConfig
	}
	return nil
}

type CreateWorkerSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateWorkerSessionResponse) Reset() {
	*x = CreateWorkerSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWorkerSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkerSessionResponse) ProtoMessage() {}

func (x *CreateWorkerSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkerSessionResponse.ProtoReflect.Descriptor instead.
func (*CreateWorkerSessionResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{3}
}

type DeleteWorkerSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Sessions are identified by a given handle.
	SessionHandle string `protobuf:"bytes,1,opt,name=session_handle,json=sessionHandle,proto3" json:"session_handle,omitempty"`
}

func (x *DeleteWorkerSessionRequest) Reset() {
	*x = DeleteWorkerSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWorkerSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWorkerSessionRequest) ProtoMessage() {}

func (x *DeleteWorkerSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWorkerSessionRequest.ProtoReflect.Descriptor instead.
func (*DeleteWorkerSessionRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteWorkerSessionRequest) GetSessionHandle() string {
	if x != nil {
		return x.SessionHandle
	}
	return ""
}

type DeleteWorkerSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteWorkerSessionResponse) Reset() {
	*x = DeleteWorkerSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWorkerSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWorkerSessionResponse) ProtoMessage() {}

func (x *DeleteWorkerSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWorkerSessionResponse.ProtoReflect.Descriptor instead.
func (*DeleteWorkerSessionResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{5}
}

type RegisterGraphRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subgraphs are scoped within one session.
	SessionHandle string `protobuf:"bytes,1,opt,name=session_handle,json=sessionHandle,proto3" json:"session_handle,omitempty"`
	// Set to true if `CreateWorkerSession` was called for `session_handle`.
	CreateWorkerSessionCalled bool `protobuf:"varint,6,opt,name=create_worker_session_called,json=createWorkerSessionCalled,proto3" json:"create_worker_session_called,omitempty"`
	// "graph_def" has the subgraph of nodes for this worker, with each node
	// having its device_name filled in.
	GraphDef *graph_go_proto.GraphDef `protobuf:"bytes,2,opt,name=graph_def,json=graphDef,proto3" json:"graph_def,omitempty"`
	// True iff the graph (before partitioning) contains control flow nodes.
	//
	// As of 01/11/2015, this is no longer set by clients.
	//
	// Deprecated: Do not use.
	HasControlFlow bool `protobuf:"varint,3,opt,name=has_control_flow,json=hasControlFlow,proto3" json:"has_control_flow,omitempty"`
	// Configuration options for the session in which this graph was created.
	GraphOptions *GraphOptions `protobuf:"bytes,4,opt,name=graph_options,json=graphOptions,proto3" json:"graph_options,omitempty"`
	// Field(s) used by TensorFlow Debugger (tfdbg).
	DebugOptions *DebugOptions `protobuf:"bytes,5,opt,name=debug_options,json=debugOptions,proto3" json:"debug_options,omitempty"`
	// If graph_def contains any collective ops this must be a positive
	// integer used to coordinate execution with other graphs.  All
	// graphs in a distributed execution with the same
	// collective_graph_key will coordinate to use the same step_id
	// concurrently so that BufRendezvous entries will make the correct
	// values accessible.
	CollectiveGraphKey int64 `protobuf:"varint,7,opt,name=collective_graph_key,json=collectiveGraphKey,proto3" json:"collective_graph_key,omitempty"`
	// ConfigProto from the session in which this graph was created.
	// Contains additional parameters beyond graph_options, including
	// the name of the requested executor.
	ConfigProto *ConfigProto `protobuf:"bytes,8,opt,name=config_proto,json=configProto,proto3" json:"config_proto,omitempty"`
}

func (x *RegisterGraphRequest) Reset() {
	*x = RegisterGraphRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterGraphRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterGraphRequest) ProtoMessage() {}

func (x *RegisterGraphRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterGraphRequest.ProtoReflect.Descriptor instead.
func (*RegisterGraphRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{6}
}

func (x *RegisterGraphRequest) GetSessionHandle() string {
	if x != nil {
		return x.SessionHandle
	}
	return ""
}

func (x *RegisterGraphRequest) GetCreateWorkerSessionCalled() bool {
	if x != nil {
		return x.CreateWorkerSessionCalled
	}
	return false
}

func (x *RegisterGraphRequest) GetGraphDef() *graph_go_proto.GraphDef {
	if x != nil {
		return x.GraphDef
	}
	return nil
}

// Deprecated: Do not use.
func (x *RegisterGraphRequest) GetHasControlFlow() bool {
	if x != nil {
		return x.HasControlFlow
	}
	return false
}

func (x *RegisterGraphRequest) GetGraphOptions() *GraphOptions {
	if x != nil {
		return x.GraphOptions
	}
	return nil
}

func (x *RegisterGraphRequest) GetDebugOptions() *DebugOptions {
	if x != nil {
		return x.DebugOptions
	}
	return nil
}

func (x *RegisterGraphRequest) GetCollectiveGraphKey() int64 {
	if x != nil {
		return x.CollectiveGraphKey
	}
	return 0
}

func (x *RegisterGraphRequest) GetConfigProto() *ConfigProto {
	if x != nil {
		return x.ConfigProto
	}
	return nil
}

type RegisterGraphResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// If the registration succeeds, returns an opaque graph_handle to
	// the master. The master calls RunGraph with graph_handle to
	// compute different steps.
	GraphHandle string `protobuf:"bytes,1,opt,name=graph_handle,json=graphHandle,proto3" json:"graph_handle,omitempty"`
}

func (x *RegisterGraphResponse) Reset() {
	*x = RegisterGraphResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterGraphResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterGraphResponse) ProtoMessage() {}

func (x *RegisterGraphResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterGraphResponse.ProtoReflect.Descriptor instead.
func (*RegisterGraphResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{7}
}

func (x *RegisterGraphResponse) GetGraphHandle() string {
	if x != nil {
		return x.GraphHandle
	}
	return ""
}

type DeregisterGraphRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The session_handle used when registering the graph. If session_handle is
	// empty, a single global namespace is used.
	SessionHandle string `protobuf:"bytes,2,opt,name=session_handle,json=sessionHandle,proto3" json:"session_handle,omitempty"`
	// Set to true if `CreateWorkerSession` was called for `session_handle`.
	CreateWorkerSessionCalled bool `protobuf:"varint,3,opt,name=create_worker_session_called,json=createWorkerSessionCalled,proto3" json:"create_worker_session_called,omitempty"`
	// REQUIRED: graph_handle must be returned by a RegisterGraph call
	// to the same WorkerService.
	GraphHandle string `protobuf:"bytes,1,opt,name=graph_handle,json=graphHandle,proto3" json:"graph_handle,omitempty"`
}

func (x *DeregisterGraphRequest) Reset() {
	*x = DeregisterGraphRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeregisterGraphRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeregisterGraphRequest) ProtoMessage() {}

func (x *DeregisterGraphRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeregisterGraphRequest.ProtoReflect.Descriptor instead.
func (*DeregisterGraphRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{8}
}

func (x *DeregisterGraphRequest) GetSessionHandle() string {
	if x != nil {
		return x.SessionHandle
	}
	return ""
}

func (x *DeregisterGraphRequest) GetCreateWorkerSessionCalled() bool {
	if x != nil {
		return x.CreateWorkerSessionCalled
	}
	return false
}

func (x *DeregisterGraphRequest) GetGraphHandle() string {
	if x != nil {
		return x.GraphHandle
	}
	return ""
}

type DeregisterGraphResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeregisterGraphResponse) Reset() {
	*x = DeregisterGraphResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeregisterGraphResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeregisterGraphResponse) ProtoMessage() {}

func (x *DeregisterGraphResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeregisterGraphResponse.ProtoReflect.Descriptor instead.
func (*DeregisterGraphResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{9}
}

type CleanupAllRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of container names.
	//
	// If 'container' is not empty, releases resources in the given
	// containers in all devices.
	//
	// If 'container' is empty, releases resources in the default
	// container in all devices.
	Container []string `protobuf:"bytes,1,rep,name=container,proto3" json:"container,omitempty"`
}

func (x *CleanupAllRequest) Reset() {
	*x = CleanupAllRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CleanupAllRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanupAllRequest) ProtoMessage() {}

func (x *CleanupAllRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanupAllRequest.ProtoReflect.Descriptor instead.
func (*CleanupAllRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{10}
}

func (x *CleanupAllRequest) GetContainer() []string {
	if x != nil {
		return x.Container
	}
	return nil
}

type CleanupAllResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CleanupAllResponse) Reset() {
	*x = CleanupAllResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CleanupAllResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanupAllResponse) ProtoMessage() {}

func (x *CleanupAllResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanupAllResponse.ProtoReflect.Descriptor instead.
func (*CleanupAllResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{11}
}

// Options specific to the execution of a single step.
type ExecutorOpts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordCosts                    bool `protobuf:"varint,1,opt,name=record_costs,json=recordCosts,proto3" json:"record_costs,omitempty"`
	RecordTimeline                 bool `protobuf:"varint,3,opt,name=record_timeline,json=recordTimeline,proto3" json:"record_timeline,omitempty"`
	RecordPartitionGraphs          bool `protobuf:"varint,4,opt,name=record_partition_graphs,json=recordPartitionGraphs,proto3" json:"record_partition_graphs,omitempty"`
	ReportTensorAllocationsUponOom bool `protobuf:"varint,5,opt,name=report_tensor_allocations_upon_oom,json=reportTensorAllocationsUponOom,proto3" json:"report_tensor_allocations_upon_oom,omitempty"`
}

func (x *ExecutorOpts) Reset() {
	*x = ExecutorOpts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutorOpts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutorOpts) ProtoMessage() {}

func (x *ExecutorOpts) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutorOpts.ProtoReflect.Descriptor instead.
func (*ExecutorOpts) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{12}
}

func (x *ExecutorOpts) GetRecordCosts() bool {
	if x != nil {
		return x.RecordCosts
	}
	return false
}

func (x *ExecutorOpts) GetRecordTimeline() bool {
	if x != nil {
		return x.RecordTimeline
	}
	return false
}

func (x *ExecutorOpts) GetRecordPartitionGraphs() bool {
	if x != nil {
		return x.RecordPartitionGraphs
	}
	return false
}

func (x *ExecutorOpts) GetReportTensorAllocationsUponOom() bool {
	if x != nil {
		return x.ReportTensorAllocationsUponOom
	}
	return false
}

type RunGraphRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// session_handle is the master-generated unique id for this session.
	// If session_handle is non-empty, it must be the same as used when
	// registering the graph. If it is empty, a single global namespace is used to
	// search for the graph_handle.
	SessionHandle string `protobuf:"bytes,8,opt,name=session_handle,json=sessionHandle,proto3" json:"session_handle,omitempty"`
	// Set to true if `CreateWorkerSession` was called for `session_handle`.
	CreateWorkerSessionCalled bool `protobuf:"varint,10,opt,name=create_worker_session_called,json=createWorkerSessionCalled,proto3" json:"create_worker_session_called,omitempty"`
	// REQUIRED: graph_handle must be returned by a RegisterGraph call
	// to the same WorkerService.
	GraphHandle string `protobuf:"bytes,1,opt,name=graph_handle,json=graphHandle,proto3" json:"graph_handle,omitempty"`
	// A unique ID to distinguish different runs of the same graph.
	//
	// The master generates a global unique `step_id` to distinguish
	// different runs of the graph computation. Subgraphs communicate
	// (e.g., send/recv ops) with each other using `step_id` to
	// distinguish tensors generated by different runs.
	StepId int64 `protobuf:"varint,2,opt,name=step_id,json=stepId,proto3" json:"step_id,omitempty"`
	// Options for this step.
	ExecOpts *ExecutorOpts `protobuf:"bytes,5,opt,name=exec_opts,json=execOpts,proto3" json:"exec_opts,omitempty"`
	// Runs the graph.
	//
	// Sends the tensors in "send" into the graph before the run and
	// fetches the keys into `RunGraphResponse.recv` after the run.
	Send    []*NamedTensorProto `protobuf:"bytes,3,rep,name=send,proto3" json:"send,omitempty"`
	RecvKey []string            `protobuf:"bytes,4,rep,name=recv_key,json=recvKey,proto3" json:"recv_key,omitempty"`
	// True if the RunGraphRequest is a partial run request.
	IsPartial bool `protobuf:"varint,6,opt,name=is_partial,json=isPartial,proto3" json:"is_partial,omitempty"`
	// True if this is the last partial run request in a sequence of requests.
	IsLastPartialRun bool `protobuf:"varint,7,opt,name=is_last_partial_run,json=isLastPartialRun,proto3" json:"is_last_partial_run,omitempty"`
	// If true then some errors, e.g., execution errors that have long
	// error messages, may return an OK RunGraphResponse with the actual
	// error saved in the status_code/status_error_message fields of the
	// response body. This is a workaround since the RPC subsystem may
	// truncate long metadata messages.
	StoreErrorsInResponseBody bool `protobuf:"varint,9,opt,name=store_errors_in_response_body,json=storeErrorsInResponseBody,proto3" json:"store_errors_in_response_body,omitempty"`
	// Unique identifier for this request. Every RunGraphRequest must have a
	// unique request_id, and retried RunGraphRequests must have the same
	// request_id. If request_id is zero, retry detection is disabled.
	//
	// Retried RunGraphRequests are problematic because they may issue a
	// RecvTensor that will have no corresponding sender and will wait forever.
	// Workers use request_ids to reject retried RunGraph requests instead of
	// waiting forever.
	RequestId int64 `protobuf:"varint,11,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *RunGraphRequest) Reset() {
	*x = RunGraphRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunGraphRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunGraphRequest) ProtoMessage() {}

func (x *RunGraphRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunGraphRequest.ProtoReflect.Descriptor instead.
func (*RunGraphRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{13}
}

func (x *RunGraphRequest) GetSessionHandle() string {
	if x != nil {
		return x.SessionHandle
	}
	return ""
}

func (x *RunGraphRequest) GetCreateWorkerSessionCalled() bool {
	if x != nil {
		return x.CreateWorkerSessionCalled
	}
	return false
}

func (x *RunGraphRequest) GetGraphHandle() string {
	if x != nil {
		return x.GraphHandle
	}
	return ""
}

func (x *RunGraphRequest) GetStepId() int64 {
	if x != nil {
		return x.StepId
	}
	return 0
}

func (x *RunGraphRequest) GetExecOpts() *ExecutorOpts {
	if x != nil {
		return x.ExecOpts
	}
	return nil
}

func (x *RunGraphRequest) GetSend() []*NamedTensorProto {
	if x != nil {
		return x.Send
	}
	return nil
}

func (x *RunGraphRequest) GetRecvKey() []string {
	if x != nil {
		return x.RecvKey
	}
	return nil
}

func (x *RunGraphRequest) GetIsPartial() bool {
	if x != nil {
		return x.IsPartial
	}
	return false
}

func (x *RunGraphRequest) GetIsLastPartialRun() bool {
	if x != nil {
		return x.IsLastPartialRun
	}
	return false
}

func (x *RunGraphRequest) GetStoreErrorsInResponseBody() bool {
	if x != nil {
		return x.StoreErrorsInResponseBody
	}
	return false
}

func (x *RunGraphRequest) GetRequestId() int64 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

type RunGraphResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of tensors corresponding to those requested by
	// `RunGraphRequest.recv_key`.
	Recv []*NamedTensorProto `protobuf:"bytes,1,rep,name=recv,proto3" json:"recv,omitempty"`
	// If the request asked for execution stats, the cost graph, or the partition
	// graphs, these are returned here.
	// TODO(suharshs): Package these in a RunMetadata instead.
	StepStats      *step_stats_go_proto.StepStats    `protobuf:"bytes,2,opt,name=step_stats,json=stepStats,proto3" json:"step_stats,omitempty"`
	CostGraph      *cost_graph_go_proto.CostGraphDef `protobuf:"bytes,3,opt,name=cost_graph,json=costGraph,proto3" json:"cost_graph,omitempty"`
	PartitionGraph []*graph_go_proto.GraphDef        `protobuf:"bytes,4,rep,name=partition_graph,json=partitionGraph,proto3" json:"partition_graph,omitempty"`
	// If store_errors_in_response_body is true in the request, then
	// optionally the server may return an OK status for the RPC and
	// fill the true status into the fields below, to allow for messages
	// that are too long to fit in metadata.
	StatusCode         Code   `protobuf:"varint,5,opt,name=status_code,json=statusCode,proto3,enum=tensorflow.error.Code" json:"status_code,omitempty"`
	StatusErrorMessage string `protobuf:"bytes,6,opt,name=status_error_message,json=statusErrorMessage,proto3" json:"status_error_message,omitempty"`
}

func (x *RunGraphResponse) Reset() {
	*x = RunGraphResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunGraphResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunGraphResponse) ProtoMessage() {}

func (x *RunGraphResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunGraphResponse.ProtoReflect.Descriptor instead.
func (*RunGraphResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{14}
}

func (x *RunGraphResponse) GetRecv() []*NamedTensorProto {
	if x != nil {
		return x.Recv
	}
	return nil
}

func (x *RunGraphResponse) GetStepStats() *step_stats_go_proto.StepStats {
	if x != nil {
		return x.StepStats
	}
	return nil
}

func (x *RunGraphResponse) GetCostGraph() *cost_graph_go_proto.CostGraphDef {
	if x != nil {
		return x.CostGraph
	}
	return nil
}

func (x *RunGraphResponse) GetPartitionGraph() []*graph_go_proto.GraphDef {
	if x != nil {
		return x.PartitionGraph
	}
	return nil
}

func (x *RunGraphResponse) GetStatusCode() Code {
	if x != nil {
		return x.StatusCode
	}
	return Code_OK
}

func (x *RunGraphResponse) GetStatusErrorMessage() string {
	if x != nil {
		return x.StatusErrorMessage
	}
	return ""
}

type CleanupGraphRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StepId int64 `protobuf:"varint,1,opt,name=step_id,json=stepId,proto3" json:"step_id,omitempty"`
}

func (x *CleanupGraphRequest) Reset() {
	*x = CleanupGraphRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CleanupGraphRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanupGraphRequest) ProtoMessage() {}

func (x *CleanupGraphRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanupGraphRequest.ProtoReflect.Descriptor instead.
func (*CleanupGraphRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{15}
}

func (x *CleanupGraphRequest) GetStepId() int64 {
	if x != nil {
		return x.StepId
	}
	return 0
}

type CleanupGraphResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CleanupGraphResponse) Reset() {
	*x = CleanupGraphResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CleanupGraphResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanupGraphResponse) ProtoMessage() {}

func (x *CleanupGraphResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanupGraphResponse.ProtoReflect.Descriptor instead.
func (*CleanupGraphResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{16}
}

type RecvTensorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The step in which the tensor will be produced.
	//
	// REQUIRED: This must eventually correspond to the `step_id` passed
	// into a RunGraph call on the same WorkerService.
	StepId int64 `protobuf:"varint,1,opt,name=step_id,json=stepId,proto3" json:"step_id,omitempty"`
	// A key identifying the channel to receive tensors from. A RecvTensor request
	// retrieves one tensor from the channel, but multiple tensors can be sent and
	// received over the same channel with multiple RecvTensor requests. See
	// rendezvous.h for details.
	RendezvousKey string `protobuf:"bytes,2,opt,name=rendezvous_key,json=rendezvousKey,proto3" json:"rendezvous_key,omitempty"`
	// If true, use an out-of-band DMA mechanism to transfer the
	// received tensor.
	DmaOk bool `protobuf:"varint,3,opt,name=dma_ok,json=dmaOk,proto3" json:"dma_ok,omitempty"`
	// Optional information on client-side device locality.
	ClientLocality *device_attributes_go_proto.DeviceLocality `protobuf:"bytes,4,opt,name=client_locality,json=clientLocality,proto3" json:"client_locality,omitempty"`
	// Optional information on server-side device locality.
	ServerLocality *device_attributes_go_proto.DeviceLocality `protobuf:"bytes,5,opt,name=server_locality,json=serverLocality,proto3" json:"server_locality,omitempty"`
	// Optional information needed by the RPC subsystem.
	TransportOptions *anypb.Any `protobuf:"bytes,6,opt,name=transport_options,json=transportOptions,proto3" json:"transport_options,omitempty"`
	// Unique identifier for this request. Every RecvTensorRequest must have a
	// unique request_id, and retried RecvTensorRequests must have the same
	// request_id. If request_id is zero, retry detection and response cache
	// are disabled.
	//
	// Retried RecvTensorRequests are problematic because a RecvTensor with no
	// corresponding sender will wait forever, and the tensor may have been
	// delivered to a previous retry. Workers use request_ids to reject retried
	// RecvTensor requests instead of waiting forever.
	RequestId int64 `protobuf:"varint,7,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *RecvTensorRequest) Reset() {
	*x = RecvTensorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecvTensorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecvTensorRequest) ProtoMessage() {}

func (x *RecvTensorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecvTensorRequest.ProtoReflect.Descriptor instead.
func (*RecvTensorRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{17}
}

func (x *RecvTensorRequest) GetStepId() int64 {
	if x != nil {
		return x.StepId
	}
	return 0
}

func (x *RecvTensorRequest) GetRendezvousKey() string {
	if x != nil {
		return x.RendezvousKey
	}
	return ""
}

func (x *RecvTensorRequest) GetDmaOk() bool {
	if x != nil {
		return x.DmaOk
	}
	return false
}

func (x *RecvTensorRequest) GetClientLocality() *device_attributes_go_proto.DeviceLocality {
	if x != nil {
		return x.ClientLocality
	}
	return nil
}

func (x *RecvTensorRequest) GetServerLocality() *device_attributes_go_proto.DeviceLocality {
	if x != nil {
		return x.ServerLocality
	}
	return nil
}

func (x *RecvTensorRequest) GetTransportOptions() *anypb.Any {
	if x != nil {
		return x.TransportOptions
	}
	return nil
}

func (x *RecvTensorRequest) GetRequestId() int64 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

type RecvTensorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The tensor as a proto.
	Tensor *tensor_go_proto.TensorProto `protobuf:"bytes,1,opt,name=tensor,proto3" json:"tensor,omitempty"`
	// If true, this tensor was the output of a dead node, and the
	// content is invalid.
	IsDead bool `protobuf:"varint,2,opt,name=is_dead,json=isDead,proto3" json:"is_dead,omitempty"`
	// The time at which tensor was available and started to be returned.
	SendStartMicros int64 `protobuf:"varint,3,opt,name=send_start_micros,json=sendStartMicros,proto3" json:"send_start_micros,omitempty"`
	// Optional additional information about how to receive the tensor,
	// e.g. in the event that `RecvTensorRequest.dma_ok` was true.
	TransportOptions *anypb.Any `protobuf:"bytes,4,opt,name=transport_options,json=transportOptions,proto3" json:"transport_options,omitempty"`
	// Whether the receiver should send a MarkRecvFinishedRequest to the sender
	// to ack the message.
	RequireAck bool `protobuf:"varint,5,opt,name=require_ack,json=requireAck,proto3" json:"require_ack,omitempty"`
}

func (x *RecvTensorResponse) Reset() {
	*x = RecvTensorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecvTensorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecvTensorResponse) ProtoMessage() {}

func (x *RecvTensorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecvTensorResponse.ProtoReflect.Descriptor instead.
func (*RecvTensorResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{18}
}

func (x *RecvTensorResponse) GetTensor() *tensor_go_proto.TensorProto {
	if x != nil {
		return x.Tensor
	}
	return nil
}

func (x *RecvTensorResponse) GetIsDead() bool {
	if x != nil {
		return x.IsDead
	}
	return false
}

func (x *RecvTensorResponse) GetSendStartMicros() int64 {
	if x != nil {
		return x.SendStartMicros
	}
	return 0
}

func (x *RecvTensorResponse) GetTransportOptions() *anypb.Any {
	if x != nil {
		return x.TransportOptions
	}
	return nil
}

func (x *RecvTensorResponse) GetRequireAck() bool {
	if x != nil {
		return x.RequireAck
	}
	return false
}

// Message for managing the response cache maintained on the sender side.
// Currently only used by the gRPC worker service.
type MarkRecvFinishedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId int64 `protobuf:"varint,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *MarkRecvFinishedRequest) Reset() {
	*x = MarkRecvFinishedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkRecvFinishedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkRecvFinishedRequest) ProtoMessage() {}

func (x *MarkRecvFinishedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkRecvFinishedRequest.ProtoReflect.Descriptor instead.
func (*MarkRecvFinishedRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{19}
}

func (x *MarkRecvFinishedRequest) GetRequestId() int64 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

type MarkRecvFinishedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MarkRecvFinishedResponse) Reset() {
	*x = MarkRecvFinishedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkRecvFinishedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkRecvFinishedResponse) ProtoMessage() {}

func (x *MarkRecvFinishedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkRecvFinishedResponse.ProtoReflect.Descriptor instead.
func (*MarkRecvFinishedResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{20}
}

// Out-of-band request to begin or end logging, or
// to retrieve logs for particular steps.
type LoggingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// If true, RPC logging will be enabled.
	EnableRpcLogging bool `protobuf:"varint,1,opt,name=enable_rpc_logging,json=enableRpcLogging,proto3" json:"enable_rpc_logging,omitempty"`
	// If true, RPC logging will be disabled.
	DisableRpcLogging bool `protobuf:"varint,4,opt,name=disable_rpc_logging,json=disableRpcLogging,proto3" json:"disable_rpc_logging,omitempty"`
	// If true, discard any saved logging data (for all steps).
	Clear bool `protobuf:"varint,2,opt,name=clear,proto3" json:"clear,omitempty"`
	// When set, requests all saved log data pertaining to the step.
	// Any log data retrieved is eliminated from the store and cannot be
	// retrieved again.
	FetchStepId []int64 `protobuf:"varint,3,rep,packed,name=fetch_step_id,json=fetchStepId,proto3" json:"fetch_step_id,omitempty"`
}

func (x *LoggingRequest) Reset() {
	*x = LoggingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoggingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoggingRequest) ProtoMessage() {}

func (x *LoggingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoggingRequest.ProtoReflect.Descriptor instead.
func (*LoggingRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{21}
}

func (x *LoggingRequest) GetEnableRpcLogging() bool {
	if x != nil {
		return x.EnableRpcLogging
	}
	return false
}

func (x *LoggingRequest) GetDisableRpcLogging() bool {
	if x != nil {
		return x.DisableRpcLogging
	}
	return false
}

func (x *LoggingRequest) GetClear() bool {
	if x != nil {
		return x.Clear
	}
	return false
}

func (x *LoggingRequest) GetFetchStepId() []int64 {
	if x != nil {
		return x.FetchStepId
	}
	return nil
}

type LabeledStepStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StepId    int64                          `protobuf:"varint,1,opt,name=step_id,json=stepId,proto3" json:"step_id,omitempty"`
	StepStats *step_stats_go_proto.StepStats `protobuf:"bytes,2,opt,name=step_stats,json=stepStats,proto3" json:"step_stats,omitempty"`
}

func (x *LabeledStepStats) Reset() {
	*x = LabeledStepStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabeledStepStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabeledStepStats) ProtoMessage() {}

func (x *LabeledStepStats) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabeledStepStats.ProtoReflect.Descriptor instead.
func (*LabeledStepStats) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{22}
}

func (x *LabeledStepStats) GetStepId() int64 {
	if x != nil {
		return x.StepId
	}
	return 0
}

func (x *LabeledStepStats) GetStepStats() *step_stats_go_proto.StepStats {
	if x != nil {
		return x.StepStats
	}
	return nil
}

type LoggingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Step []*LabeledStepStats `protobuf:"bytes,1,rep,name=step,proto3" json:"step,omitempty"`
}

func (x *LoggingResponse) Reset() {
	*x = LoggingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoggingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoggingResponse) ProtoMessage() {}

func (x *LoggingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoggingResponse.ProtoReflect.Descriptor instead.
func (*LoggingResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{23}
}

func (x *LoggingResponse) GetStep() []*LabeledStepStats {
	if x != nil {
		return x.Step
	}
	return nil
}

type TraceOpts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Length of the trace to be taken, in seconds.
	Duration float64 `protobuf:"fixed64,1,opt,name=duration,proto3" json:"duration,omitempty"`
	// If true, capture step profile locally in each worker. Currently
	// unimplemented.
	UseStepProfiler bool `protobuf:"varint,2,opt,name=use_step_profiler,json=useStepProfiler,proto3" json:"use_step_profiler,omitempty"`
	// If true, capture kernel events from each worker.
	UseKernelProfiler bool `protobuf:"varint,3,opt,name=use_kernel_profiler,json=useKernelProfiler,proto3" json:"use_kernel_profiler,omitempty"`
	// If true, capture extended profiling events from TensorFlow process.
	UseExtendedProfiler bool `protobuf:"varint,4,opt,name=use_extended_profiler,json=useExtendedProfiler,proto3" json:"use_extended_profiler,omitempty"`
	// If true, capture GPU profiling events locally on each
	// machine. Currently unimplemented.
	UseGpuProfiler bool `protobuf:"varint,5,opt,name=use_gpu_profiler,json=useGpuProfiler,proto3" json:"use_gpu_profiler,omitempty"`
	// If true, collect sampled profile events. Currently unimplemented.
	UseSampleProfiler bool `protobuf:"varint,6,opt,name=use_sample_profiler,json=useSampleProfiler,proto3" json:"use_sample_profiler,omitempty"`
}

func (x *TraceOpts) Reset() {
	*x = TraceOpts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceOpts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceOpts) ProtoMessage() {}

func (x *TraceOpts) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceOpts.ProtoReflect.Descriptor instead.
func (*TraceOpts) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{24}
}

func (x *TraceOpts) GetDuration() float64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *TraceOpts) GetUseStepProfiler() bool {
	if x != nil {
		return x.UseStepProfiler
	}
	return false
}

func (x *TraceOpts) GetUseKernelProfiler() bool {
	if x != nil {
		return x.UseKernelProfiler
	}
	return false
}

func (x *TraceOpts) GetUseExtendedProfiler() bool {
	if x != nil {
		return x.UseExtendedProfiler
	}
	return false
}

func (x *TraceOpts) GetUseGpuProfiler() bool {
	if x != nil {
		return x.UseGpuProfiler
	}
	return false
}

func (x *TraceOpts) GetUseSampleProfiler() bool {
	if x != nil {
		return x.UseSampleProfiler
	}
	return false
}

// Out-of-band request to configure distributed tracing.
type TracingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Options *TraceOpts `protobuf:"bytes,1,opt,name=options,proto3" json:"options,omitempty"`
}

func (x *TracingRequest) Reset() {
	*x = TracingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TracingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TracingRequest) ProtoMessage() {}

func (x *TracingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TracingRequest.ProtoReflect.Descriptor instead.
func (*TracingRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{25}
}

func (x *TracingRequest) GetOptions() *TraceOpts {
	if x != nil {
		return x.Options
	}
	return nil
}

type TracingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TracingResponse) Reset() {
	*x = TracingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TracingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TracingResponse) ProtoMessage() {}

func (x *TracingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TracingResponse.ProtoReflect.Descriptor instead.
func (*TracingResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{26}
}

type RecvBufRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Used at server side to find the correct BufRendezvous.
	StepId int64 `protobuf:"varint,1,opt,name=step_id,json=stepId,proto3" json:"step_id,omitempty"`
	// Arbitrary string identifying a BufRendezvous entry.
	BufRendezvousKey string `protobuf:"bytes,2,opt,name=buf_rendezvous_key,json=bufRendezvousKey,proto3" json:"buf_rendezvous_key,omitempty"`
	// Size of value expected, must agree with BufRendezvous entry.
	NumBytes int64 `protobuf:"varint,3,opt,name=num_bytes,json=numBytes,proto3" json:"num_bytes,omitempty"`
	// When RDMA is in use, address of destination field on client.
	BufPtr uint64 `protobuf:"fixed64,4,opt,name=buf_ptr,json=bufPtr,proto3" json:"buf_ptr,omitempty"`
	// Optional information on client-side device locality.
	ClientLocality *device_attributes_go_proto.DeviceLocality `protobuf:"bytes,5,opt,name=client_locality,json=clientLocality,proto3" json:"client_locality,omitempty"`
	// Optional information on server-side device locality.
	ServerLocality *device_attributes_go_proto.DeviceLocality `protobuf:"bytes,6,opt,name=server_locality,json=serverLocality,proto3" json:"server_locality,omitempty"`
	// Optional, implementation-specific data.
	TransportOptions *anypb.Any `protobuf:"bytes,7,opt,name=transport_options,json=transportOptions,proto3" json:"transport_options,omitempty"`
	// For annotating timeline and device incarnation check.
	SrcDevice string `protobuf:"bytes,8,opt,name=src_device,json=srcDevice,proto3" json:"src_device,omitempty"`
	// Optional, for annotating the timeline.
	DstDevice string `protobuf:"bytes,9,opt,name=dst_device,json=dstDevice,proto3" json:"dst_device,omitempty"`
	// Depending on the RPC system in use, it may be necessary to set this
	// id to detect resends of RPCs where the server is not aware that
	// the prior RPC failed.
	RequestId int64 `protobuf:"varint,10,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Incarnation number of the source device, used to detect worker failures.
	SrcIncarnation uint64 `protobuf:"varint,11,opt,name=src_incarnation,json=srcIncarnation,proto3" json:"src_incarnation,omitempty"`
}

func (x *RecvBufRequest) Reset() {
	*x = RecvBufRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecvBufRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecvBufRequest) ProtoMessage() {}

func (x *RecvBufRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecvBufRequest.ProtoReflect.Descriptor instead.
func (*RecvBufRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{27}
}

func (x *RecvBufRequest) GetStepId() int64 {
	if x != nil {
		return x.StepId
	}
	return 0
}

func (x *RecvBufRequest) GetBufRendezvousKey() string {
	if x != nil {
		return x.BufRendezvousKey
	}
	return ""
}

func (x *RecvBufRequest) GetNumBytes() int64 {
	if x != nil {
		return x.NumBytes
	}
	return 0
}

func (x *RecvBufRequest) GetBufPtr() uint64 {
	if x != nil {
		return x.BufPtr
	}
	return 0
}

func (x *RecvBufRequest) GetClientLocality() *device_attributes_go_proto.DeviceLocality {
	if x != nil {
		return x.ClientLocality
	}
	return nil
}

func (x *RecvBufRequest) GetServerLocality() *device_attributes_go_proto.DeviceLocality {
	if x != nil {
		return x.ServerLocality
	}
	return nil
}

func (x *RecvBufRequest) GetTransportOptions() *anypb.Any {
	if x != nil {
		return x.TransportOptions
	}
	return nil
}

func (x *RecvBufRequest) GetSrcDevice() string {
	if x != nil {
		return x.SrcDevice
	}
	return ""
}

func (x *RecvBufRequest) GetDstDevice() string {
	if x != nil {
		return x.DstDevice
	}
	return ""
}

func (x *RecvBufRequest) GetRequestId() int64 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

func (x *RecvBufRequest) GetSrcIncarnation() uint64 {
	if x != nil {
		return x.SrcIncarnation
	}
	return 0
}

type RecvBufResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BufPtr   uint64 `protobuf:"fixed64,1,opt,name=buf_ptr,json=bufPtr,proto3" json:"buf_ptr,omitempty"`      // Address of source field on server.
	NumBytes int64  `protobuf:"varint,2,opt,name=num_bytes,json=numBytes,proto3" json:"num_bytes,omitempty"` // Byte length of buf_ptr field, if set.
	IsDead   bool   `protobuf:"varint,3,opt,name=is_dead,json=isDead,proto3" json:"is_dead,omitempty"`       // True if value is 'dead' like a tensor.
	// Optional, implementation-specific data.
	TransportOptions *anypb.Any `protobuf:"bytes,4,opt,name=transport_options,json=transportOptions,proto3" json:"transport_options,omitempty"`
	// Optional, for timeline.
	SendStartMicros int64 `protobuf:"varint,5,opt,name=send_start_micros,json=sendStartMicros,proto3" json:"send_start_micros,omitempty"`
	// Whether the receiver should send a MarkRecvFinishedRequest to the sender
	// to ack the message.
	RequireAck bool `protobuf:"varint,6,opt,name=require_ack,json=requireAck,proto3" json:"require_ack,omitempty"`
}

func (x *RecvBufResponse) Reset() {
	*x = RecvBufResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecvBufResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecvBufResponse) ProtoMessage() {}

func (x *RecvBufResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecvBufResponse.ProtoReflect.Descriptor instead.
func (*RecvBufResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{28}
}

func (x *RecvBufResponse) GetBufPtr() uint64 {
	if x != nil {
		return x.BufPtr
	}
	return 0
}

func (x *RecvBufResponse) GetNumBytes() int64 {
	if x != nil {
		return x.NumBytes
	}
	return 0
}

func (x *RecvBufResponse) GetIsDead() bool {
	if x != nil {
		return x.IsDead
	}
	return false
}

func (x *RecvBufResponse) GetTransportOptions() *anypb.Any {
	if x != nil {
		return x.TransportOptions
	}
	return nil
}

func (x *RecvBufResponse) GetSendStartMicros() int64 {
	if x != nil {
		return x.SendStartMicros
	}
	return 0
}

func (x *RecvBufResponse) GetRequireAck() bool {
	if x != nil {
		return x.RequireAck
	}
	return false
}

// Supplies one or more device names as members of the group identified by
// group_key.  Service will respond when all group_size devices become known.
// All devices in group must have same type.
type CompleteGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupKey         int32                                        `protobuf:"varint,1,opt,name=group_key,json=groupKey,proto3" json:"group_key,omitempty"`
	GroupSize        int32                                        `protobuf:"varint,2,opt,name=group_size,json=groupSize,proto3" json:"group_size,omitempty"`
	DeviceType       string                                       `protobuf:"bytes,3,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	CollectiveType   int32                                        `protobuf:"varint,5,opt,name=collective_type,json=collectiveType,proto3" json:"collective_type,omitempty"`
	DeviceAttributes *device_attributes_go_proto.DeviceAttributes `protobuf:"bytes,6,opt,name=device_attributes,json=deviceAttributes,proto3" json:"device_attributes,omitempty"`
}

func (x *CompleteGroupRequest) Reset() {
	*x = CompleteGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteGroupRequest) ProtoMessage() {}

func (x *CompleteGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteGroupRequest.ProtoReflect.Descriptor instead.
func (*CompleteGroupRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{29}
}

func (x *CompleteGroupRequest) GetGroupKey() int32 {
	if x != nil {
		return x.GroupKey
	}
	return 0
}

func (x *CompleteGroupRequest) GetGroupSize() int32 {
	if x != nil {
		return x.GroupSize
	}
	return 0
}

func (x *CompleteGroupRequest) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *CompleteGroupRequest) GetCollectiveType() int32 {
	if x != nil {
		return x.CollectiveType
	}
	return 0
}

func (x *CompleteGroupRequest) GetDeviceAttributes() *device_attributes_go_proto.DeviceAttributes {
	if x != nil {
		return x.DeviceAttributes
	}
	return nil
}

// Gives the complete membership of the group identified by group_key.
type CompleteGroupResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupKey         int32                                          `protobuf:"varint,1,opt,name=group_key,json=groupKey,proto3" json:"group_key,omitempty"`
	GroupSize        int32                                          `protobuf:"varint,2,opt,name=group_size,json=groupSize,proto3" json:"group_size,omitempty"`
	DeviceType       string                                         `protobuf:"bytes,3,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	NumTasks         int32                                          `protobuf:"varint,4,opt,name=num_tasks,json=numTasks,proto3" json:"num_tasks,omitempty"` // number of distinct tasks hosting the devices
	CommunicatorKey  []byte                                         `protobuf:"bytes,7,opt,name=communicator_key,json=communicatorKey,proto3" json:"communicator_key,omitempty"`
	DeviceAttributes []*device_attributes_go_proto.DeviceAttributes `protobuf:"bytes,8,rep,name=device_attributes,json=deviceAttributes,proto3" json:"device_attributes,omitempty"`
}

func (x *CompleteGroupResponse) Reset() {
	*x = CompleteGroupResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteGroupResponse) ProtoMessage() {}

func (x *CompleteGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteGroupResponse.ProtoReflect.Descriptor instead.
func (*CompleteGroupResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{30}
}

func (x *CompleteGroupResponse) GetGroupKey() int32 {
	if x != nil {
		return x.GroupKey
	}
	return 0
}

func (x *CompleteGroupResponse) GetGroupSize() int32 {
	if x != nil {
		return x.GroupSize
	}
	return 0
}

func (x *CompleteGroupResponse) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *CompleteGroupResponse) GetNumTasks() int32 {
	if x != nil {
		return x.NumTasks
	}
	return 0
}

func (x *CompleteGroupResponse) GetCommunicatorKey() []byte {
	if x != nil {
		return x.CommunicatorKey
	}
	return nil
}

func (x *CompleteGroupResponse) GetDeviceAttributes() []*device_attributes_go_proto.DeviceAttributes {
	if x != nil {
		return x.DeviceAttributes
	}
	return nil
}

// Supplies data about one collective op belonging to the instance identified
// by instance_key.  Service will respond when all group_size ops have
// become known.  Most of the data being sent is for correctness checking,
// to ensure that all ops in the instance share common attributes.
type CompleteInstanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string                                  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type         int32                                   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	DataType     types_go_proto.DataType                 `protobuf:"varint,3,opt,name=data_type,json=dataType,proto3,enum=tensorflow.DataType" json:"data_type,omitempty"`
	Shape        *tensor_shape_go_proto.TensorShapeProto `protobuf:"bytes,4,opt,name=shape,proto3" json:"shape,omitempty"`
	GroupKey     int32                                   `protobuf:"varint,5,opt,name=group_key,json=groupKey,proto3" json:"group_key,omitempty"`
	GroupSize    int32                                   `protobuf:"varint,6,opt,name=group_size,json=groupSize,proto3" json:"group_size,omitempty"`
	InstanceKey  int32                                   `protobuf:"varint,7,opt,name=instance_key,json=instanceKey,proto3" json:"instance_key,omitempty"`
	DeviceType   string                                  `protobuf:"bytes,8,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	SubdivOffset []int32                                 `protobuf:"varint,9,rep,packed,name=subdiv_offset,json=subdivOffset,proto3" json:"subdiv_offset,omitempty"`
	Device       string                                  `protobuf:"bytes,10,opt,name=device,proto3" json:"device,omitempty"`
	IsSource     bool                                    `protobuf:"varint,11,opt,name=is_source,json=isSource,proto3" json:"is_source,omitempty"`
}

func (x *CompleteInstanceRequest) Reset() {
	*x = CompleteInstanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteInstanceRequest) ProtoMessage() {}

func (x *CompleteInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteInstanceRequest.ProtoReflect.Descriptor instead.
func (*CompleteInstanceRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{31}
}

func (x *CompleteInstanceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CompleteInstanceRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CompleteInstanceRequest) GetDataType() types_go_proto.DataType {
	if x != nil {
		return x.DataType
	}
	return types_go_proto.DataType_DT_INVALID
}

func (x *CompleteInstanceRequest) GetShape() *tensor_shape_go_proto.TensorShapeProto {
	if x != nil {
		return x.Shape
	}
	return nil
}

func (x *CompleteInstanceRequest) GetGroupKey() int32 {
	if x != nil {
		return x.GroupKey
	}
	return 0
}

func (x *CompleteInstanceRequest) GetGroupSize() int32 {
	if x != nil {
		return x.GroupSize
	}
	return 0
}

func (x *CompleteInstanceRequest) GetInstanceKey() int32 {
	if x != nil {
		return x.InstanceKey
	}
	return 0
}

func (x *CompleteInstanceRequest) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *CompleteInstanceRequest) GetSubdivOffset() []int32 {
	if x != nil {
		return x.SubdivOffset
	}
	return nil
}

func (x *CompleteInstanceRequest) GetDevice() string {
	if x != nil {
		return x.Device
	}
	return ""
}

func (x *CompleteInstanceRequest) GetIsSource() bool {
	if x != nil {
		return x.IsSource
	}
	return false
}

// Confirms that every op in the instance has consistently declared itself.
// Also gives the source_rank in case of broadcast.
type CompleteInstanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceKey int32 `protobuf:"varint,1,opt,name=instance_key,json=instanceKey,proto3" json:"instance_key,omitempty"`
	SourceRank  int32 `protobuf:"varint,2,opt,name=source_rank,json=sourceRank,proto3" json:"source_rank,omitempty"`
}

func (x *CompleteInstanceResponse) Reset() {
	*x = CompleteInstanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteInstanceResponse) ProtoMessage() {}

func (x *CompleteInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteInstanceResponse.ProtoReflect.Descriptor instead.
func (*CompleteInstanceResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{32}
}

func (x *CompleteInstanceResponse) GetInstanceKey() int32 {
	if x != nil {
		return x.InstanceKey
	}
	return 0
}

func (x *CompleteInstanceResponse) GetSourceRank() int32 {
	if x != nil {
		return x.SourceRank
	}
	return 0
}

// Request for next agreed-upon step_id for the specified graph_keys.
// This is used to enable multiple graphs containing nodes from
// a common collective instance to coordinate using the same step_ids.
type GetStepSequenceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GraphKey []int64 `protobuf:"varint,1,rep,packed,name=graph_key,json=graphKey,proto3" json:"graph_key,omitempty"`
}

func (x *GetStepSequenceRequest) Reset() {
	*x = GetStepSequenceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStepSequenceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStepSequenceRequest) ProtoMessage() {}

func (x *GetStepSequenceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStepSequenceRequest.ProtoReflect.Descriptor instead.
func (*GetStepSequenceRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{33}
}

func (x *GetStepSequenceRequest) GetGraphKey() []int64 {
	if x != nil {
		return x.GraphKey
	}
	return nil
}

type StepSequence struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GraphKey   int64 `protobuf:"varint,1,opt,name=graph_key,json=graphKey,proto3" json:"graph_key,omitempty"`
	NextStepId int64 `protobuf:"varint,2,opt,name=next_step_id,json=nextStepId,proto3" json:"next_step_id,omitempty"`
}

func (x *StepSequence) Reset() {
	*x = StepSequence{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StepSequence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StepSequence) ProtoMessage() {}

func (x *StepSequence) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StepSequence.ProtoReflect.Descriptor instead.
func (*StepSequence) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{34}
}

func (x *StepSequence) GetGraphKey() int64 {
	if x != nil {
		return x.GraphKey
	}
	return 0
}

func (x *StepSequence) GetNextStepId() int64 {
	if x != nil {
		return x.NextStepId
	}
	return 0
}

// Next valid step_ids for one or more graph_keys.
type GetStepSequenceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StepSequence []*StepSequence `protobuf:"bytes,1,rep,name=step_sequence,json=stepSequence,proto3" json:"step_sequence,omitempty"`
}

func (x *GetStepSequenceResponse) Reset() {
	*x = GetStepSequenceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStepSequenceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStepSequenceResponse) ProtoMessage() {}

func (x *GetStepSequenceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_worker_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStepSequenceResponse.ProtoReflect.Descriptor instead.
func (*GetStepSequenceResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_worker_proto_rawDescGZIP(), []int{35}
}

func (x *GetStepSequenceResponse) GetStepSequence() []*StepSequence {
	if x != nil {
		return x.StepSequence
	}
	return nil
}

var File_tensorflow_core_protobuf_worker_proto protoreflect.FileDescriptor

var file_tensorflow_core_protobuf_worker_proto_rawDesc = []byte{
	0x0a, 0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f,
	0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x67,
	0x72, 0x61, 0x70, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d,
	0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66,
	0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f,
	0x73, 0x74, 0x65, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x26, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77,
	0x6f, 0x72, 0x6b, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x73, 0x68, 0x61, 0x70, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72,
	0x6b, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x63,
	0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x64, 0x65, 0x62, 0x75, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x12, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x5e, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x49, 0x0a, 0x11, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x10, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x22, 0xbe, 0x03,
	0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x64, 0x65,
	0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x44, 0x65, 0x66, 0x52, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x44, 0x65, 0x66, 0x12, 0x32, 0x0a, 0x15, 0x69, 0x73, 0x6f,
	0x6c, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69, 0x73, 0x6f, 0x6c, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x58, 0x0a,
	0x19, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x17,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x73, 0x74, 0x65,
	0x72, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61,
	0x73, 0x74, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2d, 0x0a, 0x12, 0x6d, 0x61, 0x73, 0x74,
	0x65, 0x72, 0x5f, 0x69, 0x6e, 0x63, 0x61, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x63, 0x61,
	0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x65, 0x0a, 0x1b, 0x63, 0x6f, 0x6f, 0x72, 0x64,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x19, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x1d,
	0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x43, 0x0a,
	0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x22, 0x1d, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0xcb, 0x03, 0x0a, 0x14, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x47, 0x72,
	0x61, 0x70, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x12, 0x3f, 0x0a, 0x1c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x77, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x65,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x6c, 0x6c,
	0x65, 0x64, 0x12, 0x31, 0x0a, 0x09, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x64, 0x65, 0x66, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x44, 0x65, 0x66, 0x52, 0x08, 0x67, 0x72, 0x61,
	0x70, 0x68, 0x44, 0x65, 0x66, 0x12, 0x2c, 0x0a, 0x10, 0x68, 0x61, 0x73, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x0e, 0x68, 0x61, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x46,
	0x6c, 0x6f, 0x77, 0x12, 0x3d, 0x0a, 0x0d, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0c, 0x67, 0x72, 0x61, 0x70, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x0c, 0x64, 0x65, 0x62, 0x75, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x12, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x61, 0x70, 0x68,
	0x4b, 0x65, 0x79, 0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x3a, 0x0a, 0x15, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x47, 0x72, 0x61, 0x70, 0x68,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x67, 0x72, 0x61, 0x70,
	0x68, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x67, 0x72, 0x61, 0x70, 0x68, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x22, 0xa3, 0x01, 0x0a, 0x16,
	0x44, 0x65, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x47, 0x72, 0x61, 0x70, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x3f, 0x0a,
	0x1c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x5f, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x19, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65,
	0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x67, 0x72, 0x61, 0x70, 0x68, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x22, 0x19, 0x0a, 0x17, 0x44, 0x65, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x31, 0x0a, 0x11,
	0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x22,
	0x14, 0x0a, 0x12, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xde, 0x01, 0x0a, 0x0c, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x6f, 0x72, 0x4f, 0x70, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x5f, 0x63, 0x6f, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x73, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x70, 0x61, 0x72,
	0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x15, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x73, 0x12, 0x4a, 0x0a, 0x22, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x61, 0x6c, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x6f, 0x6f, 0x6d,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x55,
	0x70, 0x6f, 0x6e, 0x4f, 0x6f, 0x6d, 0x22, 0xe8, 0x03, 0x0a, 0x0f, 0x52, 0x75, 0x6e, 0x47, 0x72,
	0x61, 0x70, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x12, 0x3f, 0x0a, 0x1c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x77, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x65,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x6c, 0x6c,
	0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x68, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x67, 0x72, 0x61, 0x70, 0x68, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x65, 0x70, 0x49, 0x64, 0x12, 0x35,
	0x0a, 0x09, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x6f, 0x70, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x4f, 0x70, 0x74, 0x73, 0x52, 0x08, 0x65, 0x78, 0x65,
	0x63, 0x4f, 0x70, 0x74, 0x73, 0x12, 0x30, 0x0a, 0x04, 0x73, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x52, 0x04, 0x73, 0x65, 0x6e, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x76, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x63, 0x76, 0x4b,
	0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61,
	0x6c, 0x12, 0x2d, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x72,
	0x74, 0x69, 0x61, 0x6c, 0x5f, 0x72, 0x75, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10,
	0x69, 0x73, 0x4c, 0x61, 0x73, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x75, 0x6e,
	0x12, 0x40, 0x0a, 0x1d, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x5f, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x62, 0x6f, 0x64,
	0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x6f,
	0x64, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x22, 0xdd, 0x02, 0x0a, 0x10, 0x52, 0x75, 0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x72, 0x65, 0x63, 0x76, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x52, 0x04, 0x72, 0x65, 0x63, 0x76, 0x12, 0x34, 0x0a, 0x0a, 0x73, 0x74, 0x65, 0x70,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x52, 0x09, 0x73, 0x74, 0x65, 0x70, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x37,
	0x0a, 0x0a, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x43, 0x6f, 0x73, 0x74, 0x47, 0x72, 0x61, 0x70, 0x68, 0x44, 0x65, 0x66, 0x52, 0x09, 0x63, 0x6f,
	0x73, 0x74, 0x47, 0x72, 0x61, 0x70, 0x68, 0x12, 0x3d, 0x0a, 0x0f, 0x70, 0x61, 0x72, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x47, 0x72,
	0x61, 0x70, 0x68, 0x44, 0x65, 0x66, 0x52, 0x0e, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x12, 0x37, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x30, 0x0a, 0x14, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0x2e, 0x0a, 0x13, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x47, 0x72, 0x61, 0x70,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x74, 0x65, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x65, 0x70, 0x49,
	0x64, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x47, 0x72, 0x61, 0x70,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xd6, 0x02, 0x0a, 0x11, 0x52, 0x65,
	0x63, 0x76, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x17, 0x0a, 0x07, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x73, 0x74, 0x65, 0x70, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x6e, 0x64,
	0x65, 0x7a, 0x76, 0x6f, 0x75, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x7a, 0x76, 0x6f, 0x75, 0x73, 0x4b, 0x65, 0x79, 0x12,
	0x15, 0x0a, 0x06, 0x64, 0x6d, 0x61, 0x5f, 0x6f, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x05, 0x64, 0x6d, 0x61, 0x4f, 0x6b, 0x12, 0x43, 0x0a, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x0e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x43, 0x0a, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x41, 0x0a, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e,
	0x79, 0x52, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x22, 0xee, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x63, 0x76, 0x54, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73,
	0x5f, 0x64, 0x65, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x44,
	0x65, 0x61, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f,
	0x73, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x12,
	0x41, 0x0a, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79,
	0x52, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x61, 0x63,
	0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x41, 0x63, 0x6b, 0x22, 0x38, 0x0a, 0x17, 0x4d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x63, 0x76, 0x46,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x1a, 0x0a,
	0x18, 0x4d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x63, 0x76, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xa8, 0x01, 0x0a, 0x0e, 0x4c, 0x6f,
	0x67, 0x67, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x12,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x72, 0x70, 0x63, 0x5f, 0x6c, 0x6f, 0x67, 0x67, 0x69,
	0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x70, 0x63, 0x4c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x2e, 0x0a, 0x13, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x72, 0x70, 0x63, 0x5f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x70, 0x63, 0x4c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6c,
	0x65, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x63, 0x6c, 0x65, 0x61, 0x72,
	0x12, 0x22, 0x0a, 0x0d, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x53, 0x74,
	0x65, 0x70, 0x49, 0x64, 0x22, 0x61, 0x0a, 0x10, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x65, 0x64, 0x53,
	0x74, 0x65, 0x70, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x74, 0x65, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x65, 0x70, 0x49,
	0x64, 0x12, 0x34, 0x0a, 0x0a, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x09, 0x73, 0x74,
	0x65, 0x70, 0x53, 0x74, 0x61, 0x74, 0x73, 0x22, 0x43, 0x0a, 0x0f, 0x4c, 0x6f, 0x67, 0x67, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x73, 0x74,
	0x65, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x65,
	0x70, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x04, 0x73, 0x74, 0x65, 0x70, 0x22, 0x91, 0x02, 0x0a,
	0x09, 0x54, 0x72, 0x61, 0x63, 0x65, 0x4f, 0x70, 0x74, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x5f, 0x73, 0x74,
	0x65, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x75, 0x73, 0x65, 0x5f, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x11, 0x75, 0x73, 0x65, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x72, 0x12, 0x32, 0x0a, 0x15, 0x75, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64,
	0x65, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x13, 0x75, 0x73, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x75, 0x73, 0x65, 0x5f, 0x67, 0x70,
	0x75, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0e, 0x75, 0x73, 0x65, 0x47, 0x70, 0x75, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x72,
	0x12, 0x2e, 0x0a, 0x13, 0x75, 0x73, 0x65, 0x5f, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x75,
	0x73, 0x65, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x72,
	0x22, 0x41, 0x0a, 0x0e, 0x54, 0x72, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2f, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x4f, 0x70, 0x74, 0x73, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0x11, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xe0, 0x03, 0x0a, 0x0e, 0x52, 0x65, 0x63, 0x76, 0x42,
	0x75, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x74, 0x65,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x65, 0x70,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x75, 0x66, 0x5f, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x7a,
	0x76, 0x6f, 0x75, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x62, 0x75, 0x66, 0x52, 0x65, 0x6e, 0x64, 0x65, 0x7a, 0x76, 0x6f, 0x75, 0x73, 0x4b, 0x65, 0x79,
	0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x75, 0x6d, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x6e, 0x75, 0x6d, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x17, 0x0a,
	0x07, 0x62, 0x75, 0x66, 0x5f, 0x70, 0x74, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x06, 0x52, 0x06,
	0x62, 0x75, 0x66, 0x50, 0x74, 0x72, 0x12, 0x43, 0x0a, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x0e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x43, 0x0a, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x41, 0x0a, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e,
	0x79, 0x52, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x72, 0x63, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x72, 0x63, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x73, 0x74, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x73, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x27, 0x0a, 0x0f, 0x73, 0x72, 0x63, 0x5f, 0x69, 0x6e, 0x63, 0x61, 0x72, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x73, 0x72, 0x63, 0x49, 0x6e,
	0x63, 0x61, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xf0, 0x01, 0x0a, 0x0f, 0x52, 0x65,
	0x63, 0x76, 0x42, 0x75, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x62, 0x75, 0x66, 0x5f, 0x70, 0x74, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x52, 0x06,
	0x62, 0x75, 0x66, 0x50, 0x74, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x75, 0x6d, 0x5f, 0x62, 0x79,
	0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6e, 0x75, 0x6d, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x61, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x44, 0x65, 0x61, 0x64, 0x12, 0x41, 0x0a, 0x11,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x10, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x2a, 0x0a, 0x11, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6d, 0x69,
	0x63, 0x72, 0x6f, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x65, 0x6e, 0x64,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x61, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x41, 0x63, 0x6b, 0x22, 0xed, 0x01, 0x0a,
	0x14, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4b,
	0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x49, 0x0a, 0x11, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x73, 0x52, 0x10, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x4a, 0x04, 0x08, 0x04, 0x10, 0x05, 0x22, 0x93, 0x02, 0x0a,
	0x15, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x75, 0x6d, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6e, 0x75, 0x6d, 0x54, 0x61, 0x73, 0x6b, 0x73,
	0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x6d,
	0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x4b, 0x65, 0x79, 0x12, 0x49, 0x0a, 0x11, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x73, 0x52, 0x10, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x4a, 0x04, 0x08, 0x05, 0x10, 0x06, 0x4a, 0x04, 0x08, 0x06,
	0x10, 0x07, 0x22, 0x82, 0x03, 0x0a, 0x17, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x73, 0x68, 0x61,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53, 0x68, 0x61, 0x70,
	0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x05, 0x73, 0x68, 0x61, 0x70, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x75, 0x62, 0x64, 0x69, 0x76, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x64, 0x69, 0x76, 0x4f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69,
	0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x64, 0x0a, 0x18, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x4a, 0x04, 0x08, 0x03, 0x10, 0x04, 0x22, 0x35, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x53, 0x74, 0x65, 0x70, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x72, 0x61, 0x70, 0x68,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x67, 0x72, 0x61, 0x70,
	0x68, 0x4b, 0x65, 0x79, 0x22, 0x4d, 0x0a, 0x0c, 0x53, 0x74, 0x65, 0x70, 0x53, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x67, 0x72, 0x61, 0x70, 0x68, 0x4b, 0x65,
	0x79, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x53, 0x74, 0x65,
	0x70, 0x49, 0x64, 0x22, 0x58, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x74, 0x65, 0x70, 0x53, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d,
	0x0a, 0x0d, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x52,
	0x0c, 0x73, 0x74, 0x65, 0x70, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x86, 0x01,
	0x0a, 0x1a, 0x6f, 0x72, 0x67, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x64, 0x69, 0x73, 0x74, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0c, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x50, 0x01, 0x5a, 0x55, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x67, 0x6f, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x6f, 0x72, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x5f, 0x67, 0x6f, 0x5f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0xf8, 0x01, 0x01, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tensorflow_core_protobuf_worker_proto_rawDescOnce sync.Once
	file_tensorflow_core_protobuf_worker_proto_rawDescData = file_tensorflow_core_protobuf_worker_proto_rawDesc
)

func file_tensorflow_core_protobuf_worker_proto_rawDescGZIP() []byte {
	file_tensorflow_core_protobuf_worker_proto_rawDescOnce.Do(func() {
		file_tensorflow_core_protobuf_worker_proto_rawDescData = protoimpl.X.CompressGZIP(file_tensorflow_core_protobuf_worker_proto_rawDescData)
	})
	return file_tensorflow_core_protobuf_worker_proto_rawDescData
}

var file_tensorflow_core_protobuf_worker_proto_msgTypes = make([]protoimpl.MessageInfo, 36)
var file_tensorflow_core_protobuf_worker_proto_goTypes = []interface{}{
	(*GetStatusRequest)(nil),                            // 0: tensorflow.GetStatusRequest
	(*GetStatusResponse)(nil),                           // 1: tensorflow.GetStatusResponse
	(*CreateWorkerSessionRequest)(nil),                  // 2: tensorflow.CreateWorkerSessionRequest
	(*CreateWorkerSessionResponse)(nil),                 // 3: tensorflow.CreateWorkerSessionResponse
	(*DeleteWorkerSessionRequest)(nil),                  // 4: tensorflow.DeleteWorkerSessionRequest
	(*DeleteWorkerSessionResponse)(nil),                 // 5: tensorflow.DeleteWorkerSessionResponse
	(*RegisterGraphRequest)(nil),                        // 6: tensorflow.RegisterGraphRequest
	(*RegisterGraphResponse)(nil),                       // 7: tensorflow.RegisterGraphResponse
	(*DeregisterGraphRequest)(nil),                      // 8: tensorflow.DeregisterGraphRequest
	(*DeregisterGraphResponse)(nil),                     // 9: tensorflow.DeregisterGraphResponse
	(*CleanupAllRequest)(nil),                           // 10: tensorflow.CleanupAllRequest
	(*CleanupAllResponse)(nil),                          // 11: tensorflow.CleanupAllResponse
	(*ExecutorOpts)(nil),                                // 12: tensorflow.ExecutorOpts
	(*RunGraphRequest)(nil),                             // 13: tensorflow.RunGraphRequest
	(*RunGraphResponse)(nil),                            // 14: tensorflow.RunGraphResponse
	(*CleanupGraphRequest)(nil),                         // 15: tensorflow.CleanupGraphRequest
	(*CleanupGraphResponse)(nil),                        // 16: tensorflow.CleanupGraphResponse
	(*RecvTensorRequest)(nil),                           // 17: tensorflow.RecvTensorRequest
	(*RecvTensorResponse)(nil),                          // 18: tensorflow.RecvTensorResponse
	(*MarkRecvFinishedRequest)(nil),                     // 19: tensorflow.MarkRecvFinishedRequest
	(*MarkRecvFinishedResponse)(nil),                    // 20: tensorflow.MarkRecvFinishedResponse
	(*LoggingRequest)(nil),                              // 21: tensorflow.LoggingRequest
	(*LabeledStepStats)(nil),                            // 22: tensorflow.LabeledStepStats
	(*LoggingResponse)(nil),                             // 23: tensorflow.LoggingResponse
	(*TraceOpts)(nil),                                   // 24: tensorflow.TraceOpts
	(*TracingRequest)(nil),                              // 25: tensorflow.TracingRequest
	(*TracingResponse)(nil),                             // 26: tensorflow.TracingResponse
	(*RecvBufRequest)(nil),                              // 27: tensorflow.RecvBufRequest
	(*RecvBufResponse)(nil),                             // 28: tensorflow.RecvBufResponse
	(*CompleteGroupRequest)(nil),                        // 29: tensorflow.CompleteGroupRequest
	(*CompleteGroupResponse)(nil),                       // 30: tensorflow.CompleteGroupResponse
	(*CompleteInstanceRequest)(nil),                     // 31: tensorflow.CompleteInstanceRequest
	(*CompleteInstanceResponse)(nil),                    // 32: tensorflow.CompleteInstanceResponse
	(*GetStepSequenceRequest)(nil),                      // 33: tensorflow.GetStepSequenceRequest
	(*StepSequence)(nil),                                // 34: tensorflow.StepSequence
	(*GetStepSequenceResponse)(nil),                     // 35: tensorflow.GetStepSequenceResponse
	(*device_attributes_go_proto.DeviceAttributes)(nil), // 36: tensorflow.DeviceAttributes
	(*ServerDef)(nil),                                   // 37: tensorflow.ServerDef
	(*CoordinationServiceConfig)(nil),                   // 38: tensorflow.CoordinationServiceConfig
	(*graph_go_proto.GraphDef)(nil),                     // 39: tensorflow.GraphDef
	(*GraphOptions)(nil),                                // 40: tensorflow.GraphOptions
	(*DebugOptions)(nil),                                // 41: tensorflow.DebugOptions
	(*ConfigProto)(nil),                                 // 42: tensorflow.ConfigProto
	(*NamedTensorProto)(nil),                            // 43: tensorflow.NamedTensorProto
	(*step_stats_go_proto.StepStats)(nil),               // 44: tensorflow.StepStats
	(*cost_graph_go_proto.CostGraphDef)(nil),            // 45: tensorflow.CostGraphDef
	(Code)(0),                                           // 46: tensorflow.error.Code
	(*device_attributes_go_proto.DeviceLocality)(nil),   // 47: tensorflow.DeviceLocality
	(*anypb.Any)(nil),                                   // 48: google.protobuf.Any
	(*tensor_go_proto.TensorProto)(nil),                 // 49: tensorflow.TensorProto
	(types_go_proto.DataType)(0),                        // 50: tensorflow.DataType
	(*tensor_shape_go_proto.TensorShapeProto)(nil),      // 51: tensorflow.TensorShapeProto
}
var file_tensorflow_core_protobuf_worker_proto_depIdxs = []int32{
	36, // 0: tensorflow.GetStatusResponse.device_attributes:type_name -> tensorflow.DeviceAttributes
	37, // 1: tensorflow.CreateWorkerSessionRequest.server_def:type_name -> tensorflow.ServerDef
	36, // 2: tensorflow.CreateWorkerSessionRequest.cluster_device_attributes:type_name -> tensorflow.DeviceAttributes
	38, // 3: tensorflow.CreateWorkerSessionRequest.coordination_service_config:type_name -> tensorflow.CoordinationServiceConfig
	39, // 4: tensorflow.RegisterGraphRequest.graph_def:type_name -> tensorflow.GraphDef
	40, // 5: tensorflow.RegisterGraphRequest.graph_options:type_name -> tensorflow.GraphOptions
	41, // 6: tensorflow.RegisterGraphRequest.debug_options:type_name -> tensorflow.DebugOptions
	42, // 7: tensorflow.RegisterGraphRequest.config_proto:type_name -> tensorflow.ConfigProto
	12, // 8: tensorflow.RunGraphRequest.exec_opts:type_name -> tensorflow.ExecutorOpts
	43, // 9: tensorflow.RunGraphRequest.send:type_name -> tensorflow.NamedTensorProto
	43, // 10: tensorflow.RunGraphResponse.recv:type_name -> tensorflow.NamedTensorProto
	44, // 11: tensorflow.RunGraphResponse.step_stats:type_name -> tensorflow.StepStats
	45, // 12: tensorflow.RunGraphResponse.cost_graph:type_name -> tensorflow.CostGraphDef
	39, // 13: tensorflow.RunGraphResponse.partition_graph:type_name -> tensorflow.GraphDef
	46, // 14: tensorflow.RunGraphResponse.status_code:type_name -> tensorflow.error.Code
	47, // 15: tensorflow.RecvTensorRequest.client_locality:type_name -> tensorflow.DeviceLocality
	47, // 16: tensorflow.RecvTensorRequest.server_locality:type_name -> tensorflow.DeviceLocality
	48, // 17: tensorflow.RecvTensorRequest.transport_options:type_name -> google.protobuf.Any
	49, // 18: tensorflow.RecvTensorResponse.tensor:type_name -> tensorflow.TensorProto
	48, // 19: tensorflow.RecvTensorResponse.transport_options:type_name -> google.protobuf.Any
	44, // 20: tensorflow.LabeledStepStats.step_stats:type_name -> tensorflow.StepStats
	22, // 21: tensorflow.LoggingResponse.step:type_name -> tensorflow.LabeledStepStats
	24, // 22: tensorflow.TracingRequest.options:type_name -> tensorflow.TraceOpts
	47, // 23: tensorflow.RecvBufRequest.client_locality:type_name -> tensorflow.DeviceLocality
	47, // 24: tensorflow.RecvBufRequest.server_locality:type_name -> tensorflow.DeviceLocality
	48, // 25: tensorflow.RecvBufRequest.transport_options:type_name -> google.protobuf.Any
	48, // 26: tensorflow.RecvBufResponse.transport_options:type_name -> google.protobuf.Any
	36, // 27: tensorflow.CompleteGroupRequest.device_attributes:type_name -> tensorflow.DeviceAttributes
	36, // 28: tensorflow.CompleteGroupResponse.device_attributes:type_name -> tensorflow.DeviceAttributes
	50, // 29: tensorflow.CompleteInstanceRequest.data_type:type_name -> tensorflow.DataType
	51, // 30: tensorflow.CompleteInstanceRequest.shape:type_name -> tensorflow.TensorShapeProto
	34, // 31: tensorflow.GetStepSequenceResponse.step_sequence:type_name -> tensorflow.StepSequence
	32, // [32:32] is the sub-list for method output_type
	32, // [32:32] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_tensorflow_core_protobuf_worker_proto_init() }
func file_tensorflow_core_protobuf_worker_proto_init() {
	if File_tensorflow_core_protobuf_worker_proto != nil {
		return
	}
	file_tensorflow_core_protobuf_config_proto_init()
	file_tensorflow_core_protobuf_coordination_config_proto_init()
	file_tensorflow_core_protobuf_debug_proto_init()
	file_tensorflow_core_protobuf_error_codes_proto_init()
	file_tensorflow_core_protobuf_named_tensor_proto_init()
	file_tensorflow_core_protobuf_tensorflow_server_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tensorflow_core_protobuf_worker_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWorkerSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWorkerSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteWorkerSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteWorkerSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterGraphRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterGraphResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeregisterGraphRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeregisterGraphResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CleanupAllRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CleanupAllResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutorOpts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunGraphRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunGraphResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CleanupGraphRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CleanupGraphResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecvTensorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecvTensorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkRecvFinishedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkRecvFinishedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoggingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabeledStepStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoggingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceOpts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TracingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TracingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecvBufRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecvBufResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteGroupResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteInstanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteInstanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStepSequenceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StepSequence); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_worker_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStepSequenceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tensorflow_core_protobuf_worker_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   36,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_core_protobuf_worker_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_protobuf_worker_proto_depIdxs,
		MessageInfos:      file_tensorflow_core_protobuf_worker_proto_msgTypes,
	}.Build()
	File_tensorflow_core_protobuf_worker_proto = out.File
	file_tensorflow_core_protobuf_worker_proto_rawDesc = nil
	file_tensorflow_core_protobuf_worker_proto_goTypes = nil
	file_tensorflow_core_protobuf_worker_proto_depIdxs = nil
}
