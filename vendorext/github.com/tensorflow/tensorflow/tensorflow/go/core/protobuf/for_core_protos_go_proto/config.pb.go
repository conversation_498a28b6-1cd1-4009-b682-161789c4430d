// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: tensorflow/core/protobuf/config.proto

package for_core_protos_go_proto

import (
	cost_graph_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/cost_graph_go_proto"
	graph_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/graph_go_proto"
	step_stats_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/step_stats_go_proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Optimization level
type OptimizerOptions_Level int32

const (
	// L1 is the default level.
	// Optimization performed at L1 :
	// 1. Common subexpression elimination
	// 2. Constant folding
	OptimizerOptions_L1 OptimizerOptions_Level = 0
	// No optimizations
	OptimizerOptions_L0 OptimizerOptions_Level = -1
)

// Enum value maps for OptimizerOptions_Level.
var (
	OptimizerOptions_Level_name = map[int32]string{
		0:  "L1",
		-1: "L0",
	}
	OptimizerOptions_Level_value = map[string]int32{
		"L1": 0,
		"L0": -1,
	}
)

func (x OptimizerOptions_Level) Enum() *OptimizerOptions_Level {
	p := new(OptimizerOptions_Level)
	*p = x
	return p
}

func (x OptimizerOptions_Level) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OptimizerOptions_Level) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_core_protobuf_config_proto_enumTypes[0].Descriptor()
}

func (OptimizerOptions_Level) Type() protoreflect.EnumType {
	return &file_tensorflow_core_protobuf_config_proto_enumTypes[0]
}

func (x OptimizerOptions_Level) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OptimizerOptions_Level.Descriptor instead.
func (OptimizerOptions_Level) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{1, 0}
}

// Control the use of the compiler/jit.  Experimental.
type OptimizerOptions_GlobalJitLevel int32

const (
	OptimizerOptions_DEFAULT OptimizerOptions_GlobalJitLevel = 0 // Default setting ("off" now, but later expected to be "on")
	OptimizerOptions_OFF     OptimizerOptions_GlobalJitLevel = -1
	// The following settings turn on compilation, with higher values being
	// more aggressive.  Higher values may reduce opportunities for parallelism
	// and may use more memory.  (At present, there is no distinction, but this
	// is expected to change.)
	OptimizerOptions_ON_1 OptimizerOptions_GlobalJitLevel = 1
	OptimizerOptions_ON_2 OptimizerOptions_GlobalJitLevel = 2
)

// Enum value maps for OptimizerOptions_GlobalJitLevel.
var (
	OptimizerOptions_GlobalJitLevel_name = map[int32]string{
		0:  "DEFAULT",
		-1: "OFF",
		1:  "ON_1",
		2:  "ON_2",
	}
	OptimizerOptions_GlobalJitLevel_value = map[string]int32{
		"DEFAULT": 0,
		"OFF":     -1,
		"ON_1":    1,
		"ON_2":    2,
	}
)

func (x OptimizerOptions_GlobalJitLevel) Enum() *OptimizerOptions_GlobalJitLevel {
	p := new(OptimizerOptions_GlobalJitLevel)
	*p = x
	return p
}

func (x OptimizerOptions_GlobalJitLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OptimizerOptions_GlobalJitLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_core_protobuf_config_proto_enumTypes[1].Descriptor()
}

func (OptimizerOptions_GlobalJitLevel) Type() protoreflect.EnumType {
	return &file_tensorflow_core_protobuf_config_proto_enumTypes[1]
}

func (x OptimizerOptions_GlobalJitLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OptimizerOptions_GlobalJitLevel.Descriptor instead.
func (OptimizerOptions_GlobalJitLevel) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{1, 1}
}

// An enum that describes the state of the MLIR bridge rollout.
type ConfigProto_Experimental_MlirBridgeRollout int32

const (
	// If this field is left unspecified, the MLIR bridge may be selectively
	// enabled on a per graph basis.
	ConfigProto_Experimental_MLIR_BRIDGE_ROLLOUT_UNSPECIFIED ConfigProto_Experimental_MlirBridgeRollout = 0
	// Enabling the MLIR bridge enables it for all graphs in this session.
	ConfigProto_Experimental_MLIR_BRIDGE_ROLLOUT_ENABLED ConfigProto_Experimental_MlirBridgeRollout = 1
	// Disabling the MLIR bridge disables it for all graphs in this session.
	ConfigProto_Experimental_MLIR_BRIDGE_ROLLOUT_DISABLED ConfigProto_Experimental_MlirBridgeRollout = 2
	// Enable the MLIR bridge on a per graph basis based on an analysis of
	// the features used in the graph. If the features used by the graph are
	// supported by the MLIR bridge, the MLIR bridge will be used to run the
	// graph.
	ConfigProto_Experimental_MLIR_BRIDGE_ROLLOUT_SAFE_MODE_ENABLED ConfigProto_Experimental_MlirBridgeRollout = 3
	// Enable the MLIR bridge in a fallback mode on a per graph basis based
	// on an analysis of the features used in the graph.
	// Running the MLIR bridge in the fallback mode means that it is
	// executed and it commits all the changes to the TF graph in case
	// of success. And it does not in case of failures and let the old bridge
	// to process the TF graph.
	ConfigProto_Experimental_MLIR_BRIDGE_ROLLOUT_SAFE_MODE_FALLBACK_ENABLED ConfigProto_Experimental_MlirBridgeRollout = 4
)

// Enum value maps for ConfigProto_Experimental_MlirBridgeRollout.
var (
	ConfigProto_Experimental_MlirBridgeRollout_name = map[int32]string{
		0: "MLIR_BRIDGE_ROLLOUT_UNSPECIFIED",
		1: "MLIR_BRIDGE_ROLLOUT_ENABLED",
		2: "MLIR_BRIDGE_ROLLOUT_DISABLED",
		3: "MLIR_BRIDGE_ROLLOUT_SAFE_MODE_ENABLED",
		4: "MLIR_BRIDGE_ROLLOUT_SAFE_MODE_FALLBACK_ENABLED",
	}
	ConfigProto_Experimental_MlirBridgeRollout_value = map[string]int32{
		"MLIR_BRIDGE_ROLLOUT_UNSPECIFIED":                0,
		"MLIR_BRIDGE_ROLLOUT_ENABLED":                    1,
		"MLIR_BRIDGE_ROLLOUT_DISABLED":                   2,
		"MLIR_BRIDGE_ROLLOUT_SAFE_MODE_ENABLED":          3,
		"MLIR_BRIDGE_ROLLOUT_SAFE_MODE_FALLBACK_ENABLED": 4,
	}
)

func (x ConfigProto_Experimental_MlirBridgeRollout) Enum() *ConfigProto_Experimental_MlirBridgeRollout {
	p := new(ConfigProto_Experimental_MlirBridgeRollout)
	*p = x
	return p
}

func (x ConfigProto_Experimental_MlirBridgeRollout) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConfigProto_Experimental_MlirBridgeRollout) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_core_protobuf_config_proto_enumTypes[2].Descriptor()
}

func (ConfigProto_Experimental_MlirBridgeRollout) Type() protoreflect.EnumType {
	return &file_tensorflow_core_protobuf_config_proto_enumTypes[2]
}

func (x ConfigProto_Experimental_MlirBridgeRollout) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConfigProto_Experimental_MlirBridgeRollout.Descriptor instead.
func (ConfigProto_Experimental_MlirBridgeRollout) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{6, 1, 0}
}

// TODO(pbar) Turn this into a TraceOptions proto which allows
// tracing to be controlled in a more orthogonal manner?
type RunOptions_TraceLevel int32

const (
	RunOptions_NO_TRACE       RunOptions_TraceLevel = 0
	RunOptions_SOFTWARE_TRACE RunOptions_TraceLevel = 1
	RunOptions_HARDWARE_TRACE RunOptions_TraceLevel = 2
	RunOptions_FULL_TRACE     RunOptions_TraceLevel = 3
)

// Enum value maps for RunOptions_TraceLevel.
var (
	RunOptions_TraceLevel_name = map[int32]string{
		0: "NO_TRACE",
		1: "SOFTWARE_TRACE",
		2: "HARDWARE_TRACE",
		3: "FULL_TRACE",
	}
	RunOptions_TraceLevel_value = map[string]int32{
		"NO_TRACE":       0,
		"SOFTWARE_TRACE": 1,
		"HARDWARE_TRACE": 2,
		"FULL_TRACE":     3,
	}
)

func (x RunOptions_TraceLevel) Enum() *RunOptions_TraceLevel {
	p := new(RunOptions_TraceLevel)
	*p = x
	return p
}

func (x RunOptions_TraceLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RunOptions_TraceLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_core_protobuf_config_proto_enumTypes[3].Descriptor()
}

func (RunOptions_TraceLevel) Type() protoreflect.EnumType {
	return &file_tensorflow_core_protobuf_config_proto_enumTypes[3]
}

func (x RunOptions_TraceLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RunOptions_TraceLevel.Descriptor instead.
func (RunOptions_TraceLevel) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{7, 0}
}

type GPUOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Fraction of the available GPU memory to allocate for each process.
	// 1 means to allocate all of the GPU memory, 0.5 means the process
	// allocates up to ~50% of the available GPU memory.
	//
	// GPU memory is pre-allocated unless the allow_growth option is enabled.
	//
	// If greater than 1.0, uses CUDA unified memory to potentially oversubscribe
	// the amount of memory available on the GPU device by using host memory as a
	// swap space. Accessing memory not available on the device will be
	// significantly slower as that would require memory transfer between the host
	// and the device. Options to reduce the memory requirement should be
	// considered before enabling this option as this may come with a negative
	// performance impact. Oversubscription using the unified memory requires
	// Pascal class or newer GPUs and it is currently only supported on the Linux
	// operating system. See
	// https://docs.nvidia.com/cuda/cuda-c-programming-guide/index.html#um-requirements
	// for the detailed requirements.
	PerProcessGpuMemoryFraction float64 `protobuf:"fixed64,1,opt,name=per_process_gpu_memory_fraction,json=perProcessGpuMemoryFraction,proto3" json:"per_process_gpu_memory_fraction,omitempty"`
	// If true, the allocator does not pre-allocate the entire specified
	// GPU memory region, instead starting small and growing as needed.
	AllowGrowth bool `protobuf:"varint,4,opt,name=allow_growth,json=allowGrowth,proto3" json:"allow_growth,omitempty"`
	// The type of GPU allocation strategy to use.
	//
	// Allowed values:
	// "": The empty string (default) uses a system-chosen default
	//
	//	which may change over time.
	//
	// "BFC": A "Best-fit with coalescing" algorithm, simplified from a
	//
	//	version of dlmalloc.
	AllocatorType string `protobuf:"bytes,2,opt,name=allocator_type,json=allocatorType,proto3" json:"allocator_type,omitempty"`
	// Delay deletion of up to this many bytes to reduce the number of
	// interactions with gpu driver code.  If 0, the system chooses
	// a reasonable default (several MBs).
	DeferredDeletionBytes int64 `protobuf:"varint,3,opt,name=deferred_deletion_bytes,json=deferredDeletionBytes,proto3" json:"deferred_deletion_bytes,omitempty"`
	// A comma-separated list of GPU ids that determines the 'visible'
	// to 'virtual' mapping of GPU devices.  For example, if TensorFlow
	// can see 8 GPU devices in the process, and one wanted to map
	// visible GPU devices 5 and 3 as "/device:GPU:0", and "/device:GPU:1",
	// then one would specify this field as "5,3".  This field is similar in
	// spirit to the CUDA_VISIBLE_DEVICES environment variable, except
	// it applies to the visible GPU devices in the process.
	//
	// NOTE:
	//  1. The GPU driver provides the process with the visible GPUs
	//     in an order which is not guaranteed to have any correlation to
	//     the *physical* GPU id in the machine.  This field is used for
	//     remapping "visible" to "virtual", which means this operates only
	//     after the process starts.  Users are required to use vendor
	//     specific mechanisms (e.g., CUDA_VISIBLE_DEVICES) to control the
	//     physical to visible device mapping prior to invoking TensorFlow.
	//  2. In the code, the ids in this list are also called "platform GPU id"s,
	//     and the 'virtual' ids of GPU devices (i.e. the ids in the device
	//     name "/device:GPU:<id>") are also called "TF GPU id"s. Please
	//     refer to third_party/tensorflow/core/common_runtime/gpu/gpu_id.h
	//     for more information.
	VisibleDeviceList string `protobuf:"bytes,5,opt,name=visible_device_list,json=visibleDeviceList,proto3" json:"visible_device_list,omitempty"`
	// In the event polling loop sleep this many microseconds between
	// PollEvents calls, when the queue is not empty.  If value is not
	// set or set to 0, gets set to a non-zero default.
	PollingActiveDelayUsecs int32 `protobuf:"varint,6,opt,name=polling_active_delay_usecs,json=pollingActiveDelayUsecs,proto3" json:"polling_active_delay_usecs,omitempty"`
	// This field is deprecated and ignored.
	PollingInactiveDelayMsecs int32 `protobuf:"varint,7,opt,name=polling_inactive_delay_msecs,json=pollingInactiveDelayMsecs,proto3" json:"polling_inactive_delay_msecs,omitempty"`
	// Force all tensors to be gpu_compatible. On a GPU-enabled TensorFlow,
	// enabling this option forces all CPU tensors to be allocated with Cuda
	// pinned memory. Normally, TensorFlow will infer which tensors should be
	// allocated as the pinned memory. But in case where the inference is
	// incomplete, this option can significantly speed up the cross-device memory
	// copy performance as long as it fits the memory.
	// Note that this option is not something that should be
	// enabled by default for unknown or very large models, since all Cuda pinned
	// memory is unpageable, having too much pinned memory might negatively impact
	// the overall host system performance.
	ForceGpuCompatible bool `protobuf:"varint,8,opt,name=force_gpu_compatible,json=forceGpuCompatible,proto3" json:"force_gpu_compatible,omitempty"`
	// Everything inside experimental is subject to change and is not subject
	// to API stability guarantees in
	// https://www.tensorflow.org/guide/version_compat.
	Experimental *GPUOptions_Experimental `protobuf:"bytes,9,opt,name=experimental,proto3" json:"experimental,omitempty"`
}

func (x *GPUOptions) Reset() {
	*x = GPUOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GPUOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GPUOptions) ProtoMessage() {}

func (x *GPUOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GPUOptions.ProtoReflect.Descriptor instead.
func (*GPUOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{0}
}

func (x *GPUOptions) GetPerProcessGpuMemoryFraction() float64 {
	if x != nil {
		return x.PerProcessGpuMemoryFraction
	}
	return 0
}

func (x *GPUOptions) GetAllowGrowth() bool {
	if x != nil {
		return x.AllowGrowth
	}
	return false
}

func (x *GPUOptions) GetAllocatorType() string {
	if x != nil {
		return x.AllocatorType
	}
	return ""
}

func (x *GPUOptions) GetDeferredDeletionBytes() int64 {
	if x != nil {
		return x.DeferredDeletionBytes
	}
	return 0
}

func (x *GPUOptions) GetVisibleDeviceList() string {
	if x != nil {
		return x.VisibleDeviceList
	}
	return ""
}

func (x *GPUOptions) GetPollingActiveDelayUsecs() int32 {
	if x != nil {
		return x.PollingActiveDelayUsecs
	}
	return 0
}

func (x *GPUOptions) GetPollingInactiveDelayMsecs() int32 {
	if x != nil {
		return x.PollingInactiveDelayMsecs
	}
	return 0
}

func (x *GPUOptions) GetForceGpuCompatible() bool {
	if x != nil {
		return x.ForceGpuCompatible
	}
	return false
}

func (x *GPUOptions) GetExperimental() *GPUOptions_Experimental {
	if x != nil {
		return x.Experimental
	}
	return nil
}

// Options passed to the graph optimizer
type OptimizerOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// If true, optimize the graph using common subexpression elimination.
	// Note: the optimization Level L1 will override this setting to true. So in
	// order to disable common subexpression elimination the opt_level has to be
	// set to L0.
	DoCommonSubexpressionElimination bool `protobuf:"varint,1,opt,name=do_common_subexpression_elimination,json=doCommonSubexpressionElimination,proto3" json:"do_common_subexpression_elimination,omitempty"`
	// If true, perform constant folding optimization on the graph.
	// Note: the optimization Level L1 will override this setting to true. So in
	// order to disable constant folding the opt_level has to be set to L0.
	DoConstantFolding bool `protobuf:"varint,2,opt,name=do_constant_folding,json=doConstantFolding,proto3" json:"do_constant_folding,omitempty"`
	// Constant folding optimization replaces tensors whose values can be
	// predetermined, with constant nodes. To avoid inserting too large constants,
	// the size of each constant created can be limited. If this value is zero, a
	// default limit of 10 MiB will be applied. If constant folding optimization
	// is disabled, this value is ignored.
	MaxFoldedConstantInBytes int64 `protobuf:"varint,6,opt,name=max_folded_constant_in_bytes,json=maxFoldedConstantInBytes,proto3" json:"max_folded_constant_in_bytes,omitempty"`
	// If true, perform function inlining on the graph.
	DoFunctionInlining bool `protobuf:"varint,4,opt,name=do_function_inlining,json=doFunctionInlining,proto3" json:"do_function_inlining,omitempty"`
	// Overall optimization level. The actual optimizations applied will be the
	// logical OR of the flags that this level implies and any flags already set.
	OptLevel       OptimizerOptions_Level          `protobuf:"varint,3,opt,name=opt_level,json=optLevel,proto3,enum=tensorflow.OptimizerOptions_Level" json:"opt_level,omitempty"`
	GlobalJitLevel OptimizerOptions_GlobalJitLevel `protobuf:"varint,5,opt,name=global_jit_level,json=globalJitLevel,proto3,enum=tensorflow.OptimizerOptions_GlobalJitLevel" json:"global_jit_level,omitempty"`
	// CPU code will be autoclustered only if global_jit_level >= ON_1 and either:
	//   - this flag is true, or
	//   - TF_XLA_FLAGS contains --tf_xla_cpu_global_jit=true.
	CpuGlobalJit bool `protobuf:"varint,7,opt,name=cpu_global_jit,json=cpuGlobalJit,proto3" json:"cpu_global_jit,omitempty"`
}

func (x *OptimizerOptions) Reset() {
	*x = OptimizerOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OptimizerOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptimizerOptions) ProtoMessage() {}

func (x *OptimizerOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptimizerOptions.ProtoReflect.Descriptor instead.
func (*OptimizerOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{1}
}

func (x *OptimizerOptions) GetDoCommonSubexpressionElimination() bool {
	if x != nil {
		return x.DoCommonSubexpressionElimination
	}
	return false
}

func (x *OptimizerOptions) GetDoConstantFolding() bool {
	if x != nil {
		return x.DoConstantFolding
	}
	return false
}

func (x *OptimizerOptions) GetMaxFoldedConstantInBytes() int64 {
	if x != nil {
		return x.MaxFoldedConstantInBytes
	}
	return 0
}

func (x *OptimizerOptions) GetDoFunctionInlining() bool {
	if x != nil {
		return x.DoFunctionInlining
	}
	return false
}

func (x *OptimizerOptions) GetOptLevel() OptimizerOptions_Level {
	if x != nil {
		return x.OptLevel
	}
	return OptimizerOptions_L1
}

func (x *OptimizerOptions) GetGlobalJitLevel() OptimizerOptions_GlobalJitLevel {
	if x != nil {
		return x.GlobalJitLevel
	}
	return OptimizerOptions_DEFAULT
}

func (x *OptimizerOptions) GetCpuGlobalJit() bool {
	if x != nil {
		return x.CpuGlobalJit
	}
	return false
}

type GraphOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// If true, use control flow to schedule the activation of Recv nodes.
	// (Currently ignored.)
	EnableRecvScheduling bool `protobuf:"varint,2,opt,name=enable_recv_scheduling,json=enableRecvScheduling,proto3" json:"enable_recv_scheduling,omitempty"`
	// Options controlling how graph is optimized.
	OptimizerOptions *OptimizerOptions `protobuf:"bytes,3,opt,name=optimizer_options,json=optimizerOptions,proto3" json:"optimizer_options,omitempty"`
	// The number of steps to run before returning a cost model detailing
	// the memory usage and performance of each node of the graph. 0 means
	// no cost model.
	BuildCostModel int64 `protobuf:"varint,4,opt,name=build_cost_model,json=buildCostModel,proto3" json:"build_cost_model,omitempty"`
	// The number of steps to skip before collecting statistics for the
	// cost model.
	BuildCostModelAfter int64 `protobuf:"varint,9,opt,name=build_cost_model_after,json=buildCostModelAfter,proto3" json:"build_cost_model_after,omitempty"`
	// Annotate each Node with Op output shape data, to the extent it can
	// be statically inferred.
	InferShapes bool `protobuf:"varint,5,opt,name=infer_shapes,json=inferShapes,proto3" json:"infer_shapes,omitempty"`
	// Only place the subgraphs that are run, rather than the entire graph.
	//
	// This is useful for interactive graph building, where one might
	// produce graphs that cannot be placed during the debugging
	// process.  In particular, it allows the client to continue work in
	// a session after adding a node to a graph whose placement
	// constraints are unsatisfiable.
	PlacePrunedGraph bool `protobuf:"varint,6,opt,name=place_pruned_graph,json=placePrunedGraph,proto3" json:"place_pruned_graph,omitempty"`
	// If true, transfer float values between processes as bfloat16.
	EnableBfloat16Sendrecv bool `protobuf:"varint,7,opt,name=enable_bfloat16_sendrecv,json=enableBfloat16Sendrecv,proto3" json:"enable_bfloat16_sendrecv,omitempty"`
	// If > 0, record a timeline every this many steps.
	// EXPERIMENTAL: This currently has no effect in MasterSession.
	TimelineStep int32 `protobuf:"varint,8,opt,name=timeline_step,json=timelineStep,proto3" json:"timeline_step,omitempty"`
	// Options that control the type and amount of graph rewriting.
	// Not currently configurable via the public Python API (i.e. there is no API
	// stability guarantee if you import RewriterConfig explicitly).
	RewriteOptions *RewriterConfig `protobuf:"bytes,10,opt,name=rewrite_options,json=rewriteOptions,proto3" json:"rewrite_options,omitempty"`
}

func (x *GraphOptions) Reset() {
	*x = GraphOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GraphOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GraphOptions) ProtoMessage() {}

func (x *GraphOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GraphOptions.ProtoReflect.Descriptor instead.
func (*GraphOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{2}
}

func (x *GraphOptions) GetEnableRecvScheduling() bool {
	if x != nil {
		return x.EnableRecvScheduling
	}
	return false
}

func (x *GraphOptions) GetOptimizerOptions() *OptimizerOptions {
	if x != nil {
		return x.OptimizerOptions
	}
	return nil
}

func (x *GraphOptions) GetBuildCostModel() int64 {
	if x != nil {
		return x.BuildCostModel
	}
	return 0
}

func (x *GraphOptions) GetBuildCostModelAfter() int64 {
	if x != nil {
		return x.BuildCostModelAfter
	}
	return 0
}

func (x *GraphOptions) GetInferShapes() bool {
	if x != nil {
		return x.InferShapes
	}
	return false
}

func (x *GraphOptions) GetPlacePrunedGraph() bool {
	if x != nil {
		return x.PlacePrunedGraph
	}
	return false
}

func (x *GraphOptions) GetEnableBfloat16Sendrecv() bool {
	if x != nil {
		return x.EnableBfloat16Sendrecv
	}
	return false
}

func (x *GraphOptions) GetTimelineStep() int32 {
	if x != nil {
		return x.TimelineStep
	}
	return 0
}

func (x *GraphOptions) GetRewriteOptions() *RewriterConfig {
	if x != nil {
		return x.RewriteOptions
	}
	return nil
}

type ThreadPoolOptionProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The number of threads in the pool.
	//
	// 0 means the system picks a value based on where this option proto is used
	// (see the declaration of the specific field for more info).
	NumThreads int32 `protobuf:"varint,1,opt,name=num_threads,json=numThreads,proto3" json:"num_threads,omitempty"`
	// The global name of the threadpool.
	//
	// If empty, then the threadpool is made and used according to the scope it's
	// in - e.g., for a session threadpool, it is used by that session only.
	//
	// If non-empty, then:
	//   - a global threadpool associated with this name is looked
	//     up or created. This allows, for example, sharing one threadpool across
	//     many sessions (e.g., like the default behavior, if
	//     inter_op_parallelism_threads is not configured), but still partitioning
	//     into a large and small pool.
	//   - if the threadpool for this global_name already exists, then it is an
	//     error if the existing pool was created using a different num_threads
	//     value as is specified on this call.
	//   - threadpools created this way are never garbage collected.
	GlobalName string `protobuf:"bytes,2,opt,name=global_name,json=globalName,proto3" json:"global_name,omitempty"`
}

func (x *ThreadPoolOptionProto) Reset() {
	*x = ThreadPoolOptionProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThreadPoolOptionProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadPoolOptionProto) ProtoMessage() {}

func (x *ThreadPoolOptionProto) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadPoolOptionProto.ProtoReflect.Descriptor instead.
func (*ThreadPoolOptionProto) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{3}
}

func (x *ThreadPoolOptionProto) GetNumThreads() int32 {
	if x != nil {
		return x.NumThreads
	}
	return 0
}

func (x *ThreadPoolOptionProto) GetGlobalName() string {
	if x != nil {
		return x.GlobalName
	}
	return ""
}

type RPCOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// If true, always use RPC to contact the session target.
	//
	// If false (the default option), TensorFlow may use an optimized
	// transport for client-master communication that avoids the RPC
	// stack. This option is primarily for used testing the RPC stack.
	UseRpcForInprocessMaster bool `protobuf:"varint,1,opt,name=use_rpc_for_inprocess_master,json=useRpcForInprocessMaster,proto3" json:"use_rpc_for_inprocess_master,omitempty"`
	// The compression algorithm to be used. One of "deflate", "gzip".
	CompressionAlgorithm string `protobuf:"bytes,2,opt,name=compression_algorithm,json=compressionAlgorithm,proto3" json:"compression_algorithm,omitempty"`
	// If compression_algorithm is set, the compression level to be used.
	// From 0 (no compression), up to 3.
	CompressionLevel int32 `protobuf:"varint,3,opt,name=compression_level,json=compressionLevel,proto3" json:"compression_level,omitempty"`
	// Setting cache_rpc_response to true will enable sender side caching of
	// response for RecvTensorAsync and RecvBufAsync to allow receiver to retry
	// requests . This is only necessary when the network fabric is experiencing a
	// significant error rate.  Without it we'll fail a step on an network error,
	// while with it we'll be able to complete long steps (like complex
	// initializations) in the face of some network errors during RecvTensor.
	CacheRpcResponse bool `protobuf:"varint,4,opt,name=cache_rpc_response,json=cacheRpcResponse,proto3" json:"cache_rpc_response,omitempty"`
	// Disables TCP connection sharing when opening a new RPC channel.
	DisableSessionConnectionSharing bool `protobuf:"varint,5,opt,name=disable_session_connection_sharing,json=disableSessionConnectionSharing,proto3" json:"disable_session_connection_sharing,omitempty"`
	// Setting num_channels_per_target > 0 allows uses of multiple channels to
	// communicate to the same target. This can be used to improve the aggregate
	// throughput on high speed links (e.g 100G) where single connection is not
	// sufficient to maximize link utilization. Note that a single RPC only goes
	// on a single channel, this only helps in situations where there are multiple
	// transfers to the same target overlapping in time.
	NumChannelsPerTarget int32 `protobuf:"varint,6,opt,name=num_channels_per_target,json=numChannelsPerTarget,proto3" json:"num_channels_per_target,omitempty"`
}

func (x *RPCOptions) Reset() {
	*x = RPCOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RPCOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RPCOptions) ProtoMessage() {}

func (x *RPCOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RPCOptions.ProtoReflect.Descriptor instead.
func (*RPCOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{4}
}

func (x *RPCOptions) GetUseRpcForInprocessMaster() bool {
	if x != nil {
		return x.UseRpcForInprocessMaster
	}
	return false
}

func (x *RPCOptions) GetCompressionAlgorithm() string {
	if x != nil {
		return x.CompressionAlgorithm
	}
	return ""
}

func (x *RPCOptions) GetCompressionLevel() int32 {
	if x != nil {
		return x.CompressionLevel
	}
	return 0
}

func (x *RPCOptions) GetCacheRpcResponse() bool {
	if x != nil {
		return x.CacheRpcResponse
	}
	return false
}

func (x *RPCOptions) GetDisableSessionConnectionSharing() bool {
	if x != nil {
		return x.DisableSessionConnectionSharing
	}
	return false
}

func (x *RPCOptions) GetNumChannelsPerTarget() int32 {
	if x != nil {
		return x.NumChannelsPerTarget
	}
	return 0
}

// Metadata about the session.
//
// This can be used by the runtime and the Ops for debugging, monitoring, etc.
//
// The (name, version) tuple is expected to be a unique identifier for
// sessions within the same process.
//
// NOTE: This is currently used and propagated only by the direct session.
type SessionMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The version is optional. If set, needs to be >= 0.
	Version int64 `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *SessionMetadata) Reset() {
	*x = SessionMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SessionMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionMetadata) ProtoMessage() {}

func (x *SessionMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionMetadata.ProtoReflect.Descriptor instead.
func (*SessionMetadata) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{5}
}

func (x *SessionMetadata) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SessionMetadata) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

// Session configuration parameters.
// The system picks appropriate values for fields that are not set.
type ConfigProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map from device type name (e.g., "CPU" or "GPU" ) to maximum
	// number of devices of that type to use.  If a particular device
	// type is not found in the map, the system picks an appropriate
	// number.
	DeviceCount map[string]int32 `protobuf:"bytes,1,rep,name=device_count,json=deviceCount,proto3" json:"device_count,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// The execution of an individual op (for some op types) can be
	// parallelized on a pool of intra_op_parallelism_threads.
	// 0 means the system picks an appropriate number.
	//
	// If you create an ordinary session, e.g., from Python or C++,
	// then there is exactly one intra op thread pool per process.
	// The first session created determines the number of threads in this pool.
	// All subsequent sessions reuse/share this one global pool.
	//
	// There are notable exceptions to the default behavior described above:
	//  1. There is an environment variable  for overriding this thread pool,
	//     named TF_OVERRIDE_GLOBAL_THREADPOOL.
	//  2. When connecting to a server, such as a remote `tf.train.Server`
	//     instance, then this option will be ignored altogether.
	IntraOpParallelismThreads int32 `protobuf:"varint,2,opt,name=intra_op_parallelism_threads,json=intraOpParallelismThreads,proto3" json:"intra_op_parallelism_threads,omitempty"`
	// Nodes that perform blocking operations are enqueued on a pool of
	// inter_op_parallelism_threads available in each process.
	//
	// 0 means the system picks an appropriate number.
	// Negative means all operations are performed in caller's thread.
	//
	// Note that the first Session created in the process sets the
	// number of threads for all future sessions unless use_per_session_threads is
	// true or session_inter_op_thread_pool is configured.
	InterOpParallelismThreads int32 `protobuf:"varint,5,opt,name=inter_op_parallelism_threads,json=interOpParallelismThreads,proto3" json:"inter_op_parallelism_threads,omitempty"`
	// If true, use a new set of threads for this session rather than the global
	// pool of threads. Only supported by direct sessions.
	//
	// If false, use the global threads created by the first session, or the
	// per-session thread pools configured by session_inter_op_thread_pool.
	//
	// This option is deprecated. The same effect can be achieved by setting
	// session_inter_op_thread_pool to have one element, whose num_threads equals
	// inter_op_parallelism_threads.
	UsePerSessionThreads bool `protobuf:"varint,9,opt,name=use_per_session_threads,json=usePerSessionThreads,proto3" json:"use_per_session_threads,omitempty"`
	// This option is experimental - it may be replaced with a different mechanism
	// in the future.
	//
	// Configures session thread pools. If this is configured, then RunOptions for
	// a Run call can select the thread pool to use.
	//
	// The intended use is for when some session invocations need to run in a
	// background pool limited to a small number of threads:
	// - For example, a session may be configured to have one large pool (for
	// regular compute) and one small pool (for periodic, low priority work);
	// using the small pool is currently the mechanism for limiting the inter-op
	// parallelism of the low priority work.  Note that it does not limit the
	// parallelism of work spawned by a single op kernel implementation.
	// - Using this setting is normally not needed in training, but may help some
	// serving use cases.
	// - It is also generally recommended to set the global_name field of this
	// proto, to avoid creating multiple large pools. It is typically better to
	// run the non-low-priority work, even across sessions, in a single large
	// pool.
	SessionInterOpThreadPool []*ThreadPoolOptionProto `protobuf:"bytes,12,rep,name=session_inter_op_thread_pool,json=sessionInterOpThreadPool,proto3" json:"session_inter_op_thread_pool,omitempty"`
	// Assignment of Nodes to Devices is recomputed every placement_period
	// steps until the system warms up (at which point the recomputation
	// typically slows down automatically).
	PlacementPeriod int32 `protobuf:"varint,3,opt,name=placement_period,json=placementPeriod,proto3" json:"placement_period,omitempty"`
	// When any filters are present sessions will ignore all devices which do not
	// match the filters. Each filter can be partially specified, e.g. "/job:ps"
	// "/job:worker/replica:3", etc.
	DeviceFilters []string `protobuf:"bytes,4,rep,name=device_filters,json=deviceFilters,proto3" json:"device_filters,omitempty"`
	// Options that apply to all GPUs.
	GpuOptions *GPUOptions `protobuf:"bytes,6,opt,name=gpu_options,json=gpuOptions,proto3" json:"gpu_options,omitempty"`
	// Whether soft placement is allowed. If allow_soft_placement is true,
	// an op will be placed on CPU if
	//  1. there's no GPU implementation for the OP
	//
	// or
	//  2. no GPU devices are known or registered
	//
	// or
	//  3. need to co-locate with reftype input(s) which are from CPU.
	AllowSoftPlacement bool `protobuf:"varint,7,opt,name=allow_soft_placement,json=allowSoftPlacement,proto3" json:"allow_soft_placement,omitempty"`
	// Whether device placements should be logged.
	LogDevicePlacement bool `protobuf:"varint,8,opt,name=log_device_placement,json=logDevicePlacement,proto3" json:"log_device_placement,omitempty"`
	// Options that apply to all graphs.
	GraphOptions *GraphOptions `protobuf:"bytes,10,opt,name=graph_options,json=graphOptions,proto3" json:"graph_options,omitempty"`
	// Global timeout for all blocking operations in this session.  If non-zero,
	// and not overridden on a per-operation basis, this value will be used as the
	// deadline for all blocking operations.
	OperationTimeoutInMs int64 `protobuf:"varint,11,opt,name=operation_timeout_in_ms,json=operationTimeoutInMs,proto3" json:"operation_timeout_in_ms,omitempty"`
	// Options that apply when this session uses the distributed runtime.
	RpcOptions *RPCOptions `protobuf:"bytes,13,opt,name=rpc_options,json=rpcOptions,proto3" json:"rpc_options,omitempty"`
	// Optional list of all workers to use in this session.
	ClusterDef *ClusterDef `protobuf:"bytes,14,opt,name=cluster_def,json=clusterDef,proto3" json:"cluster_def,omitempty"`
	// If true, any resources such as Variables used in the session will not be
	// shared with other sessions. However, when clusterspec propagation is
	// enabled, this field is ignored and sessions are always isolated.
	IsolateSessionState bool `protobuf:"varint,15,opt,name=isolate_session_state,json=isolateSessionState,proto3" json:"isolate_session_state,omitempty"`
	// When true, WorkerSessions are created with device attributes from the
	// full cluster.
	// This is helpful when a worker wants to partition a graph
	// (for example during a PartitionedCallOp).
	ShareClusterDevicesInSession bool                      `protobuf:"varint,17,opt,name=share_cluster_devices_in_session,json=shareClusterDevicesInSession,proto3" json:"share_cluster_devices_in_session,omitempty"`
	Experimental                 *ConfigProto_Experimental `protobuf:"bytes,16,opt,name=experimental,proto3" json:"experimental,omitempty"`
}

func (x *ConfigProto) Reset() {
	*x = ConfigProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigProto) ProtoMessage() {}

func (x *ConfigProto) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigProto.ProtoReflect.Descriptor instead.
func (*ConfigProto) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{6}
}

func (x *ConfigProto) GetDeviceCount() map[string]int32 {
	if x != nil {
		return x.DeviceCount
	}
	return nil
}

func (x *ConfigProto) GetIntraOpParallelismThreads() int32 {
	if x != nil {
		return x.IntraOpParallelismThreads
	}
	return 0
}

func (x *ConfigProto) GetInterOpParallelismThreads() int32 {
	if x != nil {
		return x.InterOpParallelismThreads
	}
	return 0
}

func (x *ConfigProto) GetUsePerSessionThreads() bool {
	if x != nil {
		return x.UsePerSessionThreads
	}
	return false
}

func (x *ConfigProto) GetSessionInterOpThreadPool() []*ThreadPoolOptionProto {
	if x != nil {
		return x.SessionInterOpThreadPool
	}
	return nil
}

func (x *ConfigProto) GetPlacementPeriod() int32 {
	if x != nil {
		return x.PlacementPeriod
	}
	return 0
}

func (x *ConfigProto) GetDeviceFilters() []string {
	if x != nil {
		return x.DeviceFilters
	}
	return nil
}

func (x *ConfigProto) GetGpuOptions() *GPUOptions {
	if x != nil {
		return x.GpuOptions
	}
	return nil
}

func (x *ConfigProto) GetAllowSoftPlacement() bool {
	if x != nil {
		return x.AllowSoftPlacement
	}
	return false
}

func (x *ConfigProto) GetLogDevicePlacement() bool {
	if x != nil {
		return x.LogDevicePlacement
	}
	return false
}

func (x *ConfigProto) GetGraphOptions() *GraphOptions {
	if x != nil {
		return x.GraphOptions
	}
	return nil
}

func (x *ConfigProto) GetOperationTimeoutInMs() int64 {
	if x != nil {
		return x.OperationTimeoutInMs
	}
	return 0
}

func (x *ConfigProto) GetRpcOptions() *RPCOptions {
	if x != nil {
		return x.RpcOptions
	}
	return nil
}

func (x *ConfigProto) GetClusterDef() *ClusterDef {
	if x != nil {
		return x.ClusterDef
	}
	return nil
}

func (x *ConfigProto) GetIsolateSessionState() bool {
	if x != nil {
		return x.IsolateSessionState
	}
	return false
}

func (x *ConfigProto) GetShareClusterDevicesInSession() bool {
	if x != nil {
		return x.ShareClusterDevicesInSession
	}
	return false
}

func (x *ConfigProto) GetExperimental() *ConfigProto_Experimental {
	if x != nil {
		return x.Experimental
	}
	return nil
}

// Options for a single Run() call.
type RunOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraceLevel RunOptions_TraceLevel `protobuf:"varint,1,opt,name=trace_level,json=traceLevel,proto3,enum=tensorflow.RunOptions_TraceLevel" json:"trace_level,omitempty"`
	// Time to wait for operation to complete in milliseconds.
	TimeoutInMs int64 `protobuf:"varint,2,opt,name=timeout_in_ms,json=timeoutInMs,proto3" json:"timeout_in_ms,omitempty"`
	// The thread pool to use, if session_inter_op_thread_pool is configured.
	// To use the caller thread set this to -1 - this uses the caller thread
	// to execute Session::Run() and thus avoids a context switch. Using the
	// caller thread to execute Session::Run() should be done ONLY for simple
	// graphs, where the overhead of an additional context switch is
	// comparable with the overhead of Session::Run().
	InterOpThreadPool int32 `protobuf:"varint,3,opt,name=inter_op_thread_pool,json=interOpThreadPool,proto3" json:"inter_op_thread_pool,omitempty"`
	// Whether the partition graph(s) executed by the executor(s) should be
	// outputted via RunMetadata.
	OutputPartitionGraphs bool `protobuf:"varint,5,opt,name=output_partition_graphs,json=outputPartitionGraphs,proto3" json:"output_partition_graphs,omitempty"`
	// EXPERIMENTAL.  Options used to initialize DebuggerState, if enabled.
	DebugOptions *DebugOptions `protobuf:"bytes,6,opt,name=debug_options,json=debugOptions,proto3" json:"debug_options,omitempty"`
	// When enabled, causes tensor allocation information to be included in
	// the error message when the Run() call fails because the allocator ran
	// out of memory (OOM).
	//
	// Enabling this option can slow down the Run() call.
	ReportTensorAllocationsUponOom bool                     `protobuf:"varint,7,opt,name=report_tensor_allocations_upon_oom,json=reportTensorAllocationsUponOom,proto3" json:"report_tensor_allocations_upon_oom,omitempty"`
	Experimental                   *RunOptions_Experimental `protobuf:"bytes,8,opt,name=experimental,proto3" json:"experimental,omitempty"`
}

func (x *RunOptions) Reset() {
	*x = RunOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunOptions) ProtoMessage() {}

func (x *RunOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunOptions.ProtoReflect.Descriptor instead.
func (*RunOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{7}
}

func (x *RunOptions) GetTraceLevel() RunOptions_TraceLevel {
	if x != nil {
		return x.TraceLevel
	}
	return RunOptions_NO_TRACE
}

func (x *RunOptions) GetTimeoutInMs() int64 {
	if x != nil {
		return x.TimeoutInMs
	}
	return 0
}

func (x *RunOptions) GetInterOpThreadPool() int32 {
	if x != nil {
		return x.InterOpThreadPool
	}
	return 0
}

func (x *RunOptions) GetOutputPartitionGraphs() bool {
	if x != nil {
		return x.OutputPartitionGraphs
	}
	return false
}

func (x *RunOptions) GetDebugOptions() *DebugOptions {
	if x != nil {
		return x.DebugOptions
	}
	return nil
}

func (x *RunOptions) GetReportTensorAllocationsUponOom() bool {
	if x != nil {
		return x.ReportTensorAllocationsUponOom
	}
	return false
}

func (x *RunOptions) GetExperimental() *RunOptions_Experimental {
	if x != nil {
		return x.Experimental
	}
	return nil
}

// Metadata output (i.e., non-Tensor) for a single Run() call.
type RunMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Statistics traced for this step. Populated if tracing is turned on via the
	// "RunOptions" proto.
	// EXPERIMENTAL: The format and set of events may change in future versions.
	StepStats *step_stats_go_proto.StepStats `protobuf:"bytes,1,opt,name=step_stats,json=stepStats,proto3" json:"step_stats,omitempty"`
	// The cost graph for the computation defined by the run call.
	CostGraph *cost_graph_go_proto.CostGraphDef `protobuf:"bytes,2,opt,name=cost_graph,json=costGraph,proto3" json:"cost_graph,omitempty"`
	// Graphs of the partitions executed by executors.
	PartitionGraphs []*graph_go_proto.GraphDef `protobuf:"bytes,3,rep,name=partition_graphs,json=partitionGraphs,proto3" json:"partition_graphs,omitempty"`
	// This is only populated for graphs that are run as functions in TensorFlow
	// V2. There will be an entry below for each function that is traced.
	// The main use cases of the post_optimization_graph and the partition_graphs
	// is to give the caller insight into the graphs that were actually run by the
	// runtime. Additional information (such as those in step_stats) will match
	// these graphs.
	// We also include the pre_optimization_graph since it is usually easier to
	// read, and is helpful in situations where the caller wants to get a high
	// level idea of what the built graph looks like (since the various graph
	// optimization passes might change the structure of the graph significantly).
	FunctionGraphs []*RunMetadata_FunctionGraphs `protobuf:"bytes,4,rep,name=function_graphs,json=functionGraphs,proto3" json:"function_graphs,omitempty"`
}

func (x *RunMetadata) Reset() {
	*x = RunMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunMetadata) ProtoMessage() {}

func (x *RunMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunMetadata.ProtoReflect.Descriptor instead.
func (*RunMetadata) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{8}
}

func (x *RunMetadata) GetStepStats() *step_stats_go_proto.StepStats {
	if x != nil {
		return x.StepStats
	}
	return nil
}

func (x *RunMetadata) GetCostGraph() *cost_graph_go_proto.CostGraphDef {
	if x != nil {
		return x.CostGraph
	}
	return nil
}

func (x *RunMetadata) GetPartitionGraphs() []*graph_go_proto.GraphDef {
	if x != nil {
		return x.PartitionGraphs
	}
	return nil
}

func (x *RunMetadata) GetFunctionGraphs() []*RunMetadata_FunctionGraphs {
	if x != nil {
		return x.FunctionGraphs
	}
	return nil
}

// Defines a connection between two tensors in a `GraphDef`.
type TensorConnection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A tensor name. The value of this tensor will be substituted for
	// the tensor named in `to_tensor`.
	FromTensor string `protobuf:"bytes,1,opt,name=from_tensor,json=fromTensor,proto3" json:"from_tensor,omitempty"`
	// A tensor name. The value of this tensor will be bound to the
	// value of the tensor named in `from_tensor`.
	ToTensor string `protobuf:"bytes,2,opt,name=to_tensor,json=toTensor,proto3" json:"to_tensor,omitempty"`
}

func (x *TensorConnection) Reset() {
	*x = TensorConnection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TensorConnection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorConnection) ProtoMessage() {}

func (x *TensorConnection) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorConnection.ProtoReflect.Descriptor instead.
func (*TensorConnection) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{9}
}

func (x *TensorConnection) GetFromTensor() string {
	if x != nil {
		return x.FromTensor
	}
	return ""
}

func (x *TensorConnection) GetToTensor() string {
	if x != nil {
		return x.ToTensor
	}
	return ""
}

// Defines a subgraph in another `GraphDef` as a set of feed points and nodes
// to be fetched or executed.
//
// Compare with the arguments to `Session::Run()`.
type CallableOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Tensors to be fed in the callable. Each feed is the name of a tensor.
	Feed []string `protobuf:"bytes,1,rep,name=feed,proto3" json:"feed,omitempty"`
	// Fetches. A list of tensor names. The caller of the callable expects a
	// tensor to be returned for each fetch[i] (see RunStepResponse.tensor). The
	// order of specified fetches does not change the execution order.
	Fetch []string `protobuf:"bytes,2,rep,name=fetch,proto3" json:"fetch,omitempty"`
	// Target Nodes. A list of node names. The named nodes will be run by the
	// callable but their outputs will not be returned.
	Target []string `protobuf:"bytes,3,rep,name=target,proto3" json:"target,omitempty"`
	// Options that will be applied to each run.
	RunOptions *RunOptions `protobuf:"bytes,4,opt,name=run_options,json=runOptions,proto3" json:"run_options,omitempty"`
	// Tensors to be connected in the callable. Each TensorConnection denotes
	// a pair of tensors in the graph, between which an edge will be created
	// in the callable.
	TensorConnection []*TensorConnection `protobuf:"bytes,5,rep,name=tensor_connection,json=tensorConnection,proto3" json:"tensor_connection,omitempty"`
	// The Tensor objects fed in the callable and fetched from the callable
	// are expected to be backed by host (CPU) memory by default.
	//
	// The options below allow changing that - feeding tensors backed by
	// device memory, or returning tensors that are backed by device memory.
	//
	// The maps below map the name of a feed/fetch tensor (which appears in
	// 'feed' or 'fetch' fields above), to the fully qualified name of the device
	// owning the memory backing the contents of the tensor.
	//
	// For example, creating a callable with the following options:
	//
	//	CallableOptions {
	//	  feed: "a:0"
	//	  feed: "b:0"
	//
	//	  fetch: "x:0"
	//	  fetch: "y:0"
	//
	//	  feed_devices: {
	//	    "a:0": "/job:localhost/replica:0/task:0/device:GPU:0"
	//	  }
	//
	//	  fetch_devices: {
	//	    "y:0": "/job:localhost/replica:0/task:0/device:GPU:0"
	//	 }
	//	}
	//
	// means that the Callable expects:
	// - The first argument ("a:0") is a Tensor backed by GPU memory.
	// - The second argument ("b:0") is a Tensor backed by host memory.
	// and of its return values:
	// - The first output ("x:0") will be backed by host memory.
	// - The second output ("y:0") will be backed by GPU memory.
	//
	// FEEDS:
	// It is the responsibility of the caller to ensure that the memory of the fed
	// tensors will be correctly initialized and synchronized before it is
	// accessed by operations executed during the call to Session::RunCallable().
	//
	// This is typically ensured by using the TensorFlow memory allocators
	// (Device::GetAllocator()) to create the Tensor to be fed.
	//
	// Alternatively, for CUDA-enabled GPU devices, this typically means that the
	// operation that produced the contents of the tensor has completed, i.e., the
	// CUDA stream has been synchronized (e.g., via cuCtxSynchronize() or
	// cuStreamSynchronize()).
	FeedDevices  map[string]string `protobuf:"bytes,6,rep,name=feed_devices,json=feedDevices,proto3" json:"feed_devices,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	FetchDevices map[string]string `protobuf:"bytes,7,rep,name=fetch_devices,json=fetchDevices,proto3" json:"fetch_devices,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// By default, RunCallable() will synchronize the GPU stream before returning
	// fetched tensors on a GPU device, to ensure that the values in those tensors
	// have been produced. This simplifies interacting with the tensors, but
	// potentially incurs a performance hit.
	//
	// If this options is set to true, the caller is responsible for ensuring
	// that the values in the fetched tensors have been produced before they are
	// used. The caller can do this by invoking `Device::Sync()` on the underlying
	// device(s), or by feeding the tensors back to the same Session using
	// `feed_devices` with the same corresponding device name.
	FetchSkipSync bool `protobuf:"varint,8,opt,name=fetch_skip_sync,json=fetchSkipSync,proto3" json:"fetch_skip_sync,omitempty"`
}

func (x *CallableOptions) Reset() {
	*x = CallableOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallableOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallableOptions) ProtoMessage() {}

func (x *CallableOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallableOptions.ProtoReflect.Descriptor instead.
func (*CallableOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{10}
}

func (x *CallableOptions) GetFeed() []string {
	if x != nil {
		return x.Feed
	}
	return nil
}

func (x *CallableOptions) GetFetch() []string {
	if x != nil {
		return x.Fetch
	}
	return nil
}

func (x *CallableOptions) GetTarget() []string {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *CallableOptions) GetRunOptions() *RunOptions {
	if x != nil {
		return x.RunOptions
	}
	return nil
}

func (x *CallableOptions) GetTensorConnection() []*TensorConnection {
	if x != nil {
		return x.TensorConnection
	}
	return nil
}

func (x *CallableOptions) GetFeedDevices() map[string]string {
	if x != nil {
		return x.FeedDevices
	}
	return nil
}

func (x *CallableOptions) GetFetchDevices() map[string]string {
	if x != nil {
		return x.FetchDevices
	}
	return nil
}

func (x *CallableOptions) GetFetchSkipSync() bool {
	if x != nil {
		return x.FetchSkipSync
	}
	return false
}

type GPUOptions_Experimental struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The multi virtual device settings. If empty (not set), it will create
	// single virtual device on each visible GPU, according to the settings
	// in "visible_device_list" above. Otherwise, the number of elements in the
	// list must be the same as the number of visible GPUs (after
	// "visible_device_list" filtering if it is set), and the string represented
	// device names (e.g. /device:GPU:<id>) will refer to the virtual
	// devices and have the <id> field assigned sequentially starting from 0,
	// according to the order they appear in this list and the "memory_limit"
	// list inside each element. For example,
	//
	//	visible_device_list = "1,0"
	//	virtual_devices { memory_limit: 1GB memory_limit: 2GB }
	//	virtual_devices {}
	//
	// will create three virtual devices as:
	//
	//	/device:GPU:0 -> visible GPU 1 with 1GB memory
	//	/device:GPU:1 -> visible GPU 1 with 2GB memory
	//	/device:GPU:2 -> visible GPU 0 with all available memory
	//
	// NOTE:
	//  1. It's invalid to set both this and "per_process_gpu_memory_fraction"
	//     at the same time.
	//  2. Currently this setting is per-process, not per-session. Using
	//     different settings in different sessions within same process will
	//     result in undefined behavior.
	VirtualDevices []*GPUOptions_Experimental_VirtualDevices `protobuf:"bytes,1,rep,name=virtual_devices,json=virtualDevices,proto3" json:"virtual_devices,omitempty"`
	// If true, uses CUDA unified memory for memory allocations. If
	// per_process_gpu_memory_fraction option is greater than 1.0, then unified
	// memory is used regardless of the value for this field. See comments for
	// per_process_gpu_memory_fraction field for more details and requirements
	// of the unified memory. This option is useful to oversubscribe memory if
	// multiple processes are sharing a single GPU while individually using less
	// than 1.0 per process memory fraction.
	UseUnifiedMemory bool `protobuf:"varint,2,opt,name=use_unified_memory,json=useUnifiedMemory,proto3" json:"use_unified_memory,omitempty"`
	// If > 1, the number of device-to-device copy streams to create
	// for each GPUDevice.  Default value is 0, which is automatically
	// converted to 1.
	NumDevToDevCopyStreams int32 `protobuf:"varint,3,opt,name=num_dev_to_dev_copy_streams,json=numDevToDevCopyStreams,proto3" json:"num_dev_to_dev_copy_streams,omitempty"`
	// If non-empty, defines a good GPU ring order on a single worker based on
	// device interconnect.  This assumes that all workers have the same GPU
	// topology.  Specify as a comma-separated string, e.g. "3,2,1,0,7,6,5,4".
	// This ring order is used by the RingReducer implementation of
	// CollectiveReduce, and serves as an override to automatic ring order
	// generation in OrderTaskDeviceMap() during CollectiveParam resolution.
	CollectiveRingOrder string `protobuf:"bytes,4,opt,name=collective_ring_order,json=collectiveRingOrder,proto3" json:"collective_ring_order,omitempty"`
	// If true then extra work is done by GPUDevice and GPUBFCAllocator to
	// keep track of when GPU memory is freed and when kernels actually
	// complete so that we can know when a nominally free memory chunk
	// is really not subject to pending use.
	TimestampedAllocator bool `protobuf:"varint,5,opt,name=timestamped_allocator,json=timestampedAllocator,proto3" json:"timestamped_allocator,omitempty"`
	// Parameters for GPUKernelTracker.  By default no kernel tracking is done.
	// Note that timestamped_allocator is only effective if some tracking is
	// specified.
	//
	// If kernel_tracker_max_interval = n > 0, then a tracking event
	// is inserted after every n kernels without an event.
	KernelTrackerMaxInterval int32 `protobuf:"varint,7,opt,name=kernel_tracker_max_interval,json=kernelTrackerMaxInterval,proto3" json:"kernel_tracker_max_interval,omitempty"`
	// If kernel_tracker_max_bytes = n > 0, then a tracking event is
	// inserted after every series of kernels allocating a sum of
	// memory >= n.  If one kernel allocates b * n bytes, then one
	// event will be inserted after it, but it will count as b against
	// the pending limit.
	KernelTrackerMaxBytes int32 `protobuf:"varint,8,opt,name=kernel_tracker_max_bytes,json=kernelTrackerMaxBytes,proto3" json:"kernel_tracker_max_bytes,omitempty"`
	// If kernel_tracker_max_pending > 0 then no more than this many
	// tracking events can be outstanding at a time.  An attempt to
	// launch an additional kernel will stall until an event
	// completes.
	KernelTrackerMaxPending int32 `protobuf:"varint,9,opt,name=kernel_tracker_max_pending,json=kernelTrackerMaxPending,proto3" json:"kernel_tracker_max_pending,omitempty"`
	// BFC Allocator can return an allocated chunk of memory upto 2x the
	// requested size. For virtual devices with tight memory constraints, and
	// proportionately large allocation requests, this can lead to a significant
	// reduction in available memory. The threshold below controls when a chunk
	// should be split if the chunk size exceeds requested memory size. It is
	// expressed as a fraction of total available memory for the tf device. For
	// example setting it to 0.05 would imply a chunk needs to be split if its
	// size exceeds the requested memory by 5% of the total virtual device/gpu
	// memory size.
	InternalFragmentationFraction float64 `protobuf:"fixed64,10,opt,name=internal_fragmentation_fraction,json=internalFragmentationFraction,proto3" json:"internal_fragmentation_fraction,omitempty"`
	// When true, use CUDA cudaMallocAsync API instead of TF gpu allocator.
	UseCudaMallocAsync bool `protobuf:"varint,11,opt,name=use_cuda_malloc_async,json=useCudaMallocAsync,proto3" json:"use_cuda_malloc_async,omitempty"`
}

func (x *GPUOptions_Experimental) Reset() {
	*x = GPUOptions_Experimental{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GPUOptions_Experimental) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GPUOptions_Experimental) ProtoMessage() {}

func (x *GPUOptions_Experimental) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GPUOptions_Experimental.ProtoReflect.Descriptor instead.
func (*GPUOptions_Experimental) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GPUOptions_Experimental) GetVirtualDevices() []*GPUOptions_Experimental_VirtualDevices {
	if x != nil {
		return x.VirtualDevices
	}
	return nil
}

func (x *GPUOptions_Experimental) GetUseUnifiedMemory() bool {
	if x != nil {
		return x.UseUnifiedMemory
	}
	return false
}

func (x *GPUOptions_Experimental) GetNumDevToDevCopyStreams() int32 {
	if x != nil {
		return x.NumDevToDevCopyStreams
	}
	return 0
}

func (x *GPUOptions_Experimental) GetCollectiveRingOrder() string {
	if x != nil {
		return x.CollectiveRingOrder
	}
	return ""
}

func (x *GPUOptions_Experimental) GetTimestampedAllocator() bool {
	if x != nil {
		return x.TimestampedAllocator
	}
	return false
}

func (x *GPUOptions_Experimental) GetKernelTrackerMaxInterval() int32 {
	if x != nil {
		return x.KernelTrackerMaxInterval
	}
	return 0
}

func (x *GPUOptions_Experimental) GetKernelTrackerMaxBytes() int32 {
	if x != nil {
		return x.KernelTrackerMaxBytes
	}
	return 0
}

func (x *GPUOptions_Experimental) GetKernelTrackerMaxPending() int32 {
	if x != nil {
		return x.KernelTrackerMaxPending
	}
	return 0
}

func (x *GPUOptions_Experimental) GetInternalFragmentationFraction() float64 {
	if x != nil {
		return x.InternalFragmentationFraction
	}
	return 0
}

func (x *GPUOptions_Experimental) GetUseCudaMallocAsync() bool {
	if x != nil {
		return x.UseCudaMallocAsync
	}
	return false
}

// Configuration for breaking down a visible GPU into multiple "virtual"
// devices.
type GPUOptions_Experimental_VirtualDevices struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Per "virtual" device memory limit, in MB. The number of elements in
	// the list is the number of virtual devices to create on the
	// corresponding visible GPU (see "virtual_devices" below).
	// If empty, it will create single virtual device taking all available
	// memory from the device.
	//
	// For the concept of "visible" and "virtual" GPU, see the comments for
	// "visible_device_list" above for more information.
	MemoryLimitMb []float32 `protobuf:"fixed32,1,rep,packed,name=memory_limit_mb,json=memoryLimitMb,proto3" json:"memory_limit_mb,omitempty"`
	// Priority values to use with the virtual devices. Use the cuda function
	// cudaDeviceGetStreamPriorityRange to query for valid range of values for
	// priority.
	//
	// On a P4000 GPU with cuda 10.1, the priority range reported was 0 for
	// least priority and -1 for greatest priority.
	//
	// If this field is not specified, then the virtual devices will be
	// created with the default. If this field has values set, then the size
	// of this must match with the above memory_limit_mb.
	Priority []int32 `protobuf:"varint,2,rep,packed,name=priority,proto3" json:"priority,omitempty"`
}

func (x *GPUOptions_Experimental_VirtualDevices) Reset() {
	*x = GPUOptions_Experimental_VirtualDevices{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GPUOptions_Experimental_VirtualDevices) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GPUOptions_Experimental_VirtualDevices) ProtoMessage() {}

func (x *GPUOptions_Experimental_VirtualDevices) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GPUOptions_Experimental_VirtualDevices.ProtoReflect.Descriptor instead.
func (*GPUOptions_Experimental_VirtualDevices) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *GPUOptions_Experimental_VirtualDevices) GetMemoryLimitMb() []float32 {
	if x != nil {
		return x.MemoryLimitMb
	}
	return nil
}

func (x *GPUOptions_Experimental_VirtualDevices) GetPriority() []int32 {
	if x != nil {
		return x.Priority
	}
	return nil
}

// Everything inside Experimental is subject to change and is not subject
// to API stability guarantees in
// https://www.tensorflow.org/guide/version_compat.
type ConfigProto_Experimental struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Task name for group resolution.
	CollectiveGroupLeader string `protobuf:"bytes,1,opt,name=collective_group_leader,json=collectiveGroupLeader,proto3" json:"collective_group_leader,omitempty"`
	// Which executor to use, the default executor will be used
	// if it is an empty string or "DEFAULT"
	ExecutorType string `protobuf:"bytes,3,opt,name=executor_type,json=executorType,proto3" json:"executor_type,omitempty"`
	// Guidance to formatting of large RecvBuf fields for transfer.
	// Any positive value sets the max chunk size.  0 defaults to 4096.
	// Any negative value indicates no max, i.e. one chunk only.
	RecvBufMaxChunk int32 `protobuf:"varint,4,opt,name=recv_buf_max_chunk,json=recvBufMaxChunk,proto3" json:"recv_buf_max_chunk,omitempty"`
	// If true, and supported by the platform, the runtime will attempt to
	// use NUMA affinity where applicable.  One consequence will be the
	// existence of as many CPU devices as there are available NUMA nodes.
	UseNumaAffinity bool `protobuf:"varint,5,opt,name=use_numa_affinity,json=useNumaAffinity,proto3" json:"use_numa_affinity,omitempty"`
	// If true, make collective op execution order sequential and deterministic
	// for potentially concurrent collective instances.
	CollectiveDeterministicSequentialExecution bool `protobuf:"varint,6,opt,name=collective_deterministic_sequential_execution,json=collectiveDeterministicSequentialExecution,proto3" json:"collective_deterministic_sequential_execution,omitempty"`
	// If true, use NCCL for CollectiveOps.  This feature is highly
	// experimental.
	CollectiveNccl bool `protobuf:"varint,7,opt,name=collective_nccl,json=collectiveNccl,proto3" json:"collective_nccl,omitempty"`
	// In the following, session state means the value of a variable, elements
	// in a hash table, or any other resource, accessible by worker sessions
	// held by a TF server.
	//
	// When ClusterSpec propagation is enabled, the value of
	// isolate_session_state is ignored when deciding whether to share session
	// states in a TF server (for backwards compatibility reasons).
	// - If share_session_state_in_clusterspec_propagation is true, the session
	// states are shared.
	// - If share_session_state_in_clusterspec_propagation is false, session
	// states are isolated.
	//
	// When clusterspec propagation is not used, the value of
	// share_session_state_in_clusterspec_propagation is ignored when deciding
	// whether to share session states in a TF server.
	// - If isolate_session_state is true, session states are isolated.
	// - If isolate_session_state is false, session states are shared.
	//
	// TODO(b/129330037): Add a single API that consistently treats
	// isolate_session_state and ClusterSpec propagation.
	ShareSessionStateInClusterspecPropagation bool `protobuf:"varint,8,opt,name=share_session_state_in_clusterspec_propagation,json=shareSessionStateInClusterspecPropagation,proto3" json:"share_session_state_in_clusterspec_propagation,omitempty"`
	// If using a direct session, disable spinning while waiting for work in
	// the thread pool. This may result in higher latency for completing ops,
	// but in the case where there is a lot of spinning may result in lower
	// CPU usage.
	DisableThreadSpinning bool `protobuf:"varint,9,opt,name=disable_thread_spinning,json=disableThreadSpinning,proto3" json:"disable_thread_spinning,omitempty"`
	// This was promoted to a non-experimental API. Please use
	// ConfigProto.share_cluster_devices_in_session instead.
	ShareClusterDevicesInSession bool `protobuf:"varint,10,opt,name=share_cluster_devices_in_session,json=shareClusterDevicesInSession,proto3" json:"share_cluster_devices_in_session,omitempty"`
	// Metadata about the session.
	//
	// If set, this can be used by the runtime and the Ops for debugging,
	// monitoring, etc.
	//
	// NOTE: This is currently used and propagated only by the direct session.
	SessionMetadata *SessionMetadata `protobuf:"bytes,11,opt,name=session_metadata,json=sessionMetadata,proto3" json:"session_metadata,omitempty"`
	// If true, the session may treat the graph as being static for optimization
	// purposes.
	//
	// If this option is set to true when a session is created, the full
	// GraphDef must be passed in a single call to Session::Create(), and
	// Session::Extend() may not be supported.
	OptimizeForStaticGraph bool `protobuf:"varint,12,opt,name=optimize_for_static_graph,json=optimizeForStaticGraph,proto3" json:"optimize_for_static_graph,omitempty"`
	// This field will eventually be deprecated and replaced by
	// mlir_bridge_rollout (b/166038521).
	//
	// Whether to enable the MLIR-based TF->XLA bridge.
	//
	// This is a replacement to the existing bridge, and not ready for
	// production usage yet.
	// If this option is set to true when a session is created, MLIR is used to
	// perform the set of graph transformations to put the graph in a form that
	// can be executed with delegation of some computations to an accelerator.
	// This builds on the model of XLA where a subset of the graph is
	// encapsulated and attached to a "compile" operation, whose result is fed
	// to an "execute" operation. The kernel for these operations is responsible
	// to lower the encapsulated graph to a particular device.
	EnableMlirBridge bool `protobuf:"varint,13,opt,name=enable_mlir_bridge,json=enableMlirBridge,proto3" json:"enable_mlir_bridge,omitempty"`
	// This field is underdevelopment, for now use enable_mlir_bridge
	// (b/166038521).
	//
	// Whether to enable the MLIR-based TF->XLA bridge.
	MlirBridgeRollout ConfigProto_Experimental_MlirBridgeRollout `protobuf:"varint,17,opt,name=mlir_bridge_rollout,json=mlirBridgeRollout,proto3,enum=tensorflow.ConfigProto_Experimental_MlirBridgeRollout" json:"mlir_bridge_rollout,omitempty"`
	// Whether to enable the MLIR-based Graph optimizations.
	//
	// This will become a part of standard Tensorflow graph optimization
	// pipeline, currently this is only used for gradual migration and testing
	// new passes that are replacing existing optimizations in Grappler.
	EnableMlirGraphOptimization bool `protobuf:"varint,16,opt,name=enable_mlir_graph_optimization,json=enableMlirGraphOptimization,proto3" json:"enable_mlir_graph_optimization,omitempty"`
	// If true, the session will not store an additional copy of the graph for
	// each subgraph.
	//
	// If this option is set to true when a session is created, the
	// `RunOptions.output_partition_graphs` options must not be set.
	DisableOutputPartitionGraphs bool `protobuf:"varint,14,opt,name=disable_output_partition_graphs,json=disableOutputPartitionGraphs,proto3" json:"disable_output_partition_graphs,omitempty"`
	// Minimum number of batches run through the XLA graph before XLA fusion
	// autotuner is enabled. Default value of zero disables the autotuner.
	//
	// The XLA fusion autotuner can improve performance by executing a heuristic
	// search on the compiler parameters.
	XlaFusionAutotunerThresh int64 `protobuf:"varint,15,opt,name=xla_fusion_autotuner_thresh,json=xlaFusionAutotunerThresh,proto3" json:"xla_fusion_autotuner_thresh,omitempty"`
	// Whether runtime execution uses TFRT.
	UseTfrt bool `protobuf:"varint,18,opt,name=use_tfrt,json=useTfrt,proto3" json:"use_tfrt,omitempty"`
	// Whether functional control flow op lowering should be disabled. This is
	// useful when executing within a portable runtime where control flow op
	// kernels may not be loaded due to selective registration.
	DisableFunctionalOpsLowering bool `protobuf:"varint,21,opt,name=disable_functional_ops_lowering,json=disableFunctionalOpsLowering,proto3" json:"disable_functional_ops_lowering,omitempty"`
	// Provides a hint to XLA auto clustering to prefer forming a single large
	// cluster that encompases most of the graph.
	XlaPreferSingleGraphCluster bool `protobuf:"varint,22,opt,name=xla_prefer_single_graph_cluster,json=xlaPreferSingleGraphCluster,proto3" json:"xla_prefer_single_graph_cluster,omitempty"`
	// Distributed coordination service configurations.
	CoordinationConfig *CoordinationServiceConfig `protobuf:"bytes,23,opt,name=coordination_config,json=coordinationConfig,proto3" json:"coordination_config,omitempty"`
}

func (x *ConfigProto_Experimental) Reset() {
	*x = ConfigProto_Experimental{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigProto_Experimental) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigProto_Experimental) ProtoMessage() {}

func (x *ConfigProto_Experimental) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigProto_Experimental.ProtoReflect.Descriptor instead.
func (*ConfigProto_Experimental) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{6, 1}
}

func (x *ConfigProto_Experimental) GetCollectiveGroupLeader() string {
	if x != nil {
		return x.CollectiveGroupLeader
	}
	return ""
}

func (x *ConfigProto_Experimental) GetExecutorType() string {
	if x != nil {
		return x.ExecutorType
	}
	return ""
}

func (x *ConfigProto_Experimental) GetRecvBufMaxChunk() int32 {
	if x != nil {
		return x.RecvBufMaxChunk
	}
	return 0
}

func (x *ConfigProto_Experimental) GetUseNumaAffinity() bool {
	if x != nil {
		return x.UseNumaAffinity
	}
	return false
}

func (x *ConfigProto_Experimental) GetCollectiveDeterministicSequentialExecution() bool {
	if x != nil {
		return x.CollectiveDeterministicSequentialExecution
	}
	return false
}

func (x *ConfigProto_Experimental) GetCollectiveNccl() bool {
	if x != nil {
		return x.CollectiveNccl
	}
	return false
}

func (x *ConfigProto_Experimental) GetShareSessionStateInClusterspecPropagation() bool {
	if x != nil {
		return x.ShareSessionStateInClusterspecPropagation
	}
	return false
}

func (x *ConfigProto_Experimental) GetDisableThreadSpinning() bool {
	if x != nil {
		return x.DisableThreadSpinning
	}
	return false
}

func (x *ConfigProto_Experimental) GetShareClusterDevicesInSession() bool {
	if x != nil {
		return x.ShareClusterDevicesInSession
	}
	return false
}

func (x *ConfigProto_Experimental) GetSessionMetadata() *SessionMetadata {
	if x != nil {
		return x.SessionMetadata
	}
	return nil
}

func (x *ConfigProto_Experimental) GetOptimizeForStaticGraph() bool {
	if x != nil {
		return x.OptimizeForStaticGraph
	}
	return false
}

func (x *ConfigProto_Experimental) GetEnableMlirBridge() bool {
	if x != nil {
		return x.EnableMlirBridge
	}
	return false
}

func (x *ConfigProto_Experimental) GetMlirBridgeRollout() ConfigProto_Experimental_MlirBridgeRollout {
	if x != nil {
		return x.MlirBridgeRollout
	}
	return ConfigProto_Experimental_MLIR_BRIDGE_ROLLOUT_UNSPECIFIED
}

func (x *ConfigProto_Experimental) GetEnableMlirGraphOptimization() bool {
	if x != nil {
		return x.EnableMlirGraphOptimization
	}
	return false
}

func (x *ConfigProto_Experimental) GetDisableOutputPartitionGraphs() bool {
	if x != nil {
		return x.DisableOutputPartitionGraphs
	}
	return false
}

func (x *ConfigProto_Experimental) GetXlaFusionAutotunerThresh() int64 {
	if x != nil {
		return x.XlaFusionAutotunerThresh
	}
	return 0
}

func (x *ConfigProto_Experimental) GetUseTfrt() bool {
	if x != nil {
		return x.UseTfrt
	}
	return false
}

func (x *ConfigProto_Experimental) GetDisableFunctionalOpsLowering() bool {
	if x != nil {
		return x.DisableFunctionalOpsLowering
	}
	return false
}

func (x *ConfigProto_Experimental) GetXlaPreferSingleGraphCluster() bool {
	if x != nil {
		return x.XlaPreferSingleGraphCluster
	}
	return false
}

func (x *ConfigProto_Experimental) GetCoordinationConfig() *CoordinationServiceConfig {
	if x != nil {
		return x.CoordinationConfig
	}
	return nil
}

// Everything inside Experimental is subject to change and is not subject
// to API stability guarantees in
// https://www.tensorflow.org/guide/version_compat.
type RunOptions_Experimental struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// If non-zero, declares that this graph is going to use collective
	// ops and must synchronize step_ids with any other graph with this
	// same group_key value (in a distributed computation where tasks
	// run disjoint graphs).
	CollectiveGraphKey int64 `protobuf:"varint,1,opt,name=collective_graph_key,json=collectiveGraphKey,proto3" json:"collective_graph_key,omitempty"`
	// If true, then operations (using the inter-op pool) across all
	// session::run() calls will be centrally scheduled, optimizing for (median
	// and tail) latency.
	// Consider using this option for CPU-bound workloads like inference.
	UseRunHandlerPool     bool                                           `protobuf:"varint,2,opt,name=use_run_handler_pool,json=useRunHandlerPool,proto3" json:"use_run_handler_pool,omitempty"`
	RunHandlerPoolOptions *RunOptions_Experimental_RunHandlerPoolOptions `protobuf:"bytes,3,opt,name=run_handler_pool_options,json=runHandlerPoolOptions,proto3" json:"run_handler_pool_options,omitempty"`
}

func (x *RunOptions_Experimental) Reset() {
	*x = RunOptions_Experimental{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunOptions_Experimental) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunOptions_Experimental) ProtoMessage() {}

func (x *RunOptions_Experimental) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunOptions_Experimental.ProtoReflect.Descriptor instead.
func (*RunOptions_Experimental) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{7, 0}
}

func (x *RunOptions_Experimental) GetCollectiveGraphKey() int64 {
	if x != nil {
		return x.CollectiveGraphKey
	}
	return 0
}

func (x *RunOptions_Experimental) GetUseRunHandlerPool() bool {
	if x != nil {
		return x.UseRunHandlerPool
	}
	return false
}

func (x *RunOptions_Experimental) GetRunHandlerPoolOptions() *RunOptions_Experimental_RunHandlerPoolOptions {
	if x != nil {
		return x.RunHandlerPoolOptions
	}
	return nil
}

// Options for run handler thread pool.
type RunOptions_Experimental_RunHandlerPoolOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Priority of the request. The run handler thread pool will schedule ops
	// based on the priority number. The larger number means higher priority.
	Priority int64 `protobuf:"varint,1,opt,name=priority,proto3" json:"priority,omitempty"`
}

func (x *RunOptions_Experimental_RunHandlerPoolOptions) Reset() {
	*x = RunOptions_Experimental_RunHandlerPoolOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunOptions_Experimental_RunHandlerPoolOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunOptions_Experimental_RunHandlerPoolOptions) ProtoMessage() {}

func (x *RunOptions_Experimental_RunHandlerPoolOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunOptions_Experimental_RunHandlerPoolOptions.ProtoReflect.Descriptor instead.
func (*RunOptions_Experimental_RunHandlerPoolOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{7, 0, 0}
}

func (x *RunOptions_Experimental_RunHandlerPoolOptions) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

type RunMetadata_FunctionGraphs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// TODO(nareshmodi): Include some sort of function/cache-key identifier?
	PartitionGraphs       []*graph_go_proto.GraphDef `protobuf:"bytes,1,rep,name=partition_graphs,json=partitionGraphs,proto3" json:"partition_graphs,omitempty"`
	PreOptimizationGraph  *graph_go_proto.GraphDef   `protobuf:"bytes,2,opt,name=pre_optimization_graph,json=preOptimizationGraph,proto3" json:"pre_optimization_graph,omitempty"`
	PostOptimizationGraph *graph_go_proto.GraphDef   `protobuf:"bytes,3,opt,name=post_optimization_graph,json=postOptimizationGraph,proto3" json:"post_optimization_graph,omitempty"`
}

func (x *RunMetadata_FunctionGraphs) Reset() {
	*x = RunMetadata_FunctionGraphs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunMetadata_FunctionGraphs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunMetadata_FunctionGraphs) ProtoMessage() {}

func (x *RunMetadata_FunctionGraphs) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_config_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunMetadata_FunctionGraphs.ProtoReflect.Descriptor instead.
func (*RunMetadata_FunctionGraphs) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_config_proto_rawDescGZIP(), []int{8, 0}
}

func (x *RunMetadata_FunctionGraphs) GetPartitionGraphs() []*graph_go_proto.GraphDef {
	if x != nil {
		return x.PartitionGraphs
	}
	return nil
}

func (x *RunMetadata_FunctionGraphs) GetPreOptimizationGraph() *graph_go_proto.GraphDef {
	if x != nil {
		return x.PreOptimizationGraph
	}
	return nil
}

func (x *RunMetadata_FunctionGraphs) GetPostOptimizationGraph() *graph_go_proto.GraphDef {
	if x != nil {
		return x.PostOptimizationGraph
	}
	return nil
}

var File_tensorflow_core_protobuf_config_proto protoreflect.FileDescriptor

var file_tensorflow_core_protobuf_config_proto_rawDesc = []byte{
	0x0a, 0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x1a, 0x2a, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f,
	0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x63,
	0x6f, 0x73, 0x74, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x67, 0x72, 0x61, 0x70, 0x68,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72,
	0x6b, 0x2f, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x26, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63,
	0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x62, 0x75, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x72,
	0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc5, 0x09, 0x0a, 0x0a, 0x47, 0x50, 0x55, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x44, 0x0a, 0x1f, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x67, 0x70, 0x75, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x66, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x1b, 0x70, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x47, 0x70, 0x75, 0x4d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x46, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x64, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x64, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x76,
	0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c,
	0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x1a, 0x70,
	0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x65,
	0x6c, 0x61, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x63, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x17, 0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x65,
	0x6c, 0x61, 0x79, 0x55, 0x73, 0x65, 0x63, 0x73, 0x12, 0x3f, 0x0a, 0x1c, 0x70, 0x6f, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x65, 0x6c,
	0x61, 0x79, 0x5f, 0x6d, 0x73, 0x65, 0x63, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19,
	0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44,
	0x65, 0x6c, 0x61, 0x79, 0x4d, 0x73, 0x65, 0x63, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x66, 0x6f, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x70, 0x75, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x74, 0x69, 0x62, 0x6c,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x47, 0x70,
	0x75, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x47, 0x0a, 0x0c, 0x65,
	0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x47,
	0x50, 0x55, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69,
	0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x52, 0x0c, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65,
	0x6e, 0x74, 0x61, 0x6c, 0x1a, 0xc5, 0x05, 0x0a, 0x0c, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d,
	0x65, 0x6e, 0x74, 0x61, 0x6c, 0x12, 0x5b, 0x0a, 0x0f, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c,
	0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x47, 0x50, 0x55, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e,
	0x74, 0x61, 0x6c, 0x2e, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x52, 0x0e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x75, 0x73, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10,
	0x75, 0x73, 0x65, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x12, 0x3b, 0x0a, 0x1b, 0x6e, 0x75, 0x6d, 0x5f, 0x64, 0x65, 0x76, 0x5f, 0x74, 0x6f, 0x5f, 0x64,
	0x65, 0x76, 0x5f, 0x63, 0x6f, 0x70, 0x79, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x6e, 0x75, 0x6d, 0x44, 0x65, 0x76, 0x54, 0x6f, 0x44,
	0x65, 0x76, 0x43, 0x6f, 0x70, 0x79, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x32, 0x0a,
	0x15, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x69, 0x6e, 0x67, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x33, 0x0a, 0x15, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x65, 0x64,
	0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x14, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x65, 0x64, 0x41, 0x6c, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x3d, 0x0a, 0x1b, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x18, 0x6b, 0x65, 0x72,
	0x6e, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x4d, 0x61, 0x78, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x37, 0x0a, 0x18, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x62, 0x79, 0x74, 0x65,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x4d, 0x61, 0x78, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x3b,
	0x0a, 0x1a, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72,
	0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x17, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x4d, 0x61, 0x78, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x46, 0x0a, 0x1f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x1d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x46, 0x72,
	0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x15, 0x75, 0x73, 0x65, 0x5f, 0x63, 0x75, 0x64, 0x61, 0x5f,
	0x6d, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x5f, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x12, 0x75, 0x73, 0x65, 0x43, 0x75, 0x64, 0x61, 0x4d, 0x61, 0x6c, 0x6c, 0x6f,
	0x63, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x1a, 0x54, 0x0a, 0x0e, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61,
	0x6c, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x6d, 0x62, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x02, 0x52, 0x0d, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x4d, 0x62,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x22, 0xa8, 0x04, 0x0a,
	0x10, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x4d, 0x0a, 0x23, 0x64, 0x6f, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x73,
	0x75, 0x62, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6c, 0x69,
	0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x20,
	0x64, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x75, 0x62, 0x65, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x2e, 0x0a, 0x13, 0x64, 0x6f, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f,
	0x66, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x64,
	0x6f, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x46, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x3e, 0x0a, 0x1c, 0x6d, 0x61, 0x78, 0x5f, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x64, 0x5f, 0x63,
	0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x18, 0x6d, 0x61, 0x78, 0x46, 0x6f, 0x6c, 0x64, 0x65,
	0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x42, 0x79, 0x74, 0x65, 0x73,
	0x12, 0x30, 0x0a, 0x14, 0x64, 0x6f, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x6e, 0x6c, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12,
	0x64, 0x6f, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x6c, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x12, 0x3f, 0x0a, 0x09, 0x6f, 0x70, 0x74, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x12, 0x55, 0x0a, 0x10, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x6a, 0x69,
	0x74, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6d,
	0x69, 0x7a, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x47, 0x6c, 0x6f, 0x62,
	0x61, 0x6c, 0x4a, 0x69, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x0e, 0x67, 0x6c, 0x6f, 0x62,
	0x61, 0x6c, 0x4a, 0x69, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x70,
	0x75, 0x5f, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x6a, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x63, 0x70, 0x75, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x4a, 0x69, 0x74,
	0x22, 0x20, 0x0a, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x06, 0x0a, 0x02, 0x4c, 0x31, 0x10,
	0x00, 0x12, 0x0f, 0x0a, 0x02, 0x4c, 0x30, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0x01, 0x22, 0x43, 0x0a, 0x0e, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x4a, 0x69, 0x74, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10,
	0x00, 0x12, 0x10, 0x0a, 0x03, 0x4f, 0x46, 0x46, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x4f, 0x4e, 0x5f, 0x31, 0x10, 0x01, 0x12, 0x08, 0x0a,
	0x04, 0x4f, 0x4e, 0x5f, 0x32, 0x10, 0x02, 0x22, 0x90, 0x04, 0x0a, 0x0c, 0x47, 0x72, 0x61, 0x70,
	0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x76, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69,
	0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x65, 0x63, 0x76, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x49,
	0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a,
	0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x75, 0x69,
	0x6c, 0x64, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0e, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x43, 0x6f, 0x73, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x33, 0x0a, 0x16, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x63, 0x6f, 0x73,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x13, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x43, 0x6f, 0x73, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x41, 0x66, 0x74, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x66, 0x65,
	0x72, 0x5f, 0x73, 0x68, 0x61, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x69, 0x6e, 0x66, 0x65, 0x72, 0x53, 0x68, 0x61, 0x70, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x70,
	0x6c, 0x61, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x75, 0x6e, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x61, 0x70,
	0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x50, 0x72,
	0x75, 0x6e, 0x65, 0x64, 0x47, 0x72, 0x61, 0x70, 0x68, 0x12, 0x38, 0x0a, 0x18, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x62, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x31, 0x36, 0x5f, 0x73, 0x65, 0x6e,
	0x64, 0x72, 0x65, 0x63, 0x76, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x42, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x31, 0x36, 0x53, 0x65, 0x6e, 0x64, 0x72,
	0x65, 0x63, 0x76, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x73, 0x74, 0x65, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x65, 0x70, 0x12, 0x43, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x72,
	0x69, 0x74, 0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52,
	0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x72,
	0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4a, 0x04, 0x08,
	0x01, 0x10, 0x02, 0x52, 0x25, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x5f, 0x73, 0x75, 0x62, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65,
	0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x59, 0x0a, 0x15, 0x54, 0x68,
	0x72, 0x65, 0x61, 0x64, 0x50, 0x6f, 0x6f, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x75, 0x6d, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x61,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x54, 0x68, 0x72,
	0x65, 0x61, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x6c, 0x6f, 0x62, 0x61,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xe0, 0x02, 0x0a, 0x0a, 0x52, 0x50, 0x43, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3e, 0x0a, 0x1c, 0x75, 0x73, 0x65, 0x5f, 0x72, 0x70, 0x63, 0x5f,
	0x66, 0x6f, 0x72, 0x5f, 0x69, 0x6e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6d, 0x61,
	0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x18, 0x75, 0x73, 0x65, 0x52,
	0x70, 0x63, 0x46, 0x6f, 0x72, 0x49, 0x6e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x61,
	0x73, 0x74, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x12, 0x2b, 0x0a, 0x11, 0x63, 0x6f, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5f,
	0x72, 0x70, 0x63, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x10, 0x63, 0x61, 0x63, 0x68, 0x65, 0x52, 0x70, 0x63, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x22, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x1f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x68, 0x61, 0x72, 0x69, 0x6e,
	0x67, 0x12, 0x35, 0x0a, 0x17, 0x6e, 0x75, 0x6d, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x14, 0x6e, 0x75, 0x6d, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x50,
	0x65, 0x72, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x22, 0x3f, 0x0a, 0x0f, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xdc, 0x14, 0x0a, 0x0b, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x4b, 0x0a, 0x0c, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x1c, 0x69, 0x6e, 0x74, 0x72, 0x61, 0x5f,
	0x6f, 0x70, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x6d, 0x5f, 0x74,
	0x68, 0x72, 0x65, 0x61, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x69, 0x6e,
	0x74, 0x72, 0x61, 0x4f, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x6d,
	0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x1c, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x5f, 0x6f, 0x70, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x6d, 0x5f,
	0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x69, 0x73,
	0x6d, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x73, 0x12, 0x35, 0x0a, 0x17, 0x75, 0x73, 0x65, 0x5f,
	0x70, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x68, 0x72, 0x65,
	0x61, 0x64, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x75, 0x73, 0x65, 0x50, 0x65,
	0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x73, 0x12,
	0x61, 0x0a, 0x1c, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x5f, 0x6f, 0x70, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x18,
	0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x50, 0x6f, 0x6f, 0x6c, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x18, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x50, 0x6f,
	0x6f, 0x6c, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x70, 0x6c,
	0x61, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x25, 0x0a,
	0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x37, 0x0a, 0x0b, 0x67, 0x70, 0x75, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x47, 0x50, 0x55, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x0a, 0x67, 0x70, 0x75, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x30, 0x0a,
	0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x6f, 0x66, 0x74, 0x5f, 0x70, 0x6c, 0x61, 0x63,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x53, 0x6f, 0x66, 0x74, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x30, 0x0a, 0x14, 0x6c, 0x6f, 0x67, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x6c,
	0x61, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x6c,
	0x6f, 0x67, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x3d, 0x0a, 0x0d, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x0c, 0x67, 0x72, 0x61, 0x70, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x35, 0x0a, 0x17, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x14, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x49, 0x6e, 0x4d, 0x73, 0x12, 0x37, 0x0a, 0x0b, 0x72, 0x70, 0x63, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x50, 0x43, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0a, 0x72, 0x70, 0x63, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x37, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x66, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x66, 0x12, 0x32, 0x0a, 0x15, 0x69, 0x73, 0x6f,
	0x6c, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69, 0x73, 0x6f, 0x6c, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x46, 0x0a,
	0x20, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1c, 0x73, 0x68, 0x61, 0x72, 0x65, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x49, 0x6e, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d,
	0x65, 0x6e, 0x74, 0x61, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61,
	0x6c, 0x52, 0x0c, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x1a,
	0x3e, 0x0a, 0x10, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x86, 0x0c, 0x0a, 0x0c, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c,
	0x12, 0x36, 0x0a, 0x17, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x15, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a,
	0x12, 0x72, 0x65, 0x63, 0x76, 0x5f, 0x62, 0x75, 0x66, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x72, 0x65, 0x63, 0x76, 0x42,
	0x75, 0x66, 0x4d, 0x61, 0x78, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x12, 0x2a, 0x0a, 0x11, 0x75, 0x73,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x61, 0x5f, 0x61, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x61, 0x41, 0x66,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x12, 0x61, 0x0a, 0x2d, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x5f, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x2a, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x65, 0x74, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x69, 0x73, 0x74, 0x69, 0x63, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6e, 0x63, 0x63, 0x6c, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0e, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4e, 0x63,
	0x63, 0x6c, 0x12, 0x61, 0x0a, 0x2e, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x61, 0x67, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x29, 0x73, 0x68, 0x61, 0x72,
	0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x43,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x50, 0x72, 0x6f, 0x70, 0x61, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x17, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x73, 0x70, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x54,
	0x68, 0x72, 0x65, 0x61, 0x64, 0x53, 0x70, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x46, 0x0a,
	0x20, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1c, 0x73, 0x68, 0x61, 0x72, 0x65, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x49, 0x6e, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x10, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0f, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a,
	0x19, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x69, 0x63, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x16, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x46, 0x6f, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x63, 0x47, 0x72, 0x61, 0x70, 0x68, 0x12, 0x2c, 0x0a, 0x12, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x6d, 0x6c, 0x69, 0x72, 0x5f, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x6c, 0x69, 0x72,
	0x42, 0x72, 0x69, 0x64, 0x67, 0x65, 0x12, 0x66, 0x0a, 0x13, 0x6d, 0x6c, 0x69, 0x72, 0x5f, 0x62,
	0x72, 0x69, 0x64, 0x67, 0x65, 0x5f, 0x72, 0x6f, 0x6c, 0x6c, 0x6f, 0x75, 0x74, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x78, 0x70,
	0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x2e, 0x4d, 0x6c, 0x69, 0x72, 0x42, 0x72,
	0x69, 0x64, 0x67, 0x65, 0x52, 0x6f, 0x6c, 0x6c, 0x6f, 0x75, 0x74, 0x52, 0x11, 0x6d, 0x6c, 0x69,
	0x72, 0x42, 0x72, 0x69, 0x64, 0x67, 0x65, 0x52, 0x6f, 0x6c, 0x6c, 0x6f, 0x75, 0x74, 0x12, 0x43,
	0x0a, 0x1e, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6d, 0x6c, 0x69, 0x72, 0x5f, 0x67, 0x72,
	0x61, 0x70, 0x68, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x6c,
	0x69, 0x72, 0x47, 0x72, 0x61, 0x70, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x1f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x67, 0x72, 0x61, 0x70, 0x68, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1c, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x73, 0x12, 0x3d, 0x0a, 0x1b, 0x78, 0x6c,
	0x61, 0x5f, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x74, 0x75, 0x6e,
	0x65, 0x72, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x18, 0x78, 0x6c, 0x61, 0x46, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x75, 0x74, 0x6f, 0x74, 0x75,
	0x6e, 0x65, 0x72, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x73, 0x65,
	0x5f, 0x74, 0x66, 0x72, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x75, 0x73, 0x65,
	0x54, 0x66, 0x72, 0x74, 0x12, 0x45, 0x0a, 0x1f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x73, 0x5f, 0x6c,
	0x6f, 0x77, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1c, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x4f, 0x70, 0x73, 0x4c, 0x6f, 0x77, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x44, 0x0a, 0x1f, 0x78,
	0x6c, 0x61, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x5f, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65,
	0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x78, 0x6c, 0x61, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x53,
	0x69, 0x6e, 0x67, 0x6c, 0x65, 0x47, 0x72, 0x61, 0x70, 0x68, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x12, 0x56, 0x0a, 0x13, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6f, 0x72,
	0x64, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x12, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xda, 0x01, 0x0a, 0x11, 0x4d, 0x6c,
	0x69, 0x72, 0x42, 0x72, 0x69, 0x64, 0x67, 0x65, 0x52, 0x6f, 0x6c, 0x6c, 0x6f, 0x75, 0x74, 0x12,
	0x23, 0x0a, 0x1f, 0x4d, 0x4c, 0x49, 0x52, 0x5f, 0x42, 0x52, 0x49, 0x44, 0x47, 0x45, 0x5f, 0x52,
	0x4f, 0x4c, 0x4c, 0x4f, 0x55, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x4d, 0x4c, 0x49, 0x52, 0x5f, 0x42, 0x52, 0x49,
	0x44, 0x47, 0x45, 0x5f, 0x52, 0x4f, 0x4c, 0x4c, 0x4f, 0x55, 0x54, 0x5f, 0x45, 0x4e, 0x41, 0x42,
	0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x4d, 0x4c, 0x49, 0x52, 0x5f, 0x42, 0x52,
	0x49, 0x44, 0x47, 0x45, 0x5f, 0x52, 0x4f, 0x4c, 0x4c, 0x4f, 0x55, 0x54, 0x5f, 0x44, 0x49, 0x53,
	0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x29, 0x0a, 0x25, 0x4d, 0x4c, 0x49, 0x52, 0x5f,
	0x42, 0x52, 0x49, 0x44, 0x47, 0x45, 0x5f, 0x52, 0x4f, 0x4c, 0x4c, 0x4f, 0x55, 0x54, 0x5f, 0x53,
	0x41, 0x46, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44,
	0x10, 0x03, 0x12, 0x32, 0x0a, 0x2e, 0x4d, 0x4c, 0x49, 0x52, 0x5f, 0x42, 0x52, 0x49, 0x44, 0x47,
	0x45, 0x5f, 0x52, 0x4f, 0x4c, 0x4c, 0x4f, 0x55, 0x54, 0x5f, 0x53, 0x41, 0x46, 0x45, 0x5f, 0x4d,
	0x4f, 0x44, 0x45, 0x5f, 0x46, 0x41, 0x4c, 0x4c, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x45, 0x4e, 0x41,
	0x42, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x4a, 0x04, 0x08, 0x13,
	0x10, 0x14, 0x4a, 0x04, 0x08, 0x14, 0x10, 0x15, 0x22, 0xa8, 0x06, 0x0a, 0x0a, 0x52, 0x75, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x42, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x75, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52,
	0x0a, 0x74, 0x72, 0x61, 0x63, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x22, 0x0a, 0x0d, 0x74,
	0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x49, 0x6e, 0x4d, 0x73, 0x12,
	0x2f, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x6f, 0x70, 0x5f, 0x74, 0x68, 0x72, 0x65,
	0x61, 0x64, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x50, 0x6f, 0x6f, 0x6c,
	0x12, 0x36, 0x0a, 0x17, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x15, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x64, 0x65, 0x62, 0x75,
	0x67, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x62,
	0x75, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0c, 0x64, 0x65, 0x62, 0x75, 0x67,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4a, 0x0a, 0x22, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x6f, 0x6f, 0x6d, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x1e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x55, 0x70, 0x6f, 0x6e,
	0x4f, 0x6f, 0x6d, 0x12, 0x47, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e,
	0x74, 0x61, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x75, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x52, 0x0c,
	0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x1a, 0x9a, 0x02, 0x0a,
	0x0c, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x12, 0x30, 0x0a,
	0x14, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x72, 0x61, 0x70,
	0x68, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x61, 0x70, 0x68, 0x4b, 0x65, 0x79, 0x12,
	0x2f, 0x0a, 0x14, 0x75, 0x73, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x72, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x75,
	0x73, 0x65, 0x52, 0x75, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x50, 0x6f, 0x6f, 0x6c,
	0x12, 0x72, 0x0a, 0x18, 0x72, 0x75, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x5f,
	0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x39, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x52, 0x75, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x72,
	0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x2e, 0x52, 0x75, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x72, 0x50, 0x6f, 0x6f, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x15, 0x72,
	0x75, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x50, 0x6f, 0x6f, 0x6c, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x33, 0x0a, 0x15, 0x52, 0x75, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x72, 0x50, 0x6f, 0x6f, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x22, 0x52, 0x0a, 0x0a, 0x54, 0x72, 0x61,
	0x63, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x4f, 0x5f, 0x54, 0x52,
	0x41, 0x43, 0x45, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x4f, 0x46, 0x54, 0x57, 0x41, 0x52,
	0x45, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x45, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x48, 0x41, 0x52,
	0x44, 0x57, 0x41, 0x52, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x45, 0x10, 0x02, 0x12, 0x0e, 0x0a,
	0x0a, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x45, 0x10, 0x03, 0x4a, 0x04, 0x08,
	0x04, 0x10, 0x05, 0x22, 0xfc, 0x03, 0x0a, 0x0b, 0x52, 0x75, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x34, 0x0a, 0x0a, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x09,
	0x73, 0x74, 0x65, 0x70, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x0a, 0x63, 0x6f, 0x73,
	0x74, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x44, 0x65, 0x66, 0x52, 0x09, 0x63, 0x6f, 0x73, 0x74, 0x47, 0x72, 0x61,
	0x70, 0x68, 0x12, 0x3f, 0x0a, 0x10, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x67, 0x72, 0x61, 0x70, 0x68, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x44,
	0x65, 0x66, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x61,
	0x70, 0x68, 0x73, 0x12, 0x4f, 0x0a, 0x0f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x67, 0x72, 0x61, 0x70, 0x68, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x75, 0x6e, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72,
	0x61, 0x70, 0x68, 0x73, 0x52, 0x0e, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72,
	0x61, 0x70, 0x68, 0x73, 0x1a, 0xeb, 0x01, 0x0a, 0x0e, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x73, 0x12, 0x3f, 0x0a, 0x10, 0x70, 0x61, 0x72, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x44, 0x65, 0x66, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x73, 0x12, 0x4a, 0x0a, 0x16, 0x70, 0x72, 0x65, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x61,
	0x70, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x44, 0x65, 0x66, 0x52, 0x14,
	0x70, 0x72, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x12, 0x4c, 0x0a, 0x17, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x44, 0x65, 0x66, 0x52, 0x15, 0x70, 0x6f, 0x73,
	0x74, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x61,
	0x70, 0x68, 0x22, 0x50, 0x0a, 0x10, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x72, 0x6f,
	0x6d, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x6f, 0x5f, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x6f, 0x54, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x22, 0xa5, 0x04, 0x0a, 0x0f, 0x43, 0x61, 0x6c, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x65, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x66, 0x65, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x37, 0x0a, 0x0b, 0x72, 0x75,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x75, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0a, 0x72, 0x75, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x49, 0x0a, 0x11, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4f,
	0x0a, 0x0c, 0x66, 0x65, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0b, 0x66, 0x65, 0x65, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12,
	0x52, 0x0a, 0x0d, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x66, 0x65, 0x74, 0x63, 0x68, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x6b, 0x69,
	0x70, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x53, 0x6b, 0x69, 0x70, 0x53, 0x79, 0x6e, 0x63, 0x1a, 0x3e, 0x0a, 0x10, 0x46,
	0x65, 0x65, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3f, 0x0a, 0x11, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x84, 0x01, 0x0a,
	0x18, 0x6f, 0x72, 0x67, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x42, 0x0c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x50, 0x01, 0x5a, 0x55, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x67, 0x6f, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x5f, 0x67, 0x6f, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0xf8, 0x01, 0x01, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tensorflow_core_protobuf_config_proto_rawDescOnce sync.Once
	file_tensorflow_core_protobuf_config_proto_rawDescData = file_tensorflow_core_protobuf_config_proto_rawDesc
)

func file_tensorflow_core_protobuf_config_proto_rawDescGZIP() []byte {
	file_tensorflow_core_protobuf_config_proto_rawDescOnce.Do(func() {
		file_tensorflow_core_protobuf_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_tensorflow_core_protobuf_config_proto_rawDescData)
	})
	return file_tensorflow_core_protobuf_config_proto_rawDescData
}

var file_tensorflow_core_protobuf_config_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_tensorflow_core_protobuf_config_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_tensorflow_core_protobuf_config_proto_goTypes = []interface{}{
	(OptimizerOptions_Level)(0),                     // 0: tensorflow.OptimizerOptions.Level
	(OptimizerOptions_GlobalJitLevel)(0),            // 1: tensorflow.OptimizerOptions.GlobalJitLevel
	(ConfigProto_Experimental_MlirBridgeRollout)(0), // 2: tensorflow.ConfigProto.Experimental.MlirBridgeRollout
	(RunOptions_TraceLevel)(0),                      // 3: tensorflow.RunOptions.TraceLevel
	(*GPUOptions)(nil),                              // 4: tensorflow.GPUOptions
	(*OptimizerOptions)(nil),                        // 5: tensorflow.OptimizerOptions
	(*GraphOptions)(nil),                            // 6: tensorflow.GraphOptions
	(*ThreadPoolOptionProto)(nil),                   // 7: tensorflow.ThreadPoolOptionProto
	(*RPCOptions)(nil),                              // 8: tensorflow.RPCOptions
	(*SessionMetadata)(nil),                         // 9: tensorflow.SessionMetadata
	(*ConfigProto)(nil),                             // 10: tensorflow.ConfigProto
	(*RunOptions)(nil),                              // 11: tensorflow.RunOptions
	(*RunMetadata)(nil),                             // 12: tensorflow.RunMetadata
	(*TensorConnection)(nil),                        // 13: tensorflow.TensorConnection
	(*CallableOptions)(nil),                         // 14: tensorflow.CallableOptions
	(*GPUOptions_Experimental)(nil),                 // 15: tensorflow.GPUOptions.Experimental
	(*GPUOptions_Experimental_VirtualDevices)(nil),  // 16: tensorflow.GPUOptions.Experimental.VirtualDevices
	nil,                              // 17: tensorflow.ConfigProto.DeviceCountEntry
	(*ConfigProto_Experimental)(nil), // 18: tensorflow.ConfigProto.Experimental
	(*RunOptions_Experimental)(nil),  // 19: tensorflow.RunOptions.Experimental
	(*RunOptions_Experimental_RunHandlerPoolOptions)(nil), // 20: tensorflow.RunOptions.Experimental.RunHandlerPoolOptions
	(*RunMetadata_FunctionGraphs)(nil),                    // 21: tensorflow.RunMetadata.FunctionGraphs
	nil,                                                   // 22: tensorflow.CallableOptions.FeedDevicesEntry
	nil,                                                   // 23: tensorflow.CallableOptions.FetchDevicesEntry
	(*RewriterConfig)(nil),                                // 24: tensorflow.RewriterConfig
	(*ClusterDef)(nil),                                    // 25: tensorflow.ClusterDef
	(*DebugOptions)(nil),                                  // 26: tensorflow.DebugOptions
	(*step_stats_go_proto.StepStats)(nil),                 // 27: tensorflow.StepStats
	(*cost_graph_go_proto.CostGraphDef)(nil),              // 28: tensorflow.CostGraphDef
	(*graph_go_proto.GraphDef)(nil),                       // 29: tensorflow.GraphDef
	(*CoordinationServiceConfig)(nil),                     // 30: tensorflow.CoordinationServiceConfig
}
var file_tensorflow_core_protobuf_config_proto_depIdxs = []int32{
	15, // 0: tensorflow.GPUOptions.experimental:type_name -> tensorflow.GPUOptions.Experimental
	0,  // 1: tensorflow.OptimizerOptions.opt_level:type_name -> tensorflow.OptimizerOptions.Level
	1,  // 2: tensorflow.OptimizerOptions.global_jit_level:type_name -> tensorflow.OptimizerOptions.GlobalJitLevel
	5,  // 3: tensorflow.GraphOptions.optimizer_options:type_name -> tensorflow.OptimizerOptions
	24, // 4: tensorflow.GraphOptions.rewrite_options:type_name -> tensorflow.RewriterConfig
	17, // 5: tensorflow.ConfigProto.device_count:type_name -> tensorflow.ConfigProto.DeviceCountEntry
	7,  // 6: tensorflow.ConfigProto.session_inter_op_thread_pool:type_name -> tensorflow.ThreadPoolOptionProto
	4,  // 7: tensorflow.ConfigProto.gpu_options:type_name -> tensorflow.GPUOptions
	6,  // 8: tensorflow.ConfigProto.graph_options:type_name -> tensorflow.GraphOptions
	8,  // 9: tensorflow.ConfigProto.rpc_options:type_name -> tensorflow.RPCOptions
	25, // 10: tensorflow.ConfigProto.cluster_def:type_name -> tensorflow.ClusterDef
	18, // 11: tensorflow.ConfigProto.experimental:type_name -> tensorflow.ConfigProto.Experimental
	3,  // 12: tensorflow.RunOptions.trace_level:type_name -> tensorflow.RunOptions.TraceLevel
	26, // 13: tensorflow.RunOptions.debug_options:type_name -> tensorflow.DebugOptions
	19, // 14: tensorflow.RunOptions.experimental:type_name -> tensorflow.RunOptions.Experimental
	27, // 15: tensorflow.RunMetadata.step_stats:type_name -> tensorflow.StepStats
	28, // 16: tensorflow.RunMetadata.cost_graph:type_name -> tensorflow.CostGraphDef
	29, // 17: tensorflow.RunMetadata.partition_graphs:type_name -> tensorflow.GraphDef
	21, // 18: tensorflow.RunMetadata.function_graphs:type_name -> tensorflow.RunMetadata.FunctionGraphs
	11, // 19: tensorflow.CallableOptions.run_options:type_name -> tensorflow.RunOptions
	13, // 20: tensorflow.CallableOptions.tensor_connection:type_name -> tensorflow.TensorConnection
	22, // 21: tensorflow.CallableOptions.feed_devices:type_name -> tensorflow.CallableOptions.FeedDevicesEntry
	23, // 22: tensorflow.CallableOptions.fetch_devices:type_name -> tensorflow.CallableOptions.FetchDevicesEntry
	16, // 23: tensorflow.GPUOptions.Experimental.virtual_devices:type_name -> tensorflow.GPUOptions.Experimental.VirtualDevices
	9,  // 24: tensorflow.ConfigProto.Experimental.session_metadata:type_name -> tensorflow.SessionMetadata
	2,  // 25: tensorflow.ConfigProto.Experimental.mlir_bridge_rollout:type_name -> tensorflow.ConfigProto.Experimental.MlirBridgeRollout
	30, // 26: tensorflow.ConfigProto.Experimental.coordination_config:type_name -> tensorflow.CoordinationServiceConfig
	20, // 27: tensorflow.RunOptions.Experimental.run_handler_pool_options:type_name -> tensorflow.RunOptions.Experimental.RunHandlerPoolOptions
	29, // 28: tensorflow.RunMetadata.FunctionGraphs.partition_graphs:type_name -> tensorflow.GraphDef
	29, // 29: tensorflow.RunMetadata.FunctionGraphs.pre_optimization_graph:type_name -> tensorflow.GraphDef
	29, // 30: tensorflow.RunMetadata.FunctionGraphs.post_optimization_graph:type_name -> tensorflow.GraphDef
	31, // [31:31] is the sub-list for method output_type
	31, // [31:31] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_tensorflow_core_protobuf_config_proto_init() }
func file_tensorflow_core_protobuf_config_proto_init() {
	if File_tensorflow_core_protobuf_config_proto != nil {
		return
	}
	file_tensorflow_core_protobuf_cluster_proto_init()
	file_tensorflow_core_protobuf_coordination_config_proto_init()
	file_tensorflow_core_protobuf_debug_proto_init()
	file_tensorflow_core_protobuf_rewriter_config_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tensorflow_core_protobuf_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GPUOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OptimizerOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GraphOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThreadPoolOptionProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RPCOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SessionMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TensorConnection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallableOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GPUOptions_Experimental); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GPUOptions_Experimental_VirtualDevices); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigProto_Experimental); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunOptions_Experimental); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunOptions_Experimental_RunHandlerPoolOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_config_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunMetadata_FunctionGraphs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tensorflow_core_protobuf_config_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_core_protobuf_config_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_protobuf_config_proto_depIdxs,
		EnumInfos:         file_tensorflow_core_protobuf_config_proto_enumTypes,
		MessageInfos:      file_tensorflow_core_protobuf_config_proto_msgTypes,
	}.Build()
	File_tensorflow_core_protobuf_config_proto = out.File
	file_tensorflow_core_protobuf_config_proto_rawDesc = nil
	file_tensorflow_core_protobuf_config_proto_goTypes = nil
	file_tensorflow_core_protobuf_config_proto_depIdxs = nil
}
