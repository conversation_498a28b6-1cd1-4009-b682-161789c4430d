// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: tensorflow/core/protobuf/composite_tensor_variant.proto

package for_core_protos_go_proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Metadata for CompositeTensorVariant, used when serializing as Variant.
//
// We define a new message here (rather than directly using TypeSpecProto for
// the metadata string) to retain flexibility to change the metadata encoding
// to support additional features.
type CompositeTensorVariantMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TypeSpecProto *TypeSpecProto `protobuf:"bytes,1,opt,name=type_spec_proto,json=typeSpecProto,proto3" json:"type_spec_proto,omitempty"`
}

func (x *CompositeTensorVariantMetadata) Reset() {
	*x = CompositeTensorVariantMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_composite_tensor_variant_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompositeTensorVariantMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompositeTensorVariantMetadata) ProtoMessage() {}

func (x *CompositeTensorVariantMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_composite_tensor_variant_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompositeTensorVariantMetadata.ProtoReflect.Descriptor instead.
func (*CompositeTensorVariantMetadata) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_composite_tensor_variant_proto_rawDescGZIP(), []int{0}
}

func (x *CompositeTensorVariantMetadata) GetTypeSpecProto() *TypeSpecProto {
	if x != nil {
		return x.TypeSpecProto
	}
	return nil
}

var File_tensorflow_core_protobuf_composite_tensor_variant_proto protoreflect.FileDescriptor

var file_tensorflow_core_protobuf_composite_tensor_variant_proto_rawDesc = []byte{
	0x0a, 0x37, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x65, 0x5f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x76, 0x61, 0x72, 0x69,
	0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x1a, 0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x63, 0x0a, 0x1e,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x56,
	0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x41,
	0x0a, 0x0f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x52, 0x0d, 0x74, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x42, 0x57, 0x5a, 0x55, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2f, 0x67, 0x6f, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x66, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x73, 0x5f, 0x67, 0x6f, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_tensorflow_core_protobuf_composite_tensor_variant_proto_rawDescOnce sync.Once
	file_tensorflow_core_protobuf_composite_tensor_variant_proto_rawDescData = file_tensorflow_core_protobuf_composite_tensor_variant_proto_rawDesc
)

func file_tensorflow_core_protobuf_composite_tensor_variant_proto_rawDescGZIP() []byte {
	file_tensorflow_core_protobuf_composite_tensor_variant_proto_rawDescOnce.Do(func() {
		file_tensorflow_core_protobuf_composite_tensor_variant_proto_rawDescData = protoimpl.X.CompressGZIP(file_tensorflow_core_protobuf_composite_tensor_variant_proto_rawDescData)
	})
	return file_tensorflow_core_protobuf_composite_tensor_variant_proto_rawDescData
}

var file_tensorflow_core_protobuf_composite_tensor_variant_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_tensorflow_core_protobuf_composite_tensor_variant_proto_goTypes = []interface{}{
	(*CompositeTensorVariantMetadata)(nil), // 0: tensorflow.CompositeTensorVariantMetadata
	(*TypeSpecProto)(nil),                  // 1: tensorflow.TypeSpecProto
}
var file_tensorflow_core_protobuf_composite_tensor_variant_proto_depIdxs = []int32{
	1, // 0: tensorflow.CompositeTensorVariantMetadata.type_spec_proto:type_name -> tensorflow.TypeSpecProto
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_tensorflow_core_protobuf_composite_tensor_variant_proto_init() }
func file_tensorflow_core_protobuf_composite_tensor_variant_proto_init() {
	if File_tensorflow_core_protobuf_composite_tensor_variant_proto != nil {
		return
	}
	file_tensorflow_core_protobuf_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tensorflow_core_protobuf_composite_tensor_variant_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompositeTensorVariantMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tensorflow_core_protobuf_composite_tensor_variant_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_core_protobuf_composite_tensor_variant_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_protobuf_composite_tensor_variant_proto_depIdxs,
		MessageInfos:      file_tensorflow_core_protobuf_composite_tensor_variant_proto_msgTypes,
	}.Build()
	File_tensorflow_core_protobuf_composite_tensor_variant_proto = out.File
	file_tensorflow_core_protobuf_composite_tensor_variant_proto_rawDesc = nil
	file_tensorflow_core_protobuf_composite_tensor_variant_proto_goTypes = nil
	file_tensorflow_core_protobuf_composite_tensor_variant_proto_depIdxs = nil
}
