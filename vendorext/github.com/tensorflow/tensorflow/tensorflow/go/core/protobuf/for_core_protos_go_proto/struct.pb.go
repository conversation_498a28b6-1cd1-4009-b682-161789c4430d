// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: tensorflow/core/protobuf/struct.proto

package for_core_protos_go_proto

import (
	tensor_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto"
	tensor_shape_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto"
	types_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TypeSpecProto_TypeSpecClass int32

const (
	TypeSpecProto_UNKNOWN              TypeSpecProto_TypeSpecClass = 0
	TypeSpecProto_SPARSE_TENSOR_SPEC   TypeSpecProto_TypeSpecClass = 1  // tf.SparseTensorSpec
	TypeSpecProto_INDEXED_SLICES_SPEC  TypeSpecProto_TypeSpecClass = 2  // tf.IndexedSlicesSpec
	TypeSpecProto_RAGGED_TENSOR_SPEC   TypeSpecProto_TypeSpecClass = 3  // tf.RaggedTensorSpec
	TypeSpecProto_TENSOR_ARRAY_SPEC    TypeSpecProto_TypeSpecClass = 4  // tf.TensorArraySpec
	TypeSpecProto_DATA_DATASET_SPEC    TypeSpecProto_TypeSpecClass = 5  // tf.data.DatasetSpec
	TypeSpecProto_DATA_ITERATOR_SPEC   TypeSpecProto_TypeSpecClass = 6  // IteratorSpec from data/ops/iterator_ops.py
	TypeSpecProto_OPTIONAL_SPEC        TypeSpecProto_TypeSpecClass = 7  // tf.OptionalSpec
	TypeSpecProto_PER_REPLICA_SPEC     TypeSpecProto_TypeSpecClass = 8  // PerReplicaSpec from distribute/values.py
	TypeSpecProto_VARIABLE_SPEC        TypeSpecProto_TypeSpecClass = 9  // tf.VariableSpec
	TypeSpecProto_ROW_PARTITION_SPEC   TypeSpecProto_TypeSpecClass = 10 // RowPartitionSpec from ragged/row_partition.py
	TypeSpecProto_REGISTERED_TYPE_SPEC TypeSpecProto_TypeSpecClass = 12 // The type registered as type_spec_class_name.
	TypeSpecProto_EXTENSION_TYPE_SPEC  TypeSpecProto_TypeSpecClass = 13 // Subclasses of tf.ExtensionType
)

// Enum value maps for TypeSpecProto_TypeSpecClass.
var (
	TypeSpecProto_TypeSpecClass_name = map[int32]string{
		0:  "UNKNOWN",
		1:  "SPARSE_TENSOR_SPEC",
		2:  "INDEXED_SLICES_SPEC",
		3:  "RAGGED_TENSOR_SPEC",
		4:  "TENSOR_ARRAY_SPEC",
		5:  "DATA_DATASET_SPEC",
		6:  "DATA_ITERATOR_SPEC",
		7:  "OPTIONAL_SPEC",
		8:  "PER_REPLICA_SPEC",
		9:  "VARIABLE_SPEC",
		10: "ROW_PARTITION_SPEC",
		12: "REGISTERED_TYPE_SPEC",
		13: "EXTENSION_TYPE_SPEC",
	}
	TypeSpecProto_TypeSpecClass_value = map[string]int32{
		"UNKNOWN":              0,
		"SPARSE_TENSOR_SPEC":   1,
		"INDEXED_SLICES_SPEC":  2,
		"RAGGED_TENSOR_SPEC":   3,
		"TENSOR_ARRAY_SPEC":    4,
		"DATA_DATASET_SPEC":    5,
		"DATA_ITERATOR_SPEC":   6,
		"OPTIONAL_SPEC":        7,
		"PER_REPLICA_SPEC":     8,
		"VARIABLE_SPEC":        9,
		"ROW_PARTITION_SPEC":   10,
		"REGISTERED_TYPE_SPEC": 12,
		"EXTENSION_TYPE_SPEC":  13,
	}
)

func (x TypeSpecProto_TypeSpecClass) Enum() *TypeSpecProto_TypeSpecClass {
	p := new(TypeSpecProto_TypeSpecClass)
	*p = x
	return p
}

func (x TypeSpecProto_TypeSpecClass) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TypeSpecProto_TypeSpecClass) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_core_protobuf_struct_proto_enumTypes[0].Descriptor()
}

func (TypeSpecProto_TypeSpecClass) Type() protoreflect.EnumType {
	return &file_tensorflow_core_protobuf_struct_proto_enumTypes[0]
}

func (x TypeSpecProto_TypeSpecClass) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TypeSpecProto_TypeSpecClass.Descriptor instead.
func (TypeSpecProto_TypeSpecClass) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_struct_proto_rawDescGZIP(), []int{9, 0}
}

// `StructuredValue` represents a dynamically typed value representing various
// data structures that are inspired by Python data structures typically used in
// TensorFlow functions as inputs and outputs.
//
// For example when saving a Layer there may be a `training` argument. If the
// user passes a boolean True/False, that switches between two concrete
// TensorFlow functions. In order to switch between them in the same way after
// loading the SavedModel, we need to represent "True" and "False".
//
// A more advanced example might be a function which takes a list of
// dictionaries mapping from strings to Tensors. In order to map from
// user-specified arguments `[{"a": tf.constant(1.)}, {"q": tf.constant(3.)}]`
// after load to the right saved TensorFlow function, we need to represent the
// nested structure and the strings, recording that we have a trace for anything
// matching `[{"a": tf.TensorSpec(None, tf.float32)}, {"q": tf.TensorSpec([],
// tf.float64)}]` as an example.
//
// Likewise functions may return nested structures of Tensors, for example
// returning a dictionary mapping from strings to Tensors. In order for the
// loaded function to return the same structure we need to serialize it.
//
// This is an ergonomic aid for working with loaded SavedModels, not a promise
// to serialize all possible function signatures. For example we do not expect
// to pickle generic Python objects, and ideally we'd stay language-agnostic.
type StructuredValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The kind of value.
	//
	// Types that are assignable to Kind:
	//
	//	*StructuredValue_NoneValue
	//	*StructuredValue_Float64Value
	//	*StructuredValue_Int64Value
	//	*StructuredValue_StringValue
	//	*StructuredValue_BoolValue
	//	*StructuredValue_TensorShapeValue
	//	*StructuredValue_TensorDtypeValue
	//	*StructuredValue_TensorSpecValue
	//	*StructuredValue_TypeSpecValue
	//	*StructuredValue_BoundedTensorSpecValue
	//	*StructuredValue_ListValue
	//	*StructuredValue_TupleValue
	//	*StructuredValue_DictValue
	//	*StructuredValue_NamedTupleValue
	Kind isStructuredValue_Kind `protobuf_oneof:"kind"`
}

func (x *StructuredValue) Reset() {
	*x = StructuredValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StructuredValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StructuredValue) ProtoMessage() {}

func (x *StructuredValue) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StructuredValue.ProtoReflect.Descriptor instead.
func (*StructuredValue) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_struct_proto_rawDescGZIP(), []int{0}
}

func (m *StructuredValue) GetKind() isStructuredValue_Kind {
	if m != nil {
		return m.Kind
	}
	return nil
}

func (x *StructuredValue) GetNoneValue() *NoneValue {
	if x, ok := x.GetKind().(*StructuredValue_NoneValue); ok {
		return x.NoneValue
	}
	return nil
}

func (x *StructuredValue) GetFloat64Value() float64 {
	if x, ok := x.GetKind().(*StructuredValue_Float64Value); ok {
		return x.Float64Value
	}
	return 0
}

func (x *StructuredValue) GetInt64Value() int64 {
	if x, ok := x.GetKind().(*StructuredValue_Int64Value); ok {
		return x.Int64Value
	}
	return 0
}

func (x *StructuredValue) GetStringValue() string {
	if x, ok := x.GetKind().(*StructuredValue_StringValue); ok {
		return x.StringValue
	}
	return ""
}

func (x *StructuredValue) GetBoolValue() bool {
	if x, ok := x.GetKind().(*StructuredValue_BoolValue); ok {
		return x.BoolValue
	}
	return false
}

func (x *StructuredValue) GetTensorShapeValue() *tensor_shape_go_proto.TensorShapeProto {
	if x, ok := x.GetKind().(*StructuredValue_TensorShapeValue); ok {
		return x.TensorShapeValue
	}
	return nil
}

func (x *StructuredValue) GetTensorDtypeValue() types_go_proto.DataType {
	if x, ok := x.GetKind().(*StructuredValue_TensorDtypeValue); ok {
		return x.TensorDtypeValue
	}
	return types_go_proto.DataType_DT_INVALID
}

func (x *StructuredValue) GetTensorSpecValue() *TensorSpecProto {
	if x, ok := x.GetKind().(*StructuredValue_TensorSpecValue); ok {
		return x.TensorSpecValue
	}
	return nil
}

func (x *StructuredValue) GetTypeSpecValue() *TypeSpecProto {
	if x, ok := x.GetKind().(*StructuredValue_TypeSpecValue); ok {
		return x.TypeSpecValue
	}
	return nil
}

func (x *StructuredValue) GetBoundedTensorSpecValue() *BoundedTensorSpecProto {
	if x, ok := x.GetKind().(*StructuredValue_BoundedTensorSpecValue); ok {
		return x.BoundedTensorSpecValue
	}
	return nil
}

func (x *StructuredValue) GetListValue() *ListValue {
	if x, ok := x.GetKind().(*StructuredValue_ListValue); ok {
		return x.ListValue
	}
	return nil
}

func (x *StructuredValue) GetTupleValue() *TupleValue {
	if x, ok := x.GetKind().(*StructuredValue_TupleValue); ok {
		return x.TupleValue
	}
	return nil
}

func (x *StructuredValue) GetDictValue() *DictValue {
	if x, ok := x.GetKind().(*StructuredValue_DictValue); ok {
		return x.DictValue
	}
	return nil
}

func (x *StructuredValue) GetNamedTupleValue() *NamedTupleValue {
	if x, ok := x.GetKind().(*StructuredValue_NamedTupleValue); ok {
		return x.NamedTupleValue
	}
	return nil
}

type isStructuredValue_Kind interface {
	isStructuredValue_Kind()
}

type StructuredValue_NoneValue struct {
	// Represents None.
	NoneValue *NoneValue `protobuf:"bytes,1,opt,name=none_value,json=noneValue,proto3,oneof"`
}

type StructuredValue_Float64Value struct {
	// Represents a double-precision floating-point value (a Python `float`).
	Float64Value float64 `protobuf:"fixed64,11,opt,name=float64_value,json=float64Value,proto3,oneof"`
}

type StructuredValue_Int64Value struct {
	// Represents a signed integer value, limited to 64 bits.
	// Larger values from Python's arbitrary-precision integers are unsupported.
	Int64Value int64 `protobuf:"zigzag64,12,opt,name=int64_value,json=int64Value,proto3,oneof"`
}

type StructuredValue_StringValue struct {
	// Represents a string of Unicode characters stored in a Python `str`.
	// In Python 3, this is exactly what type `str` is.
	// In Python 2, this is the UTF-8 encoding of the characters.
	// For strings with ASCII characters only (as often used in TensorFlow code)
	// there is effectively no difference between the language versions.
	// The obsolescent `unicode` type of Python 2 is not supported here.
	StringValue string `protobuf:"bytes,13,opt,name=string_value,json=stringValue,proto3,oneof"`
}

type StructuredValue_BoolValue struct {
	// Represents a boolean value.
	BoolValue bool `protobuf:"varint,14,opt,name=bool_value,json=boolValue,proto3,oneof"`
}

type StructuredValue_TensorShapeValue struct {
	// Represents a TensorShape.
	TensorShapeValue *tensor_shape_go_proto.TensorShapeProto `protobuf:"bytes,31,opt,name=tensor_shape_value,json=tensorShapeValue,proto3,oneof"`
}

type StructuredValue_TensorDtypeValue struct {
	// Represents an enum value for dtype.
	TensorDtypeValue types_go_proto.DataType `protobuf:"varint,32,opt,name=tensor_dtype_value,json=tensorDtypeValue,proto3,enum=tensorflow.DataType,oneof"`
}

type StructuredValue_TensorSpecValue struct {
	// Represents a value for tf.TensorSpec.
	TensorSpecValue *TensorSpecProto `protobuf:"bytes,33,opt,name=tensor_spec_value,json=tensorSpecValue,proto3,oneof"`
}

type StructuredValue_TypeSpecValue struct {
	// Represents a value for tf.TypeSpec.
	TypeSpecValue *TypeSpecProto `protobuf:"bytes,34,opt,name=type_spec_value,json=typeSpecValue,proto3,oneof"`
}

type StructuredValue_BoundedTensorSpecValue struct {
	// Represents a value for tf.BoundedTensorSpec.
	BoundedTensorSpecValue *BoundedTensorSpecProto `protobuf:"bytes,35,opt,name=bounded_tensor_spec_value,json=boundedTensorSpecValue,proto3,oneof"`
}

type StructuredValue_ListValue struct {
	// Represents a list of `Value`.
	ListValue *ListValue `protobuf:"bytes,51,opt,name=list_value,json=listValue,proto3,oneof"`
}

type StructuredValue_TupleValue struct {
	// Represents a tuple of `Value`.
	TupleValue *TupleValue `protobuf:"bytes,52,opt,name=tuple_value,json=tupleValue,proto3,oneof"`
}

type StructuredValue_DictValue struct {
	// Represents a dict `Value`.
	DictValue *DictValue `protobuf:"bytes,53,opt,name=dict_value,json=dictValue,proto3,oneof"`
}

type StructuredValue_NamedTupleValue struct {
	// Represents Python's namedtuple.
	NamedTupleValue *NamedTupleValue `protobuf:"bytes,54,opt,name=named_tuple_value,json=namedTupleValue,proto3,oneof"`
}

func (*StructuredValue_NoneValue) isStructuredValue_Kind() {}

func (*StructuredValue_Float64Value) isStructuredValue_Kind() {}

func (*StructuredValue_Int64Value) isStructuredValue_Kind() {}

func (*StructuredValue_StringValue) isStructuredValue_Kind() {}

func (*StructuredValue_BoolValue) isStructuredValue_Kind() {}

func (*StructuredValue_TensorShapeValue) isStructuredValue_Kind() {}

func (*StructuredValue_TensorDtypeValue) isStructuredValue_Kind() {}

func (*StructuredValue_TensorSpecValue) isStructuredValue_Kind() {}

func (*StructuredValue_TypeSpecValue) isStructuredValue_Kind() {}

func (*StructuredValue_BoundedTensorSpecValue) isStructuredValue_Kind() {}

func (*StructuredValue_ListValue) isStructuredValue_Kind() {}

func (*StructuredValue_TupleValue) isStructuredValue_Kind() {}

func (*StructuredValue_DictValue) isStructuredValue_Kind() {}

func (*StructuredValue_NamedTupleValue) isStructuredValue_Kind() {}

// Represents None.
type NoneValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NoneValue) Reset() {
	*x = NoneValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoneValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoneValue) ProtoMessage() {}

func (x *NoneValue) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoneValue.ProtoReflect.Descriptor instead.
func (*NoneValue) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_struct_proto_rawDescGZIP(), []int{1}
}

// Represents a Python list.
type ListValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Values []*StructuredValue `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *ListValue) Reset() {
	*x = ListValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListValue) ProtoMessage() {}

func (x *ListValue) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListValue.ProtoReflect.Descriptor instead.
func (*ListValue) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_struct_proto_rawDescGZIP(), []int{2}
}

func (x *ListValue) GetValues() []*StructuredValue {
	if x != nil {
		return x.Values
	}
	return nil
}

// Represents a Python tuple.
type TupleValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Values []*StructuredValue `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *TupleValue) Reset() {
	*x = TupleValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TupleValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TupleValue) ProtoMessage() {}

func (x *TupleValue) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TupleValue.ProtoReflect.Descriptor instead.
func (*TupleValue) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_struct_proto_rawDescGZIP(), []int{3}
}

func (x *TupleValue) GetValues() []*StructuredValue {
	if x != nil {
		return x.Values
	}
	return nil
}

// Represents a Python dict keyed by `str`.
// The comment on Unicode from Value.string_value applies analogously.
type DictValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fields map[string]*StructuredValue `protobuf:"bytes,1,rep,name=fields,proto3" json:"fields,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DictValue) Reset() {
	*x = DictValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DictValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictValue) ProtoMessage() {}

func (x *DictValue) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictValue.ProtoReflect.Descriptor instead.
func (*DictValue) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_struct_proto_rawDescGZIP(), []int{4}
}

func (x *DictValue) GetFields() map[string]*StructuredValue {
	if x != nil {
		return x.Fields
	}
	return nil
}

// Represents a (key, value) pair.
type PairValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string           `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value *StructuredValue `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *PairValue) Reset() {
	*x = PairValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PairValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PairValue) ProtoMessage() {}

func (x *PairValue) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PairValue.ProtoReflect.Descriptor instead.
func (*PairValue) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_struct_proto_rawDescGZIP(), []int{5}
}

func (x *PairValue) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *PairValue) GetValue() *StructuredValue {
	if x != nil {
		return x.Value
	}
	return nil
}

// Represents Python's namedtuple.
type NamedTupleValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string       `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Values []*PairValue `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *NamedTupleValue) Reset() {
	*x = NamedTupleValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamedTupleValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedTupleValue) ProtoMessage() {}

func (x *NamedTupleValue) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedTupleValue.ProtoReflect.Descriptor instead.
func (*NamedTupleValue) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_struct_proto_rawDescGZIP(), []int{6}
}

func (x *NamedTupleValue) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedTupleValue) GetValues() []*PairValue {
	if x != nil {
		return x.Values
	}
	return nil
}

// A protobuf to represent tf.TensorSpec.
type TensorSpecProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string                                  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Shape *tensor_shape_go_proto.TensorShapeProto `protobuf:"bytes,2,opt,name=shape,proto3" json:"shape,omitempty"`
	Dtype types_go_proto.DataType                 `protobuf:"varint,3,opt,name=dtype,proto3,enum=tensorflow.DataType" json:"dtype,omitempty"`
}

func (x *TensorSpecProto) Reset() {
	*x = TensorSpecProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TensorSpecProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorSpecProto) ProtoMessage() {}

func (x *TensorSpecProto) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorSpecProto.ProtoReflect.Descriptor instead.
func (*TensorSpecProto) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_struct_proto_rawDescGZIP(), []int{7}
}

func (x *TensorSpecProto) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TensorSpecProto) GetShape() *tensor_shape_go_proto.TensorShapeProto {
	if x != nil {
		return x.Shape
	}
	return nil
}

func (x *TensorSpecProto) GetDtype() types_go_proto.DataType {
	if x != nil {
		return x.Dtype
	}
	return types_go_proto.DataType_DT_INVALID
}

// A protobuf to represent tf.BoundedTensorSpec.
type BoundedTensorSpecProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string                                  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Shape   *tensor_shape_go_proto.TensorShapeProto `protobuf:"bytes,2,opt,name=shape,proto3" json:"shape,omitempty"`
	Dtype   types_go_proto.DataType                 `protobuf:"varint,3,opt,name=dtype,proto3,enum=tensorflow.DataType" json:"dtype,omitempty"`
	Minimum *tensor_go_proto.TensorProto            `protobuf:"bytes,4,opt,name=minimum,proto3" json:"minimum,omitempty"`
	Maximum *tensor_go_proto.TensorProto            `protobuf:"bytes,5,opt,name=maximum,proto3" json:"maximum,omitempty"`
}

func (x *BoundedTensorSpecProto) Reset() {
	*x = BoundedTensorSpecProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoundedTensorSpecProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoundedTensorSpecProto) ProtoMessage() {}

func (x *BoundedTensorSpecProto) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoundedTensorSpecProto.ProtoReflect.Descriptor instead.
func (*BoundedTensorSpecProto) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_struct_proto_rawDescGZIP(), []int{8}
}

func (x *BoundedTensorSpecProto) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BoundedTensorSpecProto) GetShape() *tensor_shape_go_proto.TensorShapeProto {
	if x != nil {
		return x.Shape
	}
	return nil
}

func (x *BoundedTensorSpecProto) GetDtype() types_go_proto.DataType {
	if x != nil {
		return x.Dtype
	}
	return types_go_proto.DataType_DT_INVALID
}

func (x *BoundedTensorSpecProto) GetMinimum() *tensor_go_proto.TensorProto {
	if x != nil {
		return x.Minimum
	}
	return nil
}

func (x *BoundedTensorSpecProto) GetMaximum() *tensor_go_proto.TensorProto {
	if x != nil {
		return x.Maximum
	}
	return nil
}

// Represents a tf.TypeSpec
type TypeSpecProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TypeSpecClass TypeSpecProto_TypeSpecClass `protobuf:"varint,1,opt,name=type_spec_class,json=typeSpecClass,proto3,enum=tensorflow.TypeSpecProto_TypeSpecClass" json:"type_spec_class,omitempty"`
	// The value returned by TypeSpec._serialize().
	TypeState *StructuredValue `protobuf:"bytes,2,opt,name=type_state,json=typeState,proto3" json:"type_state,omitempty"`
	// The name of the TypeSpec class.
	//   - If type_spec_class == REGISTERED_TYPE_SPEC, the TypeSpec class is
	//     the one registered under this name. For types registered outside
	//     core TensorFlow by an add-on library, that library must be loaded
	//     before this value can be deserialized by nested_structure_coder.
	//   - If type_spec_class specifies a particular TypeSpec class, this field is
	//     redundant with the type_spec_class enum, and is only used for error
	//     reporting in older binaries that do not know the tupe_spec_class enum.
	TypeSpecClassName string `protobuf:"bytes,3,opt,name=type_spec_class_name,json=typeSpecClassName,proto3" json:"type_spec_class_name,omitempty"`
	// The number of flat tensor components required by this TypeSpec.
	NumFlatComponents int32 `protobuf:"varint,4,opt,name=num_flat_components,json=numFlatComponents,proto3" json:"num_flat_components,omitempty"`
}

func (x *TypeSpecProto) Reset() {
	*x = TypeSpecProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TypeSpecProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TypeSpecProto) ProtoMessage() {}

func (x *TypeSpecProto) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_struct_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TypeSpecProto.ProtoReflect.Descriptor instead.
func (*TypeSpecProto) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_struct_proto_rawDescGZIP(), []int{9}
}

func (x *TypeSpecProto) GetTypeSpecClass() TypeSpecProto_TypeSpecClass {
	if x != nil {
		return x.TypeSpecClass
	}
	return TypeSpecProto_UNKNOWN
}

func (x *TypeSpecProto) GetTypeState() *StructuredValue {
	if x != nil {
		return x.TypeState
	}
	return nil
}

func (x *TypeSpecProto) GetTypeSpecClassName() string {
	if x != nil {
		return x.TypeSpecClassName
	}
	return ""
}

func (x *TypeSpecProto) GetNumFlatComponents() int32 {
	if x != nil {
		return x.NumFlatComponents
	}
	return 0
}

var File_tensorflow_core_protobuf_struct_proto protoreflect.FileDescriptor

var file_tensorflow_core_protobuf_struct_proto_rawDesc = []byte{
	0x0a, 0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x1a, 0x26, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f,
	0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61,
	0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x73, 0x68,
	0x61, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65,
	0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xdc, 0x06, 0x0a, 0x0f, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x64, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x36, 0x0a, 0x0a, 0x6e, 0x6f, 0x6e, 0x65, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x4e, 0x6f, 0x6e, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48,
	0x00, 0x52, 0x09, 0x6e, 0x6f, 0x6e, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x25, 0x0a, 0x0d,
	0x66, 0x6c, 0x6f, 0x61, 0x74, 0x36, 0x34, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0c, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x36, 0x34, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x21, 0x0a, 0x0b, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x12, 0x48, 0x00, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x23, 0x0a, 0x0c, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b,
	0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1f, 0x0a, 0x0a, 0x62,
	0x6f, 0x6f, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x00, 0x52, 0x09, 0x62, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x4c, 0x0a, 0x12,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x73, 0x68, 0x61, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53, 0x68, 0x61, 0x70,
	0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x48, 0x00, 0x52, 0x10, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x53, 0x68, 0x61, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x44, 0x0a, 0x12, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x64, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x20, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x10,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x74, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x49, 0x0a, 0x11, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53,
	0x70, 0x65, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x48, 0x00, 0x52, 0x0f, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x53, 0x70, 0x65, 0x63, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x43, 0x0a, 0x0f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x22,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x48,
	0x00, 0x52, 0x0d, 0x74, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x5f, 0x0a, 0x19, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x23, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53, 0x70,
	0x65, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x48, 0x00, 0x52, 0x16, 0x62, 0x6f, 0x75, 0x6e, 0x64,
	0x65, 0x64, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53, 0x70, 0x65, 0x63, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x36, 0x0a, 0x0a, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x33, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x09,
	0x6c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x74, 0x75, 0x70,
	0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x34, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x75, 0x70, 0x6c,
	0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x74, 0x75, 0x70, 0x6c, 0x65, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x36, 0x0a, 0x0a, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x35, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x69, 0x63, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48,
	0x00, 0x52, 0x09, 0x64, 0x69, 0x63, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x49, 0x0a, 0x11,
	0x6e, 0x61, 0x6d, 0x65, 0x64, 0x5f, 0x74, 0x75, 0x70, 0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x36, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x0f, 0x6e, 0x61, 0x6d, 0x65, 0x64, 0x54, 0x75, 0x70,
	0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x22,
	0x0b, 0x0a, 0x09, 0x4e, 0x6f, 0x6e, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x40, 0x0a, 0x09,
	0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x33, 0x0a, 0x06, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65,
	0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x41,
	0x0a, 0x0a, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x33, 0x0a, 0x06,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x75, 0x72, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x22, 0x9e, 0x01, 0x0a, 0x09, 0x44, 0x69, 0x63, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x39, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x69, 0x63,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x1a, 0x56, 0x0a, 0x0b, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x31, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72,
	0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x50, 0x0a, 0x09, 0x50, 0x61, 0x69, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x31, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x22, 0x54, 0x0a, 0x0f, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x54, 0x75, 0x70,
	0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x50, 0x61, 0x69, 0x72, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x85, 0x01, 0x0a, 0x0f, 0x54,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53, 0x70, 0x65, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x73, 0x68, 0x61, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53, 0x68, 0x61, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52,
	0x05, 0x73, 0x68, 0x61, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x05, 0x64, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x64, 0x74, 0x79,
	0x70, 0x65, 0x22, 0xf2, 0x01, 0x0a, 0x16, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x54, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x53, 0x70, 0x65, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x32, 0x0a, 0x05, 0x73, 0x68, 0x61, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x53, 0x68, 0x61, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x05,
	0x73, 0x68, 0x61, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x05, 0x64, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x64, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x31, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x07, 0x6d, 0x69, 0x6e,
	0x69, 0x6d, 0x75, 0x6d, 0x12, 0x31, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x07,
	0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x22, 0xb8, 0x04, 0x0a, 0x0d, 0x54, 0x79, 0x70, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x4f, 0x0a, 0x0f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x27, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x79,
	0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x0d, 0x74, 0x79, 0x70,
	0x65, 0x53, 0x70, 0x65, 0x63, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x3a, 0x0a, 0x0a, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x75, 0x72, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x74, 0x79, 0x70,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x14, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73,
	0x70, 0x65, 0x63, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x6e, 0x75, 0x6d, 0x5f, 0x66,
	0x6c, 0x61, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6e, 0x75, 0x6d, 0x46, 0x6c, 0x61, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xb8, 0x02, 0x0a, 0x0d, 0x54, 0x79, 0x70, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x50, 0x41, 0x52, 0x53, 0x45,
	0x5f, 0x54, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x10, 0x01, 0x12, 0x17,
	0x0a, 0x13, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x45, 0x44, 0x5f, 0x53, 0x4c, 0x49, 0x43, 0x45, 0x53,
	0x5f, 0x53, 0x50, 0x45, 0x43, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x41, 0x47, 0x47, 0x45,
	0x44, 0x5f, 0x54, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x10, 0x03, 0x12,
	0x15, 0x0a, 0x11, 0x54, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f,
	0x53, 0x50, 0x45, 0x43, 0x10, 0x04, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x10, 0x05, 0x12, 0x16, 0x0a,
	0x12, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x49, 0x54, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x53,
	0x50, 0x45, 0x43, 0x10, 0x06, 0x12, 0x11, 0x0a, 0x0d, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x41,
	0x4c, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x10, 0x07, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x45, 0x52, 0x5f,
	0x52, 0x45, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x10, 0x08, 0x12, 0x11,
	0x0a, 0x0d, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x10,
	0x09, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x4f, 0x57, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x10, 0x0a, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x47,
	0x49, 0x53, 0x54, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x50, 0x45,
	0x43, 0x10, 0x0c, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x10, 0x0d, 0x22, 0x04, 0x08, 0x0b,
	0x10, 0x0b, 0x42, 0x57, 0x5a, 0x55, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2f, 0x67, 0x6f, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x66, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x73, 0x5f, 0x67, 0x6f, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_tensorflow_core_protobuf_struct_proto_rawDescOnce sync.Once
	file_tensorflow_core_protobuf_struct_proto_rawDescData = file_tensorflow_core_protobuf_struct_proto_rawDesc
)

func file_tensorflow_core_protobuf_struct_proto_rawDescGZIP() []byte {
	file_tensorflow_core_protobuf_struct_proto_rawDescOnce.Do(func() {
		file_tensorflow_core_protobuf_struct_proto_rawDescData = protoimpl.X.CompressGZIP(file_tensorflow_core_protobuf_struct_proto_rawDescData)
	})
	return file_tensorflow_core_protobuf_struct_proto_rawDescData
}

var file_tensorflow_core_protobuf_struct_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tensorflow_core_protobuf_struct_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_tensorflow_core_protobuf_struct_proto_goTypes = []interface{}{
	(TypeSpecProto_TypeSpecClass)(0), // 0: tensorflow.TypeSpecProto.TypeSpecClass
	(*StructuredValue)(nil),          // 1: tensorflow.StructuredValue
	(*NoneValue)(nil),                // 2: tensorflow.NoneValue
	(*ListValue)(nil),                // 3: tensorflow.ListValue
	(*TupleValue)(nil),               // 4: tensorflow.TupleValue
	(*DictValue)(nil),                // 5: tensorflow.DictValue
	(*PairValue)(nil),                // 6: tensorflow.PairValue
	(*NamedTupleValue)(nil),          // 7: tensorflow.NamedTupleValue
	(*TensorSpecProto)(nil),          // 8: tensorflow.TensorSpecProto
	(*BoundedTensorSpecProto)(nil),   // 9: tensorflow.BoundedTensorSpecProto
	(*TypeSpecProto)(nil),            // 10: tensorflow.TypeSpecProto
	nil,                              // 11: tensorflow.DictValue.FieldsEntry
	(*tensor_shape_go_proto.TensorShapeProto)(nil), // 12: tensorflow.TensorShapeProto
	(types_go_proto.DataType)(0),                   // 13: tensorflow.DataType
	(*tensor_go_proto.TensorProto)(nil),            // 14: tensorflow.TensorProto
}
var file_tensorflow_core_protobuf_struct_proto_depIdxs = []int32{
	2,  // 0: tensorflow.StructuredValue.none_value:type_name -> tensorflow.NoneValue
	12, // 1: tensorflow.StructuredValue.tensor_shape_value:type_name -> tensorflow.TensorShapeProto
	13, // 2: tensorflow.StructuredValue.tensor_dtype_value:type_name -> tensorflow.DataType
	8,  // 3: tensorflow.StructuredValue.tensor_spec_value:type_name -> tensorflow.TensorSpecProto
	10, // 4: tensorflow.StructuredValue.type_spec_value:type_name -> tensorflow.TypeSpecProto
	9,  // 5: tensorflow.StructuredValue.bounded_tensor_spec_value:type_name -> tensorflow.BoundedTensorSpecProto
	3,  // 6: tensorflow.StructuredValue.list_value:type_name -> tensorflow.ListValue
	4,  // 7: tensorflow.StructuredValue.tuple_value:type_name -> tensorflow.TupleValue
	5,  // 8: tensorflow.StructuredValue.dict_value:type_name -> tensorflow.DictValue
	7,  // 9: tensorflow.StructuredValue.named_tuple_value:type_name -> tensorflow.NamedTupleValue
	1,  // 10: tensorflow.ListValue.values:type_name -> tensorflow.StructuredValue
	1,  // 11: tensorflow.TupleValue.values:type_name -> tensorflow.StructuredValue
	11, // 12: tensorflow.DictValue.fields:type_name -> tensorflow.DictValue.FieldsEntry
	1,  // 13: tensorflow.PairValue.value:type_name -> tensorflow.StructuredValue
	6,  // 14: tensorflow.NamedTupleValue.values:type_name -> tensorflow.PairValue
	12, // 15: tensorflow.TensorSpecProto.shape:type_name -> tensorflow.TensorShapeProto
	13, // 16: tensorflow.TensorSpecProto.dtype:type_name -> tensorflow.DataType
	12, // 17: tensorflow.BoundedTensorSpecProto.shape:type_name -> tensorflow.TensorShapeProto
	13, // 18: tensorflow.BoundedTensorSpecProto.dtype:type_name -> tensorflow.DataType
	14, // 19: tensorflow.BoundedTensorSpecProto.minimum:type_name -> tensorflow.TensorProto
	14, // 20: tensorflow.BoundedTensorSpecProto.maximum:type_name -> tensorflow.TensorProto
	0,  // 21: tensorflow.TypeSpecProto.type_spec_class:type_name -> tensorflow.TypeSpecProto.TypeSpecClass
	1,  // 22: tensorflow.TypeSpecProto.type_state:type_name -> tensorflow.StructuredValue
	1,  // 23: tensorflow.DictValue.FieldsEntry.value:type_name -> tensorflow.StructuredValue
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_tensorflow_core_protobuf_struct_proto_init() }
func file_tensorflow_core_protobuf_struct_proto_init() {
	if File_tensorflow_core_protobuf_struct_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tensorflow_core_protobuf_struct_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StructuredValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_struct_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoneValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_struct_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_struct_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TupleValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_struct_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DictValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_struct_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PairValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_struct_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamedTupleValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_struct_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TensorSpecProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_struct_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoundedTensorSpecProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_struct_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TypeSpecProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_tensorflow_core_protobuf_struct_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*StructuredValue_NoneValue)(nil),
		(*StructuredValue_Float64Value)(nil),
		(*StructuredValue_Int64Value)(nil),
		(*StructuredValue_StringValue)(nil),
		(*StructuredValue_BoolValue)(nil),
		(*StructuredValue_TensorShapeValue)(nil),
		(*StructuredValue_TensorDtypeValue)(nil),
		(*StructuredValue_TensorSpecValue)(nil),
		(*StructuredValue_TypeSpecValue)(nil),
		(*StructuredValue_BoundedTensorSpecValue)(nil),
		(*StructuredValue_ListValue)(nil),
		(*StructuredValue_TupleValue)(nil),
		(*StructuredValue_DictValue)(nil),
		(*StructuredValue_NamedTupleValue)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tensorflow_core_protobuf_struct_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_core_protobuf_struct_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_protobuf_struct_proto_depIdxs,
		EnumInfos:         file_tensorflow_core_protobuf_struct_proto_enumTypes,
		MessageInfos:      file_tensorflow_core_protobuf_struct_proto_msgTypes,
	}.Build()
	File_tensorflow_core_protobuf_struct_proto = out.File
	file_tensorflow_core_protobuf_struct_proto_rawDesc = nil
	file_tensorflow_core_protobuf_struct_proto_goTypes = nil
	file_tensorflow_core_protobuf_struct_proto_depIdxs = nil
}
