// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: tensorflow/core/protobuf/rewriter_config.proto

package for_core_protos_go_proto

import (
	attr_value_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/attr_value_go_proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RewriterConfig_Toggle int32

const (
	RewriterConfig_DEFAULT RewriterConfig_Toggle = 0
	RewriterConfig_ON      RewriterConfig_Toggle = 1
	RewriterConfig_OFF     RewriterConfig_Toggle = 2
	// Enable some aggressive optimizations that use assumptions that TF graphs
	// may break. For example, assume the shape of a placeholder matches its
	// actual feed.
	RewriterConfig_AGGRESSIVE RewriterConfig_Toggle = 3
)

// Enum value maps for RewriterConfig_Toggle.
var (
	RewriterConfig_Toggle_name = map[int32]string{
		0: "DEFAULT",
		1: "ON",
		2: "OFF",
		3: "AGGRESSIVE",
	}
	RewriterConfig_Toggle_value = map[string]int32{
		"DEFAULT":    0,
		"ON":         1,
		"OFF":        2,
		"AGGRESSIVE": 3,
	}
)

func (x RewriterConfig_Toggle) Enum() *RewriterConfig_Toggle {
	p := new(RewriterConfig_Toggle)
	*p = x
	return p
}

func (x RewriterConfig_Toggle) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewriterConfig_Toggle) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_core_protobuf_rewriter_config_proto_enumTypes[0].Descriptor()
}

func (RewriterConfig_Toggle) Type() protoreflect.EnumType {
	return &file_tensorflow_core_protobuf_rewriter_config_proto_enumTypes[0]
}

func (x RewriterConfig_Toggle) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewriterConfig_Toggle.Descriptor instead.
func (RewriterConfig_Toggle) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_rewriter_config_proto_rawDescGZIP(), []int{2, 0}
}

// Enum for layout conversion between NCHW and NHWC on CPU. Default is OFF.
type RewriterConfig_CpuLayout int32

const (
	RewriterConfig_NO_CONVERSION_ON_CPU RewriterConfig_CpuLayout = 0
	RewriterConfig_NCHW_TO_NHWC         RewriterConfig_CpuLayout = 1
	RewriterConfig_NHWC_TO_NCHW         RewriterConfig_CpuLayout = 2
)

// Enum value maps for RewriterConfig_CpuLayout.
var (
	RewriterConfig_CpuLayout_name = map[int32]string{
		0: "NO_CONVERSION_ON_CPU",
		1: "NCHW_TO_NHWC",
		2: "NHWC_TO_NCHW",
	}
	RewriterConfig_CpuLayout_value = map[string]int32{
		"NO_CONVERSION_ON_CPU": 0,
		"NCHW_TO_NHWC":         1,
		"NHWC_TO_NCHW":         2,
	}
)

func (x RewriterConfig_CpuLayout) Enum() *RewriterConfig_CpuLayout {
	p := new(RewriterConfig_CpuLayout)
	*p = x
	return p
}

func (x RewriterConfig_CpuLayout) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewriterConfig_CpuLayout) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_core_protobuf_rewriter_config_proto_enumTypes[1].Descriptor()
}

func (RewriterConfig_CpuLayout) Type() protoreflect.EnumType {
	return &file_tensorflow_core_protobuf_rewriter_config_proto_enumTypes[1]
}

func (x RewriterConfig_CpuLayout) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewriterConfig_CpuLayout.Descriptor instead.
func (RewriterConfig_CpuLayout) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_rewriter_config_proto_rawDescGZIP(), []int{2, 1}
}

// Enum controlling the number of times to run optimizers. The default is to
// run them twice.
type RewriterConfig_NumIterationsType int32

const (
	RewriterConfig_DEFAULT_NUM_ITERS RewriterConfig_NumIterationsType = 0
	RewriterConfig_ONE               RewriterConfig_NumIterationsType = 1
	RewriterConfig_TWO               RewriterConfig_NumIterationsType = 2
)

// Enum value maps for RewriterConfig_NumIterationsType.
var (
	RewriterConfig_NumIterationsType_name = map[int32]string{
		0: "DEFAULT_NUM_ITERS",
		1: "ONE",
		2: "TWO",
	}
	RewriterConfig_NumIterationsType_value = map[string]int32{
		"DEFAULT_NUM_ITERS": 0,
		"ONE":               1,
		"TWO":               2,
	}
)

func (x RewriterConfig_NumIterationsType) Enum() *RewriterConfig_NumIterationsType {
	p := new(RewriterConfig_NumIterationsType)
	*p = x
	return p
}

func (x RewriterConfig_NumIterationsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewriterConfig_NumIterationsType) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_core_protobuf_rewriter_config_proto_enumTypes[2].Descriptor()
}

func (RewriterConfig_NumIterationsType) Type() protoreflect.EnumType {
	return &file_tensorflow_core_protobuf_rewriter_config_proto_enumTypes[2]
}

func (x RewriterConfig_NumIterationsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewriterConfig_NumIterationsType.Descriptor instead.
func (RewriterConfig_NumIterationsType) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_rewriter_config_proto_rawDescGZIP(), []int{2, 2}
}

type RewriterConfig_MemOptType int32

const (
	// The default setting (SCHEDULING and SWAPPING HEURISTICS only)
	RewriterConfig_DEFAULT_MEM_OPT RewriterConfig_MemOptType = 0
	// Disabled in the meta-optimizer.
	RewriterConfig_NO_MEM_OPT RewriterConfig_MemOptType = 1
	// Driven by manual op-level annotations.
	RewriterConfig_MANUAL RewriterConfig_MemOptType = 2
	// Swapping heuristic will move a tensor from the GPU to the CPU and move
	// it back when needed to reduce peak memory usage.
	RewriterConfig_SWAPPING_HEURISTICS RewriterConfig_MemOptType = 4
	// Recomputation heuristics will recompute ops (such as Relu activation)
	// during backprop instead of storing them, reducing peak memory usage.
	RewriterConfig_RECOMPUTATION_HEURISTICS RewriterConfig_MemOptType = 5
	// Scheduling will split big ops such as AddN and try to enforce a schedule
	// of the new computations that decreases peak memory usage.
	RewriterConfig_SCHEDULING_HEURISTICS RewriterConfig_MemOptType = 6
	// Use any combination of swapping and recomputation heuristics.
	RewriterConfig_HEURISTICS RewriterConfig_MemOptType = 3
)

// Enum value maps for RewriterConfig_MemOptType.
var (
	RewriterConfig_MemOptType_name = map[int32]string{
		0: "DEFAULT_MEM_OPT",
		1: "NO_MEM_OPT",
		2: "MANUAL",
		4: "SWAPPING_HEURISTICS",
		5: "RECOMPUTATION_HEURISTICS",
		6: "SCHEDULING_HEURISTICS",
		3: "HEURISTICS",
	}
	RewriterConfig_MemOptType_value = map[string]int32{
		"DEFAULT_MEM_OPT":          0,
		"NO_MEM_OPT":               1,
		"MANUAL":                   2,
		"SWAPPING_HEURISTICS":      4,
		"RECOMPUTATION_HEURISTICS": 5,
		"SCHEDULING_HEURISTICS":    6,
		"HEURISTICS":               3,
	}
)

func (x RewriterConfig_MemOptType) Enum() *RewriterConfig_MemOptType {
	p := new(RewriterConfig_MemOptType)
	*p = x
	return p
}

func (x RewriterConfig_MemOptType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewriterConfig_MemOptType) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_core_protobuf_rewriter_config_proto_enumTypes[3].Descriptor()
}

func (RewriterConfig_MemOptType) Type() protoreflect.EnumType {
	return &file_tensorflow_core_protobuf_rewriter_config_proto_enumTypes[3]
}

func (x RewriterConfig_MemOptType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewriterConfig_MemOptType.Descriptor instead.
func (RewriterConfig_MemOptType) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_rewriter_config_proto_rawDescGZIP(), []int{2, 3}
}

type AutoParallelOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable      bool  `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	NumReplicas int32 `protobuf:"varint,2,opt,name=num_replicas,json=numReplicas,proto3" json:"num_replicas,omitempty"`
}

func (x *AutoParallelOptions) Reset() {
	*x = AutoParallelOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_rewriter_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoParallelOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoParallelOptions) ProtoMessage() {}

func (x *AutoParallelOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_rewriter_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoParallelOptions.ProtoReflect.Descriptor instead.
func (*AutoParallelOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_rewriter_config_proto_rawDescGZIP(), []int{0}
}

func (x *AutoParallelOptions) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *AutoParallelOptions) GetNumReplicas() int32 {
	if x != nil {
		return x.NumReplicas
	}
	return 0
}

type ScopedAllocatorOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// If present, only perform optimization for these ops.
	EnableOp []string `protobuf:"bytes,1,rep,name=enable_op,json=enableOp,proto3" json:"enable_op,omitempty"`
}

func (x *ScopedAllocatorOptions) Reset() {
	*x = ScopedAllocatorOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_rewriter_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScopedAllocatorOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScopedAllocatorOptions) ProtoMessage() {}

func (x *ScopedAllocatorOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_rewriter_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScopedAllocatorOptions.ProtoReflect.Descriptor instead.
func (*ScopedAllocatorOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_rewriter_config_proto_rawDescGZIP(), []int{1}
}

func (x *ScopedAllocatorOptions) GetEnableOp() []string {
	if x != nil {
		return x.EnableOp
	}
	return nil
}

type RewriterConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// CPU Conversion settings between NHCW and NCHW.
	CpuLayoutConversion RewriterConfig_CpuLayout `protobuf:"varint,50,opt,name=cpu_layout_conversion,json=cpuLayoutConversion,proto3,enum=tensorflow.RewriterConfig_CpuLayout" json:"cpu_layout_conversion,omitempty"`
	// Optimize tensor layouts (default is ON)
	// e.g. This will try to use NCHW layout on GPU which is faster.
	LayoutOptimizer RewriterConfig_Toggle `protobuf:"varint,1,opt,name=layout_optimizer,json=layoutOptimizer,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"layout_optimizer,omitempty"`
	// Fold constants (default is ON)
	// Statically infer the value of tensors when possible, and materialize the
	// result using constants.
	ConstantFolding RewriterConfig_Toggle `protobuf:"varint,3,opt,name=constant_folding,json=constantFolding,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"constant_folding,omitempty"`
	// Shape optimizations (default is ON)
	// Simplify computations made on shapes.
	ShapeOptimization RewriterConfig_Toggle `protobuf:"varint,13,opt,name=shape_optimization,json=shapeOptimization,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"shape_optimization,omitempty"`
	// Remapping (default is ON)
	// Remap subgraphs onto more efficient implementations.
	Remapping RewriterConfig_Toggle `protobuf:"varint,14,opt,name=remapping,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"remapping,omitempty"`
	// Common subgraph elimination (default is ON)
	// e.g. Simplify arithmetic ops; merge ops with same value (like constants).
	CommonSubgraphElimination RewriterConfig_Toggle `protobuf:"varint,24,opt,name=common_subgraph_elimination,json=commonSubgraphElimination,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"common_subgraph_elimination,omitempty"`
	// Arithmetic optimizations (default is ON)
	// e.g. Simplify arithmetic ops; merge ops with same value (like constants).
	ArithmeticOptimization RewriterConfig_Toggle `protobuf:"varint,7,opt,name=arithmetic_optimization,json=arithmeticOptimization,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"arithmetic_optimization,omitempty"`
	// Control dependency optimizations (default is ON).
	// Remove redundant control dependencies, which may enable other optimization.
	DependencyOptimization RewriterConfig_Toggle `protobuf:"varint,8,opt,name=dependency_optimization,json=dependencyOptimization,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"dependency_optimization,omitempty"`
	// Loop optimizations (default is ON).
	LoopOptimization RewriterConfig_Toggle `protobuf:"varint,9,opt,name=loop_optimization,json=loopOptimization,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"loop_optimization,omitempty"`
	// Function optimizations (default is ON).
	FunctionOptimization RewriterConfig_Toggle `protobuf:"varint,10,opt,name=function_optimization,json=functionOptimization,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"function_optimization,omitempty"`
	// Strips debug-related nodes from the graph (off by default).
	DebugStripper RewriterConfig_Toggle `protobuf:"varint,11,opt,name=debug_stripper,json=debugStripper,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"debug_stripper,omitempty"`
	// If true, don't remove unnecessary ops from the graph
	DisableModelPruning bool `protobuf:"varint,2,opt,name=disable_model_pruning,json=disableModelPruning,proto3" json:"disable_model_pruning,omitempty"`
	// Try to allocate some independent Op outputs contiguously in order to
	// merge or eliminate downstream Ops (off by default).
	ScopedAllocatorOptimization RewriterConfig_Toggle `protobuf:"varint,15,opt,name=scoped_allocator_optimization,json=scopedAllocatorOptimization,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"scoped_allocator_optimization,omitempty"`
	// Force small ops onto the CPU (default is OFF).
	PinToHostOptimization RewriterConfig_Toggle `protobuf:"varint,18,opt,name=pin_to_host_optimization,json=pinToHostOptimization,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"pin_to_host_optimization,omitempty"`
	// Enable the swap of kernel implementations based on the device placement
	// (default is ON).
	ImplementationSelector RewriterConfig_Toggle `protobuf:"varint,22,opt,name=implementation_selector,json=implementationSelector,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"implementation_selector,omitempty"`
	// Optimize data types for CUDA (default is OFF).
	// This will try to use float16 on GPU which is faster.
	// Note that this can change the numerical stability of the graph and may
	// require the use of loss scaling to maintain model convergence.
	AutoMixedPrecision RewriterConfig_Toggle `protobuf:"varint,23,opt,name=auto_mixed_precision,json=autoMixedPrecision,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"auto_mixed_precision,omitempty"`
	// Optimize data types for MKL (default is OFF).
	// This will try to use bfloat16 on CPUs, which is faster.
	// Note that this can change the numerical stability of the graph.
	AutoMixedPrecisionMkl RewriterConfig_Toggle `protobuf:"varint,25,opt,name=auto_mixed_precision_mkl,json=autoMixedPrecisionMkl,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"auto_mixed_precision_mkl,omitempty"`
	// Emulate a model using data type float16 on CPU (default is OFF).
	// This will try to emulate the float16 inputs and outputs of an operator
	// on CPU to have better correlation with float16 on GPU; however the
	// computation in the operator is based on float32.
	// Note that this can change the numerical stability of the graph.
	AutoMixedPrecisionCpu RewriterConfig_Toggle `protobuf:"varint,29,opt,name=auto_mixed_precision_cpu,json=autoMixedPrecisionCpu,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"auto_mixed_precision_cpu,omitempty"`
	// Disable the entire meta optimizer (off by default).
	DisableMetaOptimizer bool `protobuf:"varint,19,opt,name=disable_meta_optimizer,json=disableMetaOptimizer,proto3" json:"disable_meta_optimizer,omitempty"`
	// Optimizers registered by plugin (default is ON)
	UsePluginOptimizers RewriterConfig_Toggle `protobuf:"varint,28,opt,name=use_plugin_optimizers,json=usePluginOptimizers,proto3,enum=tensorflow.RewriterConfig_Toggle" json:"use_plugin_optimizers,omitempty"`
	// Controls how many times we run the optimizers in meta optimizer (default
	// is once).
	MetaOptimizerIterations RewriterConfig_NumIterationsType `protobuf:"varint,12,opt,name=meta_optimizer_iterations,json=metaOptimizerIterations,proto3,enum=tensorflow.RewriterConfig_NumIterationsType" json:"meta_optimizer_iterations,omitempty"`
	// The minimum number of nodes in a graph to optimizer. For smaller graphs,
	// optimization is skipped.
	// 0 means the system picks an appropriate number.
	// < 0 means do not skip optimization.
	MinGraphNodes int32 `protobuf:"varint,17,opt,name=min_graph_nodes,json=minGraphNodes,proto3" json:"min_graph_nodes,omitempty"`
	// Disable optimizations that assume compressed tensors. Note that this flag
	// is experimental and may be removed in the future.
	ExperimentalDisableCompressedTensorOptimization bool `protobuf:"varint,26,opt,name=experimental_disable_compressed_tensor_optimization,json=experimentalDisableCompressedTensorOptimization,proto3" json:"experimental_disable_compressed_tensor_optimization,omitempty"`
	// Disable folding quantization emulation ops such as FakeQuantWithMinMax* and
	// QuantizeAndDequantize*. Some compilers (e.g. the TF-to-tflite converter)
	// have to extract quantization configs (e.g. min/max range, number of bits,
	// and per-channel) from the quantization emulation ops. Note that this flag
	// is experimental and may be removed in the future. See b/174138564 for more
	// details.
	ExperimentalDisableFoldingQuantizationEmulation bool `protobuf:"varint,27,opt,name=experimental_disable_folding_quantization_emulation,json=experimentalDisableFoldingQuantizationEmulation,proto3" json:"experimental_disable_folding_quantization_emulation,omitempty"`
	// Configures memory optimization passes through the meta-optimizer. Has no
	// effect on manually requested memory optimization passes in the optimizers
	// field.
	MemoryOptimization RewriterConfig_MemOptType `protobuf:"varint,4,opt,name=memory_optimization,json=memoryOptimization,proto3,enum=tensorflow.RewriterConfig_MemOptType" json:"memory_optimization,omitempty"`
	// A node name scope for node names which are valid outputs of recomputations.
	// Inputs to nodes that match this scope may be recomputed (subject either to
	// manual annotation of those input nodes or to manual annotation and
	// heuristics depending on memory_optimization), but the nodes themselves will
	// not be recomputed. This matches any sub-scopes as well, meaning the scope
	// can appear not just as a top-level scope. For example, if the value is
	// "gradients/", the default, it will match node name "gradients/foo",
	// "foo/gradients/bar", but not "foo_gradients/"
	MemoryOptimizerTargetNodeNameScope string `protobuf:"bytes,6,opt,name=memory_optimizer_target_node_name_scope,json=memoryOptimizerTargetNodeNameScope,proto3" json:"memory_optimizer_target_node_name_scope,omitempty"`
	// Maximum number of milliseconds to spend optimizing a single graph before
	// timing out. If less than or equal to 0 (default value) the optimizer will
	// never time out.
	MetaOptimizerTimeoutMs int64 `protobuf:"varint,20,opt,name=meta_optimizer_timeout_ms,json=metaOptimizerTimeoutMs,proto3" json:"meta_optimizer_timeout_ms,omitempty"`
	// Configures AutoParallel optimization passes either through the
	// meta-optimizer or when manually specified through the optimizers field.
	AutoParallel *AutoParallelOptions `protobuf:"bytes,5,opt,name=auto_parallel,json=autoParallel,proto3" json:"auto_parallel,omitempty"`
	// If true, any optimization pass failing will cause the MetaOptimizer to
	// stop with an error. By default - or when set to false, failing passes are
	// skipped silently.
	FailOnOptimizerErrors bool                    `protobuf:"varint,21,opt,name=fail_on_optimizer_errors,json=failOnOptimizerErrors,proto3" json:"fail_on_optimizer_errors,omitempty"`
	ScopedAllocatorOpts   *ScopedAllocatorOptions `protobuf:"bytes,16,opt,name=scoped_allocator_opts,json=scopedAllocatorOpts,proto3" json:"scoped_allocator_opts,omitempty"`
	// If non-empty, will use this as an alternative way to specify a list of
	// optimizations to turn on and the order of the optimizations (replacing the
	// meta-optimizer).
	//
	// Of the RewriterConfig options, only the AutoParallel configuration options
	// (the auto_parallel field) apply to manually requested optimization passes
	// ("autoparallel"). Memory optimization passes ("memory") invoked here are
	// not configurable (in contrast to memory optimization passes through the
	// meta-optimizer) and act only on manual op annotations.
	//
	// Custom optimizers (see custom_optimizers) that are not part of this
	// schedule will be run after - in the order that they were specified.
	Optimizers []string `protobuf:"bytes,100,rep,name=optimizers,proto3" json:"optimizers,omitempty"`
	// list of CustomGraphOptimizers to apply.
	CustomOptimizers []*RewriterConfig_CustomGraphOptimizer `protobuf:"bytes,200,rep,name=custom_optimizers,json=customOptimizers,proto3" json:"custom_optimizers,omitempty"`
	// VerifierConfig specifying the verifiers to be run after every optimizer.
	InterOptimizerVerifierConfig *VerifierConfig `protobuf:"bytes,300,opt,name=inter_optimizer_verifier_config,json=interOptimizerVerifierConfig,proto3" json:"inter_optimizer_verifier_config,omitempty"`
	// VerifierConfig specifying the verifiers to be run at the end, after all
	// optimizers have run.
	PostOptimizationVerifierConfig *VerifierConfig `protobuf:"bytes,301,opt,name=post_optimization_verifier_config,json=postOptimizationVerifierConfig,proto3" json:"post_optimization_verifier_config,omitempty"`
}

func (x *RewriterConfig) Reset() {
	*x = RewriterConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_rewriter_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewriterConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewriterConfig) ProtoMessage() {}

func (x *RewriterConfig) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_rewriter_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewriterConfig.ProtoReflect.Descriptor instead.
func (*RewriterConfig) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_rewriter_config_proto_rawDescGZIP(), []int{2}
}

func (x *RewriterConfig) GetCpuLayoutConversion() RewriterConfig_CpuLayout {
	if x != nil {
		return x.CpuLayoutConversion
	}
	return RewriterConfig_NO_CONVERSION_ON_CPU
}

func (x *RewriterConfig) GetLayoutOptimizer() RewriterConfig_Toggle {
	if x != nil {
		return x.LayoutOptimizer
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetConstantFolding() RewriterConfig_Toggle {
	if x != nil {
		return x.ConstantFolding
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetShapeOptimization() RewriterConfig_Toggle {
	if x != nil {
		return x.ShapeOptimization
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetRemapping() RewriterConfig_Toggle {
	if x != nil {
		return x.Remapping
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetCommonSubgraphElimination() RewriterConfig_Toggle {
	if x != nil {
		return x.CommonSubgraphElimination
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetArithmeticOptimization() RewriterConfig_Toggle {
	if x != nil {
		return x.ArithmeticOptimization
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetDependencyOptimization() RewriterConfig_Toggle {
	if x != nil {
		return x.DependencyOptimization
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetLoopOptimization() RewriterConfig_Toggle {
	if x != nil {
		return x.LoopOptimization
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetFunctionOptimization() RewriterConfig_Toggle {
	if x != nil {
		return x.FunctionOptimization
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetDebugStripper() RewriterConfig_Toggle {
	if x != nil {
		return x.DebugStripper
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetDisableModelPruning() bool {
	if x != nil {
		return x.DisableModelPruning
	}
	return false
}

func (x *RewriterConfig) GetScopedAllocatorOptimization() RewriterConfig_Toggle {
	if x != nil {
		return x.ScopedAllocatorOptimization
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetPinToHostOptimization() RewriterConfig_Toggle {
	if x != nil {
		return x.PinToHostOptimization
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetImplementationSelector() RewriterConfig_Toggle {
	if x != nil {
		return x.ImplementationSelector
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetAutoMixedPrecision() RewriterConfig_Toggle {
	if x != nil {
		return x.AutoMixedPrecision
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetAutoMixedPrecisionMkl() RewriterConfig_Toggle {
	if x != nil {
		return x.AutoMixedPrecisionMkl
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetAutoMixedPrecisionCpu() RewriterConfig_Toggle {
	if x != nil {
		return x.AutoMixedPrecisionCpu
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetDisableMetaOptimizer() bool {
	if x != nil {
		return x.DisableMetaOptimizer
	}
	return false
}

func (x *RewriterConfig) GetUsePluginOptimizers() RewriterConfig_Toggle {
	if x != nil {
		return x.UsePluginOptimizers
	}
	return RewriterConfig_DEFAULT
}

func (x *RewriterConfig) GetMetaOptimizerIterations() RewriterConfig_NumIterationsType {
	if x != nil {
		return x.MetaOptimizerIterations
	}
	return RewriterConfig_DEFAULT_NUM_ITERS
}

func (x *RewriterConfig) GetMinGraphNodes() int32 {
	if x != nil {
		return x.MinGraphNodes
	}
	return 0
}

func (x *RewriterConfig) GetExperimentalDisableCompressedTensorOptimization() bool {
	if x != nil {
		return x.ExperimentalDisableCompressedTensorOptimization
	}
	return false
}

func (x *RewriterConfig) GetExperimentalDisableFoldingQuantizationEmulation() bool {
	if x != nil {
		return x.ExperimentalDisableFoldingQuantizationEmulation
	}
	return false
}

func (x *RewriterConfig) GetMemoryOptimization() RewriterConfig_MemOptType {
	if x != nil {
		return x.MemoryOptimization
	}
	return RewriterConfig_DEFAULT_MEM_OPT
}

func (x *RewriterConfig) GetMemoryOptimizerTargetNodeNameScope() string {
	if x != nil {
		return x.MemoryOptimizerTargetNodeNameScope
	}
	return ""
}

func (x *RewriterConfig) GetMetaOptimizerTimeoutMs() int64 {
	if x != nil {
		return x.MetaOptimizerTimeoutMs
	}
	return 0
}

func (x *RewriterConfig) GetAutoParallel() *AutoParallelOptions {
	if x != nil {
		return x.AutoParallel
	}
	return nil
}

func (x *RewriterConfig) GetFailOnOptimizerErrors() bool {
	if x != nil {
		return x.FailOnOptimizerErrors
	}
	return false
}

func (x *RewriterConfig) GetScopedAllocatorOpts() *ScopedAllocatorOptions {
	if x != nil {
		return x.ScopedAllocatorOpts
	}
	return nil
}

func (x *RewriterConfig) GetOptimizers() []string {
	if x != nil {
		return x.Optimizers
	}
	return nil
}

func (x *RewriterConfig) GetCustomOptimizers() []*RewriterConfig_CustomGraphOptimizer {
	if x != nil {
		return x.CustomOptimizers
	}
	return nil
}

func (x *RewriterConfig) GetInterOptimizerVerifierConfig() *VerifierConfig {
	if x != nil {
		return x.InterOptimizerVerifierConfig
	}
	return nil
}

func (x *RewriterConfig) GetPostOptimizationVerifierConfig() *VerifierConfig {
	if x != nil {
		return x.PostOptimizationVerifierConfig
	}
	return nil
}

// Message to describe custom graph optimizer and its parameters
type RewriterConfig_CustomGraphOptimizer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string                                    `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ParameterMap map[string]*attr_value_go_proto.AttrValue `protobuf:"bytes,2,rep,name=parameter_map,json=parameterMap,proto3" json:"parameter_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RewriterConfig_CustomGraphOptimizer) Reset() {
	*x = RewriterConfig_CustomGraphOptimizer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_rewriter_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewriterConfig_CustomGraphOptimizer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewriterConfig_CustomGraphOptimizer) ProtoMessage() {}

func (x *RewriterConfig_CustomGraphOptimizer) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_rewriter_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewriterConfig_CustomGraphOptimizer.ProtoReflect.Descriptor instead.
func (*RewriterConfig_CustomGraphOptimizer) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_rewriter_config_proto_rawDescGZIP(), []int{2, 0}
}

func (x *RewriterConfig_CustomGraphOptimizer) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RewriterConfig_CustomGraphOptimizer) GetParameterMap() map[string]*attr_value_go_proto.AttrValue {
	if x != nil {
		return x.ParameterMap
	}
	return nil
}

var File_tensorflow_core_protobuf_rewriter_config_proto protoreflect.FileDescriptor

var file_tensorflow_core_protobuf_rewriter_config_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x72, 0x65, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0a, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x1a, 0x2a, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72,
	0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x61, 0x74, 0x74, 0x72, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x50, 0x0a, 0x13, 0x41, 0x75, 0x74, 0x6f,
	0x50, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x5f, 0x72,
	0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6e,
	0x75, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x22, 0x35, 0x0a, 0x16, 0x53, 0x63,
	0x6f, 0x70, 0x65, 0x64, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f,
	0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4f,
	0x70, 0x22, 0xdb, 0x1a, 0x0a, 0x0e, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x58, 0x0a, 0x15, 0x63, 0x70, 0x75, 0x5f, 0x6c, 0x61, 0x79, 0x6f,
	0x75, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x32, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x43, 0x70, 0x75, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x52, 0x13, 0x63, 0x70, 0x75, 0x4c, 0x61,
	0x79, 0x6f, 0x75, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x4c,
	0x0a, 0x10, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x52, 0x0f, 0x6c, 0x61, 0x79,
	0x6f, 0x75, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x12, 0x4c, 0x0a, 0x10,
	0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x66, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x46, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x50, 0x0a, 0x12, 0x73, 0x68,
	0x61, 0x70, 0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x52, 0x11, 0x73, 0x68, 0x61, 0x70, 0x65,
	0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x09,
	0x72, 0x65, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x6f, 0x67, 0x67,
	0x6c, 0x65, 0x52, 0x09, 0x72, 0x65, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x61, 0x0a,
	0x1b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x73, 0x75, 0x62, 0x67, 0x72, 0x61, 0x70, 0x68,
	0x5f, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54,
	0x6f, 0x67, 0x67, 0x6c, 0x65, 0x52, 0x19, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x75, 0x62,
	0x67, 0x72, 0x61, 0x70, 0x68, 0x45, 0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x5a, 0x0a, 0x17, 0x61, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x65, 0x74, 0x69, 0x63, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52,
	0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x6f,
	0x67, 0x67, 0x6c, 0x65, 0x52, 0x16, 0x61, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x65, 0x74, 0x69, 0x63,
	0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5a, 0x0a, 0x17,
	0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6d,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65,
	0x52, 0x16, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x79, 0x4f, 0x70, 0x74, 0x69,
	0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x11, 0x6c, 0x6f, 0x6f, 0x70,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x52, 0x10, 0x6c, 0x6f, 0x6f, 0x70, 0x4f, 0x70, 0x74, 0x69,
	0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x56, 0x0a, 0x15, 0x66, 0x75, 0x6e, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x52, 0x14, 0x66, 0x75, 0x6e, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x48, 0x0a, 0x0e, 0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x70, 0x70,
	0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x52, 0x0d, 0x64, 0x65, 0x62,
	0x75, 0x67, 0x53, 0x74, 0x72, 0x69, 0x70, 0x70, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x15, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x70, 0x72, 0x75, 0x6e,
	0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x64, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x72, 0x75, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x65,
	0x0a, 0x1d, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x52, 0x1b, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x64,
	0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5a, 0x0a, 0x18, 0x70, 0x69, 0x6e, 0x5f, 0x74, 0x6f, 0x5f,
	0x68, 0x6f, 0x73, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x52, 0x15, 0x70, 0x69, 0x6e, 0x54,
	0x6f, 0x48, 0x6f, 0x73, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x5a, 0x0a, 0x17, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54,
	0x6f, 0x67, 0x67, 0x6c, 0x65, 0x52, 0x16, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x53, 0x0a,
	0x14, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x65, 0x63,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x52, 0x12,
	0x61, 0x75, 0x74, 0x6f, 0x4d, 0x69, 0x78, 0x65, 0x64, 0x50, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x5a, 0x0a, 0x18, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x69, 0x78, 0x65, 0x64,
	0x5f, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6b, 0x6c, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x52, 0x15, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x69, 0x78,
	0x65, 0x64, 0x50, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x6b, 0x6c, 0x12, 0x5a,
	0x0a, 0x18, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x65,
	0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x70, 0x75, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65,
	0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x6f, 0x67,
	0x67, 0x6c, 0x65, 0x52, 0x15, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x69, 0x78, 0x65, 0x64, 0x50, 0x72,
	0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x70, 0x75, 0x12, 0x34, 0x0a, 0x16, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6d,
	0x69, 0x7a, 0x65, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x64, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72,
	0x12, 0x55, 0x0a, 0x15, 0x75, 0x73, 0x65, 0x5f, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x73, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x6f, 0x67, 0x67,
	0x6c, 0x65, 0x52, 0x13, 0x75, 0x73, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x73, 0x12, 0x68, 0x0a, 0x19, 0x6d, 0x65, 0x74, 0x61, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4e, 0x75, 0x6d, 0x49, 0x74, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x17, 0x6d, 0x65, 0x74, 0x61, 0x4f, 0x70,
	0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x49, 0x74, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x69, 0x6e, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x6e,
	0x6f, 0x64, 0x65, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x69, 0x6e, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x6c, 0x0a, 0x33, 0x65, 0x78, 0x70,
	0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x2f, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65,
	0x6e, 0x74, 0x61, 0x6c, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x65, 0x64, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6d,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6c, 0x0a, 0x33, 0x65, 0x78, 0x70, 0x65, 0x72,
	0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x66, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1b,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x2f, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74,
	0x61, 0x6c, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67,
	0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6d, 0x75, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x56, 0x0a, 0x13, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x25, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d,
	0x65, 0x6d, 0x4f, 0x70, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x6d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x53, 0x0a,
	0x27, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65,
	0x72, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x22,
	0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x53, 0x63, 0x6f,
	0x70, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6d,
	0x69, 0x7a, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x6d, 0x73, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x16, 0x6d, 0x65, 0x74, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6d,
	0x69, 0x7a, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x4d, 0x73, 0x12, 0x44, 0x0a,
	0x0d, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6c,
	0x6c, 0x65, 0x6c, 0x12, 0x37, 0x0a, 0x18, 0x66, 0x61, 0x69, 0x6c, 0x5f, 0x6f, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x66, 0x61, 0x69, 0x6c, 0x4f, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x56, 0x0a, 0x15,
	0x73, 0x63, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x6f, 0x70, 0x74, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x64, 0x41,
	0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x13, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x6f, 0x72,
	0x4f, 0x70, 0x74, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65,
	0x72, 0x73, 0x18, 0x64, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69,
	0x7a, 0x65, 0x72, 0x73, 0x12, 0x5d, 0x0a, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x73, 0x18, 0xc8, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65,
	0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x47, 0x72, 0x61, 0x70, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65,
	0x72, 0x52, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a,
	0x65, 0x72, 0x73, 0x12, 0x62, 0x0a, 0x1f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0xac, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x1c, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x66, 0x0a, 0x21, 0x70, 0x6f, 0x73, 0x74, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0xad, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x1e, 0x70, 0x6f, 0x73, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a,
	0xea, 0x01, 0x0a, 0x14, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x47, 0x72, 0x61, 0x70, 0x68, 0x4f,
	0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x66, 0x0a, 0x0d,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x47, 0x72, 0x61, 0x70, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6d,
	0x69, 0x7a, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x4d, 0x61, 0x70, 0x1a, 0x56, 0x0a, 0x11, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2b, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x36, 0x0a, 0x06,
	0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c,
	0x54, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x4f,
	0x46, 0x46, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x47, 0x47, 0x52, 0x45, 0x53, 0x53, 0x49,
	0x56, 0x45, 0x10, 0x03, 0x22, 0x49, 0x0a, 0x09, 0x43, 0x70, 0x75, 0x4c, 0x61, 0x79, 0x6f, 0x75,
	0x74, 0x12, 0x18, 0x0a, 0x14, 0x4e, 0x4f, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x49,
	0x4f, 0x4e, 0x5f, 0x4f, 0x4e, 0x5f, 0x43, 0x50, 0x55, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4e,
	0x43, 0x48, 0x57, 0x5f, 0x54, 0x4f, 0x5f, 0x4e, 0x48, 0x57, 0x43, 0x10, 0x01, 0x12, 0x10, 0x0a,
	0x0c, 0x4e, 0x48, 0x57, 0x43, 0x5f, 0x54, 0x4f, 0x5f, 0x4e, 0x43, 0x48, 0x57, 0x10, 0x02, 0x22,
	0x3c, 0x0a, 0x11, 0x4e, 0x75, 0x6d, 0x49, 0x74, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f,
	0x4e, 0x55, 0x4d, 0x5f, 0x49, 0x54, 0x45, 0x52, 0x53, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x4f,
	0x4e, 0x45, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x57, 0x4f, 0x10, 0x02, 0x22, 0x9f, 0x01,
	0x0a, 0x0a, 0x4d, 0x65, 0x6d, 0x4f, 0x70, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f,
	0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x4d, 0x45, 0x4d, 0x5f, 0x4f, 0x50, 0x54, 0x10,
	0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4e, 0x4f, 0x5f, 0x4d, 0x45, 0x4d, 0x5f, 0x4f, 0x50, 0x54, 0x10,
	0x01, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x17, 0x0a,
	0x13, 0x53, 0x57, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x48, 0x45, 0x55, 0x52, 0x49, 0x53,
	0x54, 0x49, 0x43, 0x53, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x45, 0x43, 0x4f, 0x4d, 0x50,
	0x55, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x48, 0x45, 0x55, 0x52, 0x49, 0x53, 0x54, 0x49,
	0x43, 0x53, 0x10, 0x05, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x49,
	0x4e, 0x47, 0x5f, 0x48, 0x45, 0x55, 0x52, 0x49, 0x53, 0x54, 0x49, 0x43, 0x53, 0x10, 0x06, 0x12,
	0x0e, 0x0a, 0x0a, 0x48, 0x45, 0x55, 0x52, 0x49, 0x53, 0x54, 0x49, 0x43, 0x53, 0x10, 0x03, 0x42,
	0x8c, 0x01, 0x0a, 0x18, 0x6f, 0x72, 0x67, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x42, 0x14, 0x52, 0x65,
	0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x73, 0x50, 0x01, 0x5a, 0x55, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2f, 0x67, 0x6f, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x66, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x73, 0x5f, 0x67, 0x6f, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0xf8, 0x01, 0x01, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tensorflow_core_protobuf_rewriter_config_proto_rawDescOnce sync.Once
	file_tensorflow_core_protobuf_rewriter_config_proto_rawDescData = file_tensorflow_core_protobuf_rewriter_config_proto_rawDesc
)

func file_tensorflow_core_protobuf_rewriter_config_proto_rawDescGZIP() []byte {
	file_tensorflow_core_protobuf_rewriter_config_proto_rawDescOnce.Do(func() {
		file_tensorflow_core_protobuf_rewriter_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_tensorflow_core_protobuf_rewriter_config_proto_rawDescData)
	})
	return file_tensorflow_core_protobuf_rewriter_config_proto_rawDescData
}

var file_tensorflow_core_protobuf_rewriter_config_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_tensorflow_core_protobuf_rewriter_config_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_tensorflow_core_protobuf_rewriter_config_proto_goTypes = []interface{}{
	(RewriterConfig_Toggle)(0),                  // 0: tensorflow.RewriterConfig.Toggle
	(RewriterConfig_CpuLayout)(0),               // 1: tensorflow.RewriterConfig.CpuLayout
	(RewriterConfig_NumIterationsType)(0),       // 2: tensorflow.RewriterConfig.NumIterationsType
	(RewriterConfig_MemOptType)(0),              // 3: tensorflow.RewriterConfig.MemOptType
	(*AutoParallelOptions)(nil),                 // 4: tensorflow.AutoParallelOptions
	(*ScopedAllocatorOptions)(nil),              // 5: tensorflow.ScopedAllocatorOptions
	(*RewriterConfig)(nil),                      // 6: tensorflow.RewriterConfig
	(*RewriterConfig_CustomGraphOptimizer)(nil), // 7: tensorflow.RewriterConfig.CustomGraphOptimizer
	nil,                                   // 8: tensorflow.RewriterConfig.CustomGraphOptimizer.ParameterMapEntry
	(*VerifierConfig)(nil),                // 9: tensorflow.VerifierConfig
	(*attr_value_go_proto.AttrValue)(nil), // 10: tensorflow.AttrValue
}
var file_tensorflow_core_protobuf_rewriter_config_proto_depIdxs = []int32{
	1,  // 0: tensorflow.RewriterConfig.cpu_layout_conversion:type_name -> tensorflow.RewriterConfig.CpuLayout
	0,  // 1: tensorflow.RewriterConfig.layout_optimizer:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 2: tensorflow.RewriterConfig.constant_folding:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 3: tensorflow.RewriterConfig.shape_optimization:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 4: tensorflow.RewriterConfig.remapping:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 5: tensorflow.RewriterConfig.common_subgraph_elimination:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 6: tensorflow.RewriterConfig.arithmetic_optimization:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 7: tensorflow.RewriterConfig.dependency_optimization:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 8: tensorflow.RewriterConfig.loop_optimization:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 9: tensorflow.RewriterConfig.function_optimization:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 10: tensorflow.RewriterConfig.debug_stripper:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 11: tensorflow.RewriterConfig.scoped_allocator_optimization:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 12: tensorflow.RewriterConfig.pin_to_host_optimization:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 13: tensorflow.RewriterConfig.implementation_selector:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 14: tensorflow.RewriterConfig.auto_mixed_precision:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 15: tensorflow.RewriterConfig.auto_mixed_precision_mkl:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 16: tensorflow.RewriterConfig.auto_mixed_precision_cpu:type_name -> tensorflow.RewriterConfig.Toggle
	0,  // 17: tensorflow.RewriterConfig.use_plugin_optimizers:type_name -> tensorflow.RewriterConfig.Toggle
	2,  // 18: tensorflow.RewriterConfig.meta_optimizer_iterations:type_name -> tensorflow.RewriterConfig.NumIterationsType
	3,  // 19: tensorflow.RewriterConfig.memory_optimization:type_name -> tensorflow.RewriterConfig.MemOptType
	4,  // 20: tensorflow.RewriterConfig.auto_parallel:type_name -> tensorflow.AutoParallelOptions
	5,  // 21: tensorflow.RewriterConfig.scoped_allocator_opts:type_name -> tensorflow.ScopedAllocatorOptions
	7,  // 22: tensorflow.RewriterConfig.custom_optimizers:type_name -> tensorflow.RewriterConfig.CustomGraphOptimizer
	9,  // 23: tensorflow.RewriterConfig.inter_optimizer_verifier_config:type_name -> tensorflow.VerifierConfig
	9,  // 24: tensorflow.RewriterConfig.post_optimization_verifier_config:type_name -> tensorflow.VerifierConfig
	8,  // 25: tensorflow.RewriterConfig.CustomGraphOptimizer.parameter_map:type_name -> tensorflow.RewriterConfig.CustomGraphOptimizer.ParameterMapEntry
	10, // 26: tensorflow.RewriterConfig.CustomGraphOptimizer.ParameterMapEntry.value:type_name -> tensorflow.AttrValue
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_tensorflow_core_protobuf_rewriter_config_proto_init() }
func file_tensorflow_core_protobuf_rewriter_config_proto_init() {
	if File_tensorflow_core_protobuf_rewriter_config_proto != nil {
		return
	}
	file_tensorflow_core_protobuf_verifier_config_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tensorflow_core_protobuf_rewriter_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoParallelOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_rewriter_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScopedAllocatorOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_rewriter_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewriterConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_rewriter_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewriterConfig_CustomGraphOptimizer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tensorflow_core_protobuf_rewriter_config_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_core_protobuf_rewriter_config_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_protobuf_rewriter_config_proto_depIdxs,
		EnumInfos:         file_tensorflow_core_protobuf_rewriter_config_proto_enumTypes,
		MessageInfos:      file_tensorflow_core_protobuf_rewriter_config_proto_msgTypes,
	}.Build()
	File_tensorflow_core_protobuf_rewriter_config_proto = out.File
	file_tensorflow_core_protobuf_rewriter_config_proto_rawDesc = nil
	file_tensorflow_core_protobuf_rewriter_config_proto_goTypes = nil
	file_tensorflow_core_protobuf_rewriter_config_proto_depIdxs = nil
}
