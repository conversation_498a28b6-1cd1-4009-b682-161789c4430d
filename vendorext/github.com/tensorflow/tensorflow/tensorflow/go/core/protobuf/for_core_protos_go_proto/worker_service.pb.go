// Copyright 2016 The TensorFlow Authors. All Rights Reserved.
//
//Licensed under the Apache License, Version 2.0 (the "License");
//you may not use this file except in compliance with the License.
//You may obtain a copy of the License at
//
//http://www.apache.org/licenses/LICENSE-2.0
//
//Unless required by applicable law or agreed to in writing, software
//distributed under the License is distributed on an "AS IS" BASIS,
//WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//See the License for the specific language governing permissions and
//limitations under the License.
//==============================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: tensorflow/core/protobuf/worker_service.proto

package for_core_protos_go_proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_tensorflow_core_protobuf_worker_service_proto protoreflect.FileDescriptor

var file_tensorflow_core_protobuf_worker_service_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x65,
	0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x67, 0x72, 0x70, 0x63,
	0x1a, 0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xf0, 0x09, 0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x48, 0x0a, 0x09, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x27, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x13, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x26, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0d, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x12, 0x20, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x47, 0x72, 0x61, 0x70, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x47, 0x72, 0x61, 0x70,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x0f, 0x44, 0x65, 0x72,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x47, 0x72, 0x61, 0x70, 0x68, 0x12, 0x22, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x72, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x47, 0x72, 0x61, 0x70, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x23, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65,
	0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x47, 0x72, 0x61, 0x70, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x52, 0x75, 0x6e, 0x47, 0x72, 0x61, 0x70,
	0x68, 0x12, 0x1b, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52,
	0x75, 0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x75, 0x6e, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x0c,
	0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x47, 0x72, 0x61, 0x70, 0x68, 0x12, 0x1f, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75,
	0x70, 0x47, 0x72, 0x61, 0x70, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x6e,
	0x75, 0x70, 0x47, 0x72, 0x61, 0x70, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4b, 0x0a, 0x0a, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x41, 0x6c, 0x6c, 0x12, 0x1d, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x6e,
	0x75, 0x70, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75,
	0x70, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x0a,
	0x52, 0x65, 0x63, 0x76, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x1d, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x63, 0x76, 0x54, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x63, 0x76, 0x54, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x42, 0x0a, 0x07, 0x4c,
	0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x4c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x4c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x42, 0x0a, 0x07, 0x54, 0x72, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x07, 0x52, 0x65, 0x63, 0x76, 0x42, 0x75, 0x66, 0x12, 0x1a,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x63, 0x76,
	0x42, 0x75, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x63, 0x76, 0x42, 0x75, 0x66, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x65, 0x70, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x22, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x65,
	0x70, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x23, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x47, 0x65,
	0x74, 0x53, 0x74, 0x65, 0x70, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x20, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x10, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x23, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8a, 0x01, 0x0a, 0x1a, 0x6f,
	0x72, 0x67, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x64, 0x69,
	0x73, 0x74, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x13, 0x57, 0x6f, 0x72, 0x6b, 0x65,
	0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x50, 0x01,
	0x5a, 0x55, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x67, 0x6f,
	0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66,
	0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x5f, 0x67,
	0x6f, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_tensorflow_core_protobuf_worker_service_proto_goTypes = []interface{}{
	(*GetStatusRequest)(nil),            // 0: tensorflow.GetStatusRequest
	(*CreateWorkerSessionRequest)(nil),  // 1: tensorflow.CreateWorkerSessionRequest
	(*DeleteWorkerSessionRequest)(nil),  // 2: tensorflow.DeleteWorkerSessionRequest
	(*RegisterGraphRequest)(nil),        // 3: tensorflow.RegisterGraphRequest
	(*DeregisterGraphRequest)(nil),      // 4: tensorflow.DeregisterGraphRequest
	(*RunGraphRequest)(nil),             // 5: tensorflow.RunGraphRequest
	(*CleanupGraphRequest)(nil),         // 6: tensorflow.CleanupGraphRequest
	(*CleanupAllRequest)(nil),           // 7: tensorflow.CleanupAllRequest
	(*RecvTensorRequest)(nil),           // 8: tensorflow.RecvTensorRequest
	(*LoggingRequest)(nil),              // 9: tensorflow.LoggingRequest
	(*TracingRequest)(nil),              // 10: tensorflow.TracingRequest
	(*RecvBufRequest)(nil),              // 11: tensorflow.RecvBufRequest
	(*GetStepSequenceRequest)(nil),      // 12: tensorflow.GetStepSequenceRequest
	(*CompleteGroupRequest)(nil),        // 13: tensorflow.CompleteGroupRequest
	(*CompleteInstanceRequest)(nil),     // 14: tensorflow.CompleteInstanceRequest
	(*GetStatusResponse)(nil),           // 15: tensorflow.GetStatusResponse
	(*CreateWorkerSessionResponse)(nil), // 16: tensorflow.CreateWorkerSessionResponse
	(*DeleteWorkerSessionResponse)(nil), // 17: tensorflow.DeleteWorkerSessionResponse
	(*RegisterGraphResponse)(nil),       // 18: tensorflow.RegisterGraphResponse
	(*DeregisterGraphResponse)(nil),     // 19: tensorflow.DeregisterGraphResponse
	(*RunGraphResponse)(nil),            // 20: tensorflow.RunGraphResponse
	(*CleanupGraphResponse)(nil),        // 21: tensorflow.CleanupGraphResponse
	(*CleanupAllResponse)(nil),          // 22: tensorflow.CleanupAllResponse
	(*RecvTensorResponse)(nil),          // 23: tensorflow.RecvTensorResponse
	(*LoggingResponse)(nil),             // 24: tensorflow.LoggingResponse
	(*TracingResponse)(nil),             // 25: tensorflow.TracingResponse
	(*RecvBufResponse)(nil),             // 26: tensorflow.RecvBufResponse
	(*GetStepSequenceResponse)(nil),     // 27: tensorflow.GetStepSequenceResponse
	(*CompleteGroupResponse)(nil),       // 28: tensorflow.CompleteGroupResponse
	(*CompleteInstanceResponse)(nil),    // 29: tensorflow.CompleteInstanceResponse
}
var file_tensorflow_core_protobuf_worker_service_proto_depIdxs = []int32{
	0,  // 0: tensorflow.grpc.WorkerService.GetStatus:input_type -> tensorflow.GetStatusRequest
	1,  // 1: tensorflow.grpc.WorkerService.CreateWorkerSession:input_type -> tensorflow.CreateWorkerSessionRequest
	2,  // 2: tensorflow.grpc.WorkerService.DeleteWorkerSession:input_type -> tensorflow.DeleteWorkerSessionRequest
	3,  // 3: tensorflow.grpc.WorkerService.RegisterGraph:input_type -> tensorflow.RegisterGraphRequest
	4,  // 4: tensorflow.grpc.WorkerService.DeregisterGraph:input_type -> tensorflow.DeregisterGraphRequest
	5,  // 5: tensorflow.grpc.WorkerService.RunGraph:input_type -> tensorflow.RunGraphRequest
	6,  // 6: tensorflow.grpc.WorkerService.CleanupGraph:input_type -> tensorflow.CleanupGraphRequest
	7,  // 7: tensorflow.grpc.WorkerService.CleanupAll:input_type -> tensorflow.CleanupAllRequest
	8,  // 8: tensorflow.grpc.WorkerService.RecvTensor:input_type -> tensorflow.RecvTensorRequest
	9,  // 9: tensorflow.grpc.WorkerService.Logging:input_type -> tensorflow.LoggingRequest
	10, // 10: tensorflow.grpc.WorkerService.Tracing:input_type -> tensorflow.TracingRequest
	11, // 11: tensorflow.grpc.WorkerService.RecvBuf:input_type -> tensorflow.RecvBufRequest
	12, // 12: tensorflow.grpc.WorkerService.GetStepSequence:input_type -> tensorflow.GetStepSequenceRequest
	13, // 13: tensorflow.grpc.WorkerService.CompleteGroup:input_type -> tensorflow.CompleteGroupRequest
	14, // 14: tensorflow.grpc.WorkerService.CompleteInstance:input_type -> tensorflow.CompleteInstanceRequest
	15, // 15: tensorflow.grpc.WorkerService.GetStatus:output_type -> tensorflow.GetStatusResponse
	16, // 16: tensorflow.grpc.WorkerService.CreateWorkerSession:output_type -> tensorflow.CreateWorkerSessionResponse
	17, // 17: tensorflow.grpc.WorkerService.DeleteWorkerSession:output_type -> tensorflow.DeleteWorkerSessionResponse
	18, // 18: tensorflow.grpc.WorkerService.RegisterGraph:output_type -> tensorflow.RegisterGraphResponse
	19, // 19: tensorflow.grpc.WorkerService.DeregisterGraph:output_type -> tensorflow.DeregisterGraphResponse
	20, // 20: tensorflow.grpc.WorkerService.RunGraph:output_type -> tensorflow.RunGraphResponse
	21, // 21: tensorflow.grpc.WorkerService.CleanupGraph:output_type -> tensorflow.CleanupGraphResponse
	22, // 22: tensorflow.grpc.WorkerService.CleanupAll:output_type -> tensorflow.CleanupAllResponse
	23, // 23: tensorflow.grpc.WorkerService.RecvTensor:output_type -> tensorflow.RecvTensorResponse
	24, // 24: tensorflow.grpc.WorkerService.Logging:output_type -> tensorflow.LoggingResponse
	25, // 25: tensorflow.grpc.WorkerService.Tracing:output_type -> tensorflow.TracingResponse
	26, // 26: tensorflow.grpc.WorkerService.RecvBuf:output_type -> tensorflow.RecvBufResponse
	27, // 27: tensorflow.grpc.WorkerService.GetStepSequence:output_type -> tensorflow.GetStepSequenceResponse
	28, // 28: tensorflow.grpc.WorkerService.CompleteGroup:output_type -> tensorflow.CompleteGroupResponse
	29, // 29: tensorflow.grpc.WorkerService.CompleteInstance:output_type -> tensorflow.CompleteInstanceResponse
	15, // [15:30] is the sub-list for method output_type
	0,  // [0:15] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_tensorflow_core_protobuf_worker_service_proto_init() }
func file_tensorflow_core_protobuf_worker_service_proto_init() {
	if File_tensorflow_core_protobuf_worker_service_proto != nil {
		return
	}
	file_tensorflow_core_protobuf_worker_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tensorflow_core_protobuf_worker_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tensorflow_core_protobuf_worker_service_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_protobuf_worker_service_proto_depIdxs,
	}.Build()
	File_tensorflow_core_protobuf_worker_service_proto = out.File
	file_tensorflow_core_protobuf_worker_service_proto_rawDesc = nil
	file_tensorflow_core_protobuf_worker_service_proto_goTypes = nil
	file_tensorflow_core_protobuf_worker_service_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// WorkerServiceClient is the client API for WorkerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type WorkerServiceClient interface {
	// See worker.proto for details.
	GetStatus(ctx context.Context, in *GetStatusRequest, opts ...grpc.CallOption) (*GetStatusResponse, error)
	// See worker.proto for details.
	CreateWorkerSession(ctx context.Context, in *CreateWorkerSessionRequest, opts ...grpc.CallOption) (*CreateWorkerSessionResponse, error)
	// See worker.proto for details.
	DeleteWorkerSession(ctx context.Context, in *DeleteWorkerSessionRequest, opts ...grpc.CallOption) (*DeleteWorkerSessionResponse, error)
	// See worker.proto for details.
	RegisterGraph(ctx context.Context, in *RegisterGraphRequest, opts ...grpc.CallOption) (*RegisterGraphResponse, error)
	// See worker.proto for details.
	DeregisterGraph(ctx context.Context, in *DeregisterGraphRequest, opts ...grpc.CallOption) (*DeregisterGraphResponse, error)
	// See worker.proto for details.
	RunGraph(ctx context.Context, in *RunGraphRequest, opts ...grpc.CallOption) (*RunGraphResponse, error)
	// See worker.proto for details.
	CleanupGraph(ctx context.Context, in *CleanupGraphRequest, opts ...grpc.CallOption) (*CleanupGraphResponse, error)
	// See worker.proto for details.
	CleanupAll(ctx context.Context, in *CleanupAllRequest, opts ...grpc.CallOption) (*CleanupAllResponse, error)
	// See worker.proto for details.
	RecvTensor(ctx context.Context, in *RecvTensorRequest, opts ...grpc.CallOption) (*RecvTensorResponse, error)
	// See worker.proto for details.
	Logging(ctx context.Context, in *LoggingRequest, opts ...grpc.CallOption) (*LoggingResponse, error)
	// See worker.proto for details.
	Tracing(ctx context.Context, in *TracingRequest, opts ...grpc.CallOption) (*TracingResponse, error)
	// See worker.proto for details.
	RecvBuf(ctx context.Context, in *RecvBufRequest, opts ...grpc.CallOption) (*RecvBufResponse, error)
	// See worker.proto for details.
	GetStepSequence(ctx context.Context, in *GetStepSequenceRequest, opts ...grpc.CallOption) (*GetStepSequenceResponse, error)
	// See worker.proto for details.
	CompleteGroup(ctx context.Context, in *CompleteGroupRequest, opts ...grpc.CallOption) (*CompleteGroupResponse, error)
	// See worker.proto for details.
	CompleteInstance(ctx context.Context, in *CompleteInstanceRequest, opts ...grpc.CallOption) (*CompleteInstanceResponse, error)
}

type workerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWorkerServiceClient(cc grpc.ClientConnInterface) WorkerServiceClient {
	return &workerServiceClient{cc}
}

func (c *workerServiceClient) GetStatus(ctx context.Context, in *GetStatusRequest, opts ...grpc.CallOption) (*GetStatusResponse, error) {
	out := new(GetStatusResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.WorkerService/GetStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workerServiceClient) CreateWorkerSession(ctx context.Context, in *CreateWorkerSessionRequest, opts ...grpc.CallOption) (*CreateWorkerSessionResponse, error) {
	out := new(CreateWorkerSessionResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.WorkerService/CreateWorkerSession", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workerServiceClient) DeleteWorkerSession(ctx context.Context, in *DeleteWorkerSessionRequest, opts ...grpc.CallOption) (*DeleteWorkerSessionResponse, error) {
	out := new(DeleteWorkerSessionResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.WorkerService/DeleteWorkerSession", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workerServiceClient) RegisterGraph(ctx context.Context, in *RegisterGraphRequest, opts ...grpc.CallOption) (*RegisterGraphResponse, error) {
	out := new(RegisterGraphResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.WorkerService/RegisterGraph", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workerServiceClient) DeregisterGraph(ctx context.Context, in *DeregisterGraphRequest, opts ...grpc.CallOption) (*DeregisterGraphResponse, error) {
	out := new(DeregisterGraphResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.WorkerService/DeregisterGraph", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workerServiceClient) RunGraph(ctx context.Context, in *RunGraphRequest, opts ...grpc.CallOption) (*RunGraphResponse, error) {
	out := new(RunGraphResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.WorkerService/RunGraph", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workerServiceClient) CleanupGraph(ctx context.Context, in *CleanupGraphRequest, opts ...grpc.CallOption) (*CleanupGraphResponse, error) {
	out := new(CleanupGraphResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.WorkerService/CleanupGraph", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workerServiceClient) CleanupAll(ctx context.Context, in *CleanupAllRequest, opts ...grpc.CallOption) (*CleanupAllResponse, error) {
	out := new(CleanupAllResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.WorkerService/CleanupAll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workerServiceClient) RecvTensor(ctx context.Context, in *RecvTensorRequest, opts ...grpc.CallOption) (*RecvTensorResponse, error) {
	out := new(RecvTensorResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.WorkerService/RecvTensor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workerServiceClient) Logging(ctx context.Context, in *LoggingRequest, opts ...grpc.CallOption) (*LoggingResponse, error) {
	out := new(LoggingResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.WorkerService/Logging", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workerServiceClient) Tracing(ctx context.Context, in *TracingRequest, opts ...grpc.CallOption) (*TracingResponse, error) {
	out := new(TracingResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.WorkerService/Tracing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workerServiceClient) RecvBuf(ctx context.Context, in *RecvBufRequest, opts ...grpc.CallOption) (*RecvBufResponse, error) {
	out := new(RecvBufResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.WorkerService/RecvBuf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workerServiceClient) GetStepSequence(ctx context.Context, in *GetStepSequenceRequest, opts ...grpc.CallOption) (*GetStepSequenceResponse, error) {
	out := new(GetStepSequenceResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.WorkerService/GetStepSequence", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workerServiceClient) CompleteGroup(ctx context.Context, in *CompleteGroupRequest, opts ...grpc.CallOption) (*CompleteGroupResponse, error) {
	out := new(CompleteGroupResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.WorkerService/CompleteGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workerServiceClient) CompleteInstance(ctx context.Context, in *CompleteInstanceRequest, opts ...grpc.CallOption) (*CompleteInstanceResponse, error) {
	out := new(CompleteInstanceResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.WorkerService/CompleteInstance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WorkerServiceServer is the server API for WorkerService service.
type WorkerServiceServer interface {
	// See worker.proto for details.
	GetStatus(context.Context, *GetStatusRequest) (*GetStatusResponse, error)
	// See worker.proto for details.
	CreateWorkerSession(context.Context, *CreateWorkerSessionRequest) (*CreateWorkerSessionResponse, error)
	// See worker.proto for details.
	DeleteWorkerSession(context.Context, *DeleteWorkerSessionRequest) (*DeleteWorkerSessionResponse, error)
	// See worker.proto for details.
	RegisterGraph(context.Context, *RegisterGraphRequest) (*RegisterGraphResponse, error)
	// See worker.proto for details.
	DeregisterGraph(context.Context, *DeregisterGraphRequest) (*DeregisterGraphResponse, error)
	// See worker.proto for details.
	RunGraph(context.Context, *RunGraphRequest) (*RunGraphResponse, error)
	// See worker.proto for details.
	CleanupGraph(context.Context, *CleanupGraphRequest) (*CleanupGraphResponse, error)
	// See worker.proto for details.
	CleanupAll(context.Context, *CleanupAllRequest) (*CleanupAllResponse, error)
	// See worker.proto for details.
	RecvTensor(context.Context, *RecvTensorRequest) (*RecvTensorResponse, error)
	// See worker.proto for details.
	Logging(context.Context, *LoggingRequest) (*LoggingResponse, error)
	// See worker.proto for details.
	Tracing(context.Context, *TracingRequest) (*TracingResponse, error)
	// See worker.proto for details.
	RecvBuf(context.Context, *RecvBufRequest) (*RecvBufResponse, error)
	// See worker.proto for details.
	GetStepSequence(context.Context, *GetStepSequenceRequest) (*GetStepSequenceResponse, error)
	// See worker.proto for details.
	CompleteGroup(context.Context, *CompleteGroupRequest) (*CompleteGroupResponse, error)
	// See worker.proto for details.
	CompleteInstance(context.Context, *CompleteInstanceRequest) (*CompleteInstanceResponse, error)
}

// UnimplementedWorkerServiceServer can be embedded to have forward compatible implementations.
type UnimplementedWorkerServiceServer struct {
}

func (*UnimplementedWorkerServiceServer) GetStatus(context.Context, *GetStatusRequest) (*GetStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStatus not implemented")
}
func (*UnimplementedWorkerServiceServer) CreateWorkerSession(context.Context, *CreateWorkerSessionRequest) (*CreateWorkerSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWorkerSession not implemented")
}
func (*UnimplementedWorkerServiceServer) DeleteWorkerSession(context.Context, *DeleteWorkerSessionRequest) (*DeleteWorkerSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWorkerSession not implemented")
}
func (*UnimplementedWorkerServiceServer) RegisterGraph(context.Context, *RegisterGraphRequest) (*RegisterGraphResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterGraph not implemented")
}
func (*UnimplementedWorkerServiceServer) DeregisterGraph(context.Context, *DeregisterGraphRequest) (*DeregisterGraphResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeregisterGraph not implemented")
}
func (*UnimplementedWorkerServiceServer) RunGraph(context.Context, *RunGraphRequest) (*RunGraphResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunGraph not implemented")
}
func (*UnimplementedWorkerServiceServer) CleanupGraph(context.Context, *CleanupGraphRequest) (*CleanupGraphResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CleanupGraph not implemented")
}
func (*UnimplementedWorkerServiceServer) CleanupAll(context.Context, *CleanupAllRequest) (*CleanupAllResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CleanupAll not implemented")
}
func (*UnimplementedWorkerServiceServer) RecvTensor(context.Context, *RecvTensorRequest) (*RecvTensorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecvTensor not implemented")
}
func (*UnimplementedWorkerServiceServer) Logging(context.Context, *LoggingRequest) (*LoggingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Logging not implemented")
}
func (*UnimplementedWorkerServiceServer) Tracing(context.Context, *TracingRequest) (*TracingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Tracing not implemented")
}
func (*UnimplementedWorkerServiceServer) RecvBuf(context.Context, *RecvBufRequest) (*RecvBufResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecvBuf not implemented")
}
func (*UnimplementedWorkerServiceServer) GetStepSequence(context.Context, *GetStepSequenceRequest) (*GetStepSequenceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStepSequence not implemented")
}
func (*UnimplementedWorkerServiceServer) CompleteGroup(context.Context, *CompleteGroupRequest) (*CompleteGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompleteGroup not implemented")
}
func (*UnimplementedWorkerServiceServer) CompleteInstance(context.Context, *CompleteInstanceRequest) (*CompleteInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompleteInstance not implemented")
}

func RegisterWorkerServiceServer(s *grpc.Server, srv WorkerServiceServer) {
	s.RegisterService(&_WorkerService_serviceDesc, srv)
}

func _WorkerService_GetStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkerServiceServer).GetStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.WorkerService/GetStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkerServiceServer).GetStatus(ctx, req.(*GetStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkerService_CreateWorkerSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkerSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkerServiceServer).CreateWorkerSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.WorkerService/CreateWorkerSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkerServiceServer).CreateWorkerSession(ctx, req.(*CreateWorkerSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkerService_DeleteWorkerSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteWorkerSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkerServiceServer).DeleteWorkerSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.WorkerService/DeleteWorkerSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkerServiceServer).DeleteWorkerSession(ctx, req.(*DeleteWorkerSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkerService_RegisterGraph_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterGraphRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkerServiceServer).RegisterGraph(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.WorkerService/RegisterGraph",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkerServiceServer).RegisterGraph(ctx, req.(*RegisterGraphRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkerService_DeregisterGraph_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeregisterGraphRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkerServiceServer).DeregisterGraph(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.WorkerService/DeregisterGraph",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkerServiceServer).DeregisterGraph(ctx, req.(*DeregisterGraphRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkerService_RunGraph_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunGraphRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkerServiceServer).RunGraph(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.WorkerService/RunGraph",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkerServiceServer).RunGraph(ctx, req.(*RunGraphRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkerService_CleanupGraph_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanupGraphRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkerServiceServer).CleanupGraph(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.WorkerService/CleanupGraph",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkerServiceServer).CleanupGraph(ctx, req.(*CleanupGraphRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkerService_CleanupAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanupAllRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkerServiceServer).CleanupAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.WorkerService/CleanupAll",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkerServiceServer).CleanupAll(ctx, req.(*CleanupAllRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkerService_RecvTensor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecvTensorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkerServiceServer).RecvTensor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.WorkerService/RecvTensor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkerServiceServer).RecvTensor(ctx, req.(*RecvTensorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkerService_Logging_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoggingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkerServiceServer).Logging(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.WorkerService/Logging",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkerServiceServer).Logging(ctx, req.(*LoggingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkerService_Tracing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TracingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkerServiceServer).Tracing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.WorkerService/Tracing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkerServiceServer).Tracing(ctx, req.(*TracingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkerService_RecvBuf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecvBufRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkerServiceServer).RecvBuf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.WorkerService/RecvBuf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkerServiceServer).RecvBuf(ctx, req.(*RecvBufRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkerService_GetStepSequence_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStepSequenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkerServiceServer).GetStepSequence(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.WorkerService/GetStepSequence",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkerServiceServer).GetStepSequence(ctx, req.(*GetStepSequenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkerService_CompleteGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompleteGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkerServiceServer).CompleteGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.WorkerService/CompleteGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkerServiceServer).CompleteGroup(ctx, req.(*CompleteGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkerService_CompleteInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompleteInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkerServiceServer).CompleteInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.WorkerService/CompleteInstance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkerServiceServer).CompleteInstance(ctx, req.(*CompleteInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _WorkerService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "tensorflow.grpc.WorkerService",
	HandlerType: (*WorkerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetStatus",
			Handler:    _WorkerService_GetStatus_Handler,
		},
		{
			MethodName: "CreateWorkerSession",
			Handler:    _WorkerService_CreateWorkerSession_Handler,
		},
		{
			MethodName: "DeleteWorkerSession",
			Handler:    _WorkerService_DeleteWorkerSession_Handler,
		},
		{
			MethodName: "RegisterGraph",
			Handler:    _WorkerService_RegisterGraph_Handler,
		},
		{
			MethodName: "DeregisterGraph",
			Handler:    _WorkerService_DeregisterGraph_Handler,
		},
		{
			MethodName: "RunGraph",
			Handler:    _WorkerService_RunGraph_Handler,
		},
		{
			MethodName: "CleanupGraph",
			Handler:    _WorkerService_CleanupGraph_Handler,
		},
		{
			MethodName: "CleanupAll",
			Handler:    _WorkerService_CleanupAll_Handler,
		},
		{
			MethodName: "RecvTensor",
			Handler:    _WorkerService_RecvTensor_Handler,
		},
		{
			MethodName: "Logging",
			Handler:    _WorkerService_Logging_Handler,
		},
		{
			MethodName: "Tracing",
			Handler:    _WorkerService_Tracing_Handler,
		},
		{
			MethodName: "RecvBuf",
			Handler:    _WorkerService_RecvBuf_Handler,
		},
		{
			MethodName: "GetStepSequence",
			Handler:    _WorkerService_GetStepSequence_Handler,
		},
		{
			MethodName: "CompleteGroup",
			Handler:    _WorkerService_CompleteGroup_Handler,
		},
		{
			MethodName: "CompleteInstance",
			Handler:    _WorkerService_CompleteInstance_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tensorflow/core/protobuf/worker_service.proto",
}
