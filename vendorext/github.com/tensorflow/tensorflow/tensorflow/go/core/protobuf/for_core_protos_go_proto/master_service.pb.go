// Copyright 2016 The TensorFlow Authors. All Rights Reserved.
//
//Licensed under the Apache License, Version 2.0 (the "License");
//you may not use this file except in compliance with the License.
//You may obtain a copy of the License at
//
//http://www.apache.org/licenses/LICENSE-2.0
//
//Unless required by applicable law or agreed to in writing, software
//distributed under the License is distributed on an "AS IS" BASIS,
//WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//See the License for the specific language governing permissions and
//limitations under the License.
//==============================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: tensorflow/core/protobuf/master_service.proto

package for_core_protos_go_proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_tensorflow_core_protobuf_master_service_proto protoreflect.FileDescriptor

var file_tensorflow_core_protobuf_master_service_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x6d, 0x61, 0x73, 0x74, 0x65,
	0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x67, 0x72, 0x70, 0x63,
	0x1a, 0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x6d, 0x61, 0x73, 0x74, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xbb, 0x06, 0x0a, 0x0d, 0x4d, 0x61, 0x73, 0x74,
	0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x54, 0x0a, 0x0d, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x54, 0x0a, 0x0d, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x20, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c,
	0x52, 0x75, 0x6e, 0x53, 0x65, 0x74, 0x75, 0x70, 0x12, 0x22, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x75, 0x6e,
	0x53, 0x65, 0x74, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61,
	0x6c, 0x52, 0x75, 0x6e, 0x53, 0x65, 0x74, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x42, 0x0a, 0x07, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x12, 0x1a, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x0c, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x1e, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x05, 0x52, 0x65, 0x73, 0x65,
	0x74, 0x12, 0x18, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52,
	0x65, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x0c, 0x4d, 0x61, 0x6b, 0x65, 0x43, 0x61,
	0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1f, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x0b, 0x52, 0x75, 0x6e,
	0x43, 0x61, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1e, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x75, 0x6e, 0x43, 0x61, 0x6c, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x75, 0x6e, 0x43, 0x61, 0x6c, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x0f, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x22, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x43, 0x61, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x23, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8a, 0x01, 0x0a, 0x1a, 0x6f, 0x72, 0x67, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x64, 0x69, 0x73, 0x74, 0x72, 0x75, 0x6e,
	0x74, 0x69, 0x6d, 0x65, 0x42, 0x13, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x50, 0x01, 0x5a, 0x55, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x67, 0x6f, 0x2f, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x6f, 0x72, 0x5f, 0x63, 0x6f,
	0x72, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x5f, 0x67, 0x6f, 0x5f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_tensorflow_core_protobuf_master_service_proto_goTypes = []interface{}{
	(*CreateSessionRequest)(nil),    // 0: tensorflow.CreateSessionRequest
	(*ExtendSessionRequest)(nil),    // 1: tensorflow.ExtendSessionRequest
	(*PartialRunSetupRequest)(nil),  // 2: tensorflow.PartialRunSetupRequest
	(*RunStepRequest)(nil),          // 3: tensorflow.RunStepRequest
	(*CloseSessionRequest)(nil),     // 4: tensorflow.CloseSessionRequest
	(*ListDevicesRequest)(nil),      // 5: tensorflow.ListDevicesRequest
	(*ResetRequest)(nil),            // 6: tensorflow.ResetRequest
	(*MakeCallableRequest)(nil),     // 7: tensorflow.MakeCallableRequest
	(*RunCallableRequest)(nil),      // 8: tensorflow.RunCallableRequest
	(*ReleaseCallableRequest)(nil),  // 9: tensorflow.ReleaseCallableRequest
	(*CreateSessionResponse)(nil),   // 10: tensorflow.CreateSessionResponse
	(*ExtendSessionResponse)(nil),   // 11: tensorflow.ExtendSessionResponse
	(*PartialRunSetupResponse)(nil), // 12: tensorflow.PartialRunSetupResponse
	(*RunStepResponse)(nil),         // 13: tensorflow.RunStepResponse
	(*CloseSessionResponse)(nil),    // 14: tensorflow.CloseSessionResponse
	(*ListDevicesResponse)(nil),     // 15: tensorflow.ListDevicesResponse
	(*ResetResponse)(nil),           // 16: tensorflow.ResetResponse
	(*MakeCallableResponse)(nil),    // 17: tensorflow.MakeCallableResponse
	(*RunCallableResponse)(nil),     // 18: tensorflow.RunCallableResponse
	(*ReleaseCallableResponse)(nil), // 19: tensorflow.ReleaseCallableResponse
}
var file_tensorflow_core_protobuf_master_service_proto_depIdxs = []int32{
	0,  // 0: tensorflow.grpc.MasterService.CreateSession:input_type -> tensorflow.CreateSessionRequest
	1,  // 1: tensorflow.grpc.MasterService.ExtendSession:input_type -> tensorflow.ExtendSessionRequest
	2,  // 2: tensorflow.grpc.MasterService.PartialRunSetup:input_type -> tensorflow.PartialRunSetupRequest
	3,  // 3: tensorflow.grpc.MasterService.RunStep:input_type -> tensorflow.RunStepRequest
	4,  // 4: tensorflow.grpc.MasterService.CloseSession:input_type -> tensorflow.CloseSessionRequest
	5,  // 5: tensorflow.grpc.MasterService.ListDevices:input_type -> tensorflow.ListDevicesRequest
	6,  // 6: tensorflow.grpc.MasterService.Reset:input_type -> tensorflow.ResetRequest
	7,  // 7: tensorflow.grpc.MasterService.MakeCallable:input_type -> tensorflow.MakeCallableRequest
	8,  // 8: tensorflow.grpc.MasterService.RunCallable:input_type -> tensorflow.RunCallableRequest
	9,  // 9: tensorflow.grpc.MasterService.ReleaseCallable:input_type -> tensorflow.ReleaseCallableRequest
	10, // 10: tensorflow.grpc.MasterService.CreateSession:output_type -> tensorflow.CreateSessionResponse
	11, // 11: tensorflow.grpc.MasterService.ExtendSession:output_type -> tensorflow.ExtendSessionResponse
	12, // 12: tensorflow.grpc.MasterService.PartialRunSetup:output_type -> tensorflow.PartialRunSetupResponse
	13, // 13: tensorflow.grpc.MasterService.RunStep:output_type -> tensorflow.RunStepResponse
	14, // 14: tensorflow.grpc.MasterService.CloseSession:output_type -> tensorflow.CloseSessionResponse
	15, // 15: tensorflow.grpc.MasterService.ListDevices:output_type -> tensorflow.ListDevicesResponse
	16, // 16: tensorflow.grpc.MasterService.Reset:output_type -> tensorflow.ResetResponse
	17, // 17: tensorflow.grpc.MasterService.MakeCallable:output_type -> tensorflow.MakeCallableResponse
	18, // 18: tensorflow.grpc.MasterService.RunCallable:output_type -> tensorflow.RunCallableResponse
	19, // 19: tensorflow.grpc.MasterService.ReleaseCallable:output_type -> tensorflow.ReleaseCallableResponse
	10, // [10:20] is the sub-list for method output_type
	0,  // [0:10] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_tensorflow_core_protobuf_master_service_proto_init() }
func file_tensorflow_core_protobuf_master_service_proto_init() {
	if File_tensorflow_core_protobuf_master_service_proto != nil {
		return
	}
	file_tensorflow_core_protobuf_master_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tensorflow_core_protobuf_master_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tensorflow_core_protobuf_master_service_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_protobuf_master_service_proto_depIdxs,
	}.Build()
	File_tensorflow_core_protobuf_master_service_proto = out.File
	file_tensorflow_core_protobuf_master_service_proto_rawDesc = nil
	file_tensorflow_core_protobuf_master_service_proto_goTypes = nil
	file_tensorflow_core_protobuf_master_service_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// MasterServiceClient is the client API for MasterService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MasterServiceClient interface {
	// Creates a session.
	CreateSession(ctx context.Context, in *CreateSessionRequest, opts ...grpc.CallOption) (*CreateSessionResponse, error)
	// Extends a session.
	ExtendSession(ctx context.Context, in *ExtendSessionRequest, opts ...grpc.CallOption) (*ExtendSessionResponse, error)
	// Prepares future partial run calls.
	PartialRunSetup(ctx context.Context, in *PartialRunSetupRequest, opts ...grpc.CallOption) (*PartialRunSetupResponse, error)
	// Drives the graph computation.
	RunStep(ctx context.Context, in *RunStepRequest, opts ...grpc.CallOption) (*RunStepResponse, error)
	// Closes a session.
	CloseSession(ctx context.Context, in *CloseSessionRequest, opts ...grpc.CallOption) (*CloseSessionResponse, error)
	// List the devices usable by the master.
	ListDevices(ctx context.Context, in *ListDevicesRequest, opts ...grpc.CallOption) (*ListDevicesResponse, error)
	// Close and abandon all existing sessions.  Ongoing computations
	// will no longer affect fresh ones via the resources in containers listed in
	// the ResetRequest.  See ResetRequest for more details.
	Reset(ctx context.Context, in *ResetRequest, opts ...grpc.CallOption) (*ResetResponse, error)
	// Registers a callable for execution with RunCallable.
	MakeCallable(ctx context.Context, in *MakeCallableRequest, opts ...grpc.CallOption) (*MakeCallableResponse, error)
	// Executes a callable registered with MakeCallable.
	RunCallable(ctx context.Context, in *RunCallableRequest, opts ...grpc.CallOption) (*RunCallableResponse, error)
	// Frees resources associated with a callable registered with MakeCallable.
	ReleaseCallable(ctx context.Context, in *ReleaseCallableRequest, opts ...grpc.CallOption) (*ReleaseCallableResponse, error)
}

type masterServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMasterServiceClient(cc grpc.ClientConnInterface) MasterServiceClient {
	return &masterServiceClient{cc}
}

func (c *masterServiceClient) CreateSession(ctx context.Context, in *CreateSessionRequest, opts ...grpc.CallOption) (*CreateSessionResponse, error) {
	out := new(CreateSessionResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.MasterService/CreateSession", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) ExtendSession(ctx context.Context, in *ExtendSessionRequest, opts ...grpc.CallOption) (*ExtendSessionResponse, error) {
	out := new(ExtendSessionResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.MasterService/ExtendSession", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) PartialRunSetup(ctx context.Context, in *PartialRunSetupRequest, opts ...grpc.CallOption) (*PartialRunSetupResponse, error) {
	out := new(PartialRunSetupResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.MasterService/PartialRunSetup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) RunStep(ctx context.Context, in *RunStepRequest, opts ...grpc.CallOption) (*RunStepResponse, error) {
	out := new(RunStepResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.MasterService/RunStep", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) CloseSession(ctx context.Context, in *CloseSessionRequest, opts ...grpc.CallOption) (*CloseSessionResponse, error) {
	out := new(CloseSessionResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.MasterService/CloseSession", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) ListDevices(ctx context.Context, in *ListDevicesRequest, opts ...grpc.CallOption) (*ListDevicesResponse, error) {
	out := new(ListDevicesResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.MasterService/ListDevices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) Reset(ctx context.Context, in *ResetRequest, opts ...grpc.CallOption) (*ResetResponse, error) {
	out := new(ResetResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.MasterService/Reset", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) MakeCallable(ctx context.Context, in *MakeCallableRequest, opts ...grpc.CallOption) (*MakeCallableResponse, error) {
	out := new(MakeCallableResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.MasterService/MakeCallable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) RunCallable(ctx context.Context, in *RunCallableRequest, opts ...grpc.CallOption) (*RunCallableResponse, error) {
	out := new(RunCallableResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.MasterService/RunCallable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterServiceClient) ReleaseCallable(ctx context.Context, in *ReleaseCallableRequest, opts ...grpc.CallOption) (*ReleaseCallableResponse, error) {
	out := new(ReleaseCallableResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.grpc.MasterService/ReleaseCallable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MasterServiceServer is the server API for MasterService service.
type MasterServiceServer interface {
	// Creates a session.
	CreateSession(context.Context, *CreateSessionRequest) (*CreateSessionResponse, error)
	// Extends a session.
	ExtendSession(context.Context, *ExtendSessionRequest) (*ExtendSessionResponse, error)
	// Prepares future partial run calls.
	PartialRunSetup(context.Context, *PartialRunSetupRequest) (*PartialRunSetupResponse, error)
	// Drives the graph computation.
	RunStep(context.Context, *RunStepRequest) (*RunStepResponse, error)
	// Closes a session.
	CloseSession(context.Context, *CloseSessionRequest) (*CloseSessionResponse, error)
	// List the devices usable by the master.
	ListDevices(context.Context, *ListDevicesRequest) (*ListDevicesResponse, error)
	// Close and abandon all existing sessions.  Ongoing computations
	// will no longer affect fresh ones via the resources in containers listed in
	// the ResetRequest.  See ResetRequest for more details.
	Reset(context.Context, *ResetRequest) (*ResetResponse, error)
	// Registers a callable for execution with RunCallable.
	MakeCallable(context.Context, *MakeCallableRequest) (*MakeCallableResponse, error)
	// Executes a callable registered with MakeCallable.
	RunCallable(context.Context, *RunCallableRequest) (*RunCallableResponse, error)
	// Frees resources associated with a callable registered with MakeCallable.
	ReleaseCallable(context.Context, *ReleaseCallableRequest) (*ReleaseCallableResponse, error)
}

// UnimplementedMasterServiceServer can be embedded to have forward compatible implementations.
type UnimplementedMasterServiceServer struct {
}

func (*UnimplementedMasterServiceServer) CreateSession(context.Context, *CreateSessionRequest) (*CreateSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSession not implemented")
}
func (*UnimplementedMasterServiceServer) ExtendSession(context.Context, *ExtendSessionRequest) (*ExtendSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtendSession not implemented")
}
func (*UnimplementedMasterServiceServer) PartialRunSetup(context.Context, *PartialRunSetupRequest) (*PartialRunSetupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialRunSetup not implemented")
}
func (*UnimplementedMasterServiceServer) RunStep(context.Context, *RunStepRequest) (*RunStepResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunStep not implemented")
}
func (*UnimplementedMasterServiceServer) CloseSession(context.Context, *CloseSessionRequest) (*CloseSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloseSession not implemented")
}
func (*UnimplementedMasterServiceServer) ListDevices(context.Context, *ListDevicesRequest) (*ListDevicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDevices not implemented")
}
func (*UnimplementedMasterServiceServer) Reset(context.Context, *ResetRequest) (*ResetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Reset not implemented")
}
func (*UnimplementedMasterServiceServer) MakeCallable(context.Context, *MakeCallableRequest) (*MakeCallableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MakeCallable not implemented")
}
func (*UnimplementedMasterServiceServer) RunCallable(context.Context, *RunCallableRequest) (*RunCallableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunCallable not implemented")
}
func (*UnimplementedMasterServiceServer) ReleaseCallable(context.Context, *ReleaseCallableRequest) (*ReleaseCallableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseCallable not implemented")
}

func RegisterMasterServiceServer(s *grpc.Server, srv MasterServiceServer) {
	s.RegisterService(&_MasterService_serviceDesc, srv)
}

func _MasterService_CreateSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).CreateSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.MasterService/CreateSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).CreateSession(ctx, req.(*CreateSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_ExtendSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtendSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).ExtendSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.MasterService/ExtendSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).ExtendSession(ctx, req.(*ExtendSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_PartialRunSetup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PartialRunSetupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).PartialRunSetup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.MasterService/PartialRunSetup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).PartialRunSetup(ctx, req.(*PartialRunSetupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_RunStep_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunStepRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).RunStep(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.MasterService/RunStep",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).RunStep(ctx, req.(*RunStepRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_CloseSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).CloseSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.MasterService/CloseSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).CloseSession(ctx, req.(*CloseSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_ListDevices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDevicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).ListDevices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.MasterService/ListDevices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).ListDevices(ctx, req.(*ListDevicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_Reset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).Reset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.MasterService/Reset",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).Reset(ctx, req.(*ResetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_MakeCallable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MakeCallableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).MakeCallable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.MasterService/MakeCallable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).MakeCallable(ctx, req.(*MakeCallableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_RunCallable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunCallableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).RunCallable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.MasterService/RunCallable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).RunCallable(ctx, req.(*RunCallableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterService_ReleaseCallable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseCallableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterServiceServer).ReleaseCallable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.grpc.MasterService/ReleaseCallable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterServiceServer).ReleaseCallable(ctx, req.(*ReleaseCallableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MasterService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "tensorflow.grpc.MasterService",
	HandlerType: (*MasterServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSession",
			Handler:    _MasterService_CreateSession_Handler,
		},
		{
			MethodName: "ExtendSession",
			Handler:    _MasterService_ExtendSession_Handler,
		},
		{
			MethodName: "PartialRunSetup",
			Handler:    _MasterService_PartialRunSetup_Handler,
		},
		{
			MethodName: "RunStep",
			Handler:    _MasterService_RunStep_Handler,
		},
		{
			MethodName: "CloseSession",
			Handler:    _MasterService_CloseSession_Handler,
		},
		{
			MethodName: "ListDevices",
			Handler:    _MasterService_ListDevices_Handler,
		},
		{
			MethodName: "Reset",
			Handler:    _MasterService_Reset_Handler,
		},
		{
			MethodName: "MakeCallable",
			Handler:    _MasterService_MakeCallable_Handler,
		},
		{
			MethodName: "RunCallable",
			Handler:    _MasterService_RunCallable_Handler,
		},
		{
			MethodName: "ReleaseCallable",
			Handler:    _MasterService_ReleaseCallable_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tensorflow/core/protobuf/master_service.proto",
}
