// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: tensorflow/core/protobuf/saved_object_graph.proto

package for_core_protos_go_proto

import (
	tensor_shape_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto"
	types_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto"
	variable_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/variable_go_proto"
	versions_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/versions_go_proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Whether the function should be compiled by XLA.
//
// The public interface to `tf.function` uses an optional boolean to
// represent three distinct states for this field.  Unfortunately, proto3
// removes the ability to explicitly check for the presence or absence of a
// field, so we instead map to an enum.
//
// See `tf.function` for details.
type FunctionSpec_JitCompile int32

const (
	FunctionSpec_DEFAULT FunctionSpec_JitCompile = 0
	FunctionSpec_ON      FunctionSpec_JitCompile = 1
	FunctionSpec_OFF     FunctionSpec_JitCompile = 2
)

// Enum value maps for FunctionSpec_JitCompile.
var (
	FunctionSpec_JitCompile_name = map[int32]string{
		0: "DEFAULT",
		1: "ON",
		2: "OFF",
	}
	FunctionSpec_JitCompile_value = map[string]int32{
		"DEFAULT": 0,
		"ON":      1,
		"OFF":     2,
	}
)

func (x FunctionSpec_JitCompile) Enum() *FunctionSpec_JitCompile {
	p := new(FunctionSpec_JitCompile)
	*p = x
	return p
}

func (x FunctionSpec_JitCompile) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FunctionSpec_JitCompile) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_core_protobuf_saved_object_graph_proto_enumTypes[0].Descriptor()
}

func (FunctionSpec_JitCompile) Type() protoreflect.EnumType {
	return &file_tensorflow_core_protobuf_saved_object_graph_proto_enumTypes[0]
}

func (x FunctionSpec_JitCompile) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FunctionSpec_JitCompile.Descriptor instead.
func (FunctionSpec_JitCompile) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescGZIP(), []int{10, 0}
}

type SavedObjectGraph struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Flattened list of objects in the object graph.
	//
	// The position of the object in this list indicates its id.
	// Nodes[0] is considered the root node.
	Nodes []*SavedObject `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
	// Information about captures and output structures in concrete functions.
	// Referenced from SavedBareConcreteFunction and SavedFunction.
	ConcreteFunctions map[string]*SavedConcreteFunction `protobuf:"bytes,2,rep,name=concrete_functions,json=concreteFunctions,proto3" json:"concrete_functions,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SavedObjectGraph) Reset() {
	*x = SavedObjectGraph{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavedObjectGraph) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavedObjectGraph) ProtoMessage() {}

func (x *SavedObjectGraph) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavedObjectGraph.ProtoReflect.Descriptor instead.
func (*SavedObjectGraph) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescGZIP(), []int{0}
}

func (x *SavedObjectGraph) GetNodes() []*SavedObject {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *SavedObjectGraph) GetConcreteFunctions() map[string]*SavedConcreteFunction {
	if x != nil {
		return x.ConcreteFunctions
	}
	return nil
}

type SavedObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Objects which this object depends on: named edges in the dependency
	// graph.
	//
	// Note: currently only valid if kind == "user_object" or "resource".
	Children []*TrackableObjectGraph_TrackableObject_ObjectReference `protobuf:"bytes,1,rep,name=children,proto3" json:"children,omitempty"`
	// Ordered list of dependencies that must be loaded before this object.
	// SavedModel loads with the bottom-up approach, by first creating all objects
	// (in the order defined by the dependencies), then connecting the edges.
	Dependencies []*TrackableObjectGraph_TrackableObject_ObjectReference `protobuf:"bytes,15,rep,name=dependencies,proto3" json:"dependencies,omitempty"`
	// Slot variables owned by this object. This describes the three-way
	// (optimizer, variable, slot variable) relationship; none of the three
	// depend on the others directly.
	//
	// Note: currently only valid if kind == "user_object".
	SlotVariables []*TrackableObjectGraph_TrackableObject_SlotVariableReference `protobuf:"bytes,3,rep,name=slot_variables,json=slotVariables,proto3" json:"slot_variables,omitempty"`
	// Types that are assignable to Kind:
	//
	//	*SavedObject_UserObject
	//	*SavedObject_Asset
	//	*SavedObject_Function
	//	*SavedObject_Variable
	//	*SavedObject_BareConcreteFunction
	//	*SavedObject_Constant
	//	*SavedObject_Resource
	//	*SavedObject_CapturedTensor
	Kind isSavedObject_Kind `protobuf_oneof:"kind"`
	// Stores the functions used to save and restore this object. At most one of
	// `saveable_objects` or `registered_saver` is defined for each SavedObject.
	// See the comment below for the difference between SaveableObject and
	// registered savers.
	SaveableObjects map[string]*SaveableObject `protobuf:"bytes,11,rep,name=saveable_objects,json=saveableObjects,proto3" json:"saveable_objects,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// The name of the registered class of the form "{package}.{class_name}".
	// This field is used to search for the registered class at loading time.
	RegisteredName string `protobuf:"bytes,13,opt,name=registered_name,json=registeredName,proto3" json:"registered_name,omitempty"`
	// The user-generated proto storing metadata for this object, to be passed to
	// the registered classes's _deserialize_from_proto method when this object is
	// loaded from the SavedModel.
	SerializedUserProto *anypb.Any `protobuf:"bytes,14,opt,name=serialized_user_proto,json=serializedUserProto,proto3" json:"serialized_user_proto,omitempty"`
	// String name of the registered saver. At most one of `saveable_objects` or
	// `registered_saver` is defined for each SavedObject.
	RegisteredSaver string `protobuf:"bytes,16,opt,name=registered_saver,json=registeredSaver,proto3" json:"registered_saver,omitempty"`
}

func (x *SavedObject) Reset() {
	*x = SavedObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavedObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavedObject) ProtoMessage() {}

func (x *SavedObject) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavedObject.ProtoReflect.Descriptor instead.
func (*SavedObject) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescGZIP(), []int{1}
}

func (x *SavedObject) GetChildren() []*TrackableObjectGraph_TrackableObject_ObjectReference {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *SavedObject) GetDependencies() []*TrackableObjectGraph_TrackableObject_ObjectReference {
	if x != nil {
		return x.Dependencies
	}
	return nil
}

func (x *SavedObject) GetSlotVariables() []*TrackableObjectGraph_TrackableObject_SlotVariableReference {
	if x != nil {
		return x.SlotVariables
	}
	return nil
}

func (m *SavedObject) GetKind() isSavedObject_Kind {
	if m != nil {
		return m.Kind
	}
	return nil
}

func (x *SavedObject) GetUserObject() *SavedUserObject {
	if x, ok := x.GetKind().(*SavedObject_UserObject); ok {
		return x.UserObject
	}
	return nil
}

func (x *SavedObject) GetAsset() *SavedAsset {
	if x, ok := x.GetKind().(*SavedObject_Asset); ok {
		return x.Asset
	}
	return nil
}

func (x *SavedObject) GetFunction() *SavedFunction {
	if x, ok := x.GetKind().(*SavedObject_Function); ok {
		return x.Function
	}
	return nil
}

func (x *SavedObject) GetVariable() *SavedVariable {
	if x, ok := x.GetKind().(*SavedObject_Variable); ok {
		return x.Variable
	}
	return nil
}

func (x *SavedObject) GetBareConcreteFunction() *SavedBareConcreteFunction {
	if x, ok := x.GetKind().(*SavedObject_BareConcreteFunction); ok {
		return x.BareConcreteFunction
	}
	return nil
}

func (x *SavedObject) GetConstant() *SavedConstant {
	if x, ok := x.GetKind().(*SavedObject_Constant); ok {
		return x.Constant
	}
	return nil
}

func (x *SavedObject) GetResource() *SavedResource {
	if x, ok := x.GetKind().(*SavedObject_Resource); ok {
		return x.Resource
	}
	return nil
}

func (x *SavedObject) GetCapturedTensor() *CapturedTensor {
	if x, ok := x.GetKind().(*SavedObject_CapturedTensor); ok {
		return x.CapturedTensor
	}
	return nil
}

func (x *SavedObject) GetSaveableObjects() map[string]*SaveableObject {
	if x != nil {
		return x.SaveableObjects
	}
	return nil
}

func (x *SavedObject) GetRegisteredName() string {
	if x != nil {
		return x.RegisteredName
	}
	return ""
}

func (x *SavedObject) GetSerializedUserProto() *anypb.Any {
	if x != nil {
		return x.SerializedUserProto
	}
	return nil
}

func (x *SavedObject) GetRegisteredSaver() string {
	if x != nil {
		return x.RegisteredSaver
	}
	return ""
}

type isSavedObject_Kind interface {
	isSavedObject_Kind()
}

type SavedObject_UserObject struct {
	UserObject *SavedUserObject `protobuf:"bytes,4,opt,name=user_object,json=userObject,proto3,oneof"`
}

type SavedObject_Asset struct {
	Asset *SavedAsset `protobuf:"bytes,5,opt,name=asset,proto3,oneof"`
}

type SavedObject_Function struct {
	Function *SavedFunction `protobuf:"bytes,6,opt,name=function,proto3,oneof"`
}

type SavedObject_Variable struct {
	Variable *SavedVariable `protobuf:"bytes,7,opt,name=variable,proto3,oneof"`
}

type SavedObject_BareConcreteFunction struct {
	BareConcreteFunction *SavedBareConcreteFunction `protobuf:"bytes,8,opt,name=bare_concrete_function,json=bareConcreteFunction,proto3,oneof"`
}

type SavedObject_Constant struct {
	Constant *SavedConstant `protobuf:"bytes,9,opt,name=constant,proto3,oneof"`
}

type SavedObject_Resource struct {
	Resource *SavedResource `protobuf:"bytes,10,opt,name=resource,proto3,oneof"`
}

type SavedObject_CapturedTensor struct {
	CapturedTensor *CapturedTensor `protobuf:"bytes,12,opt,name=captured_tensor,json=capturedTensor,proto3,oneof"`
}

func (*SavedObject_UserObject) isSavedObject_Kind() {}

func (*SavedObject_Asset) isSavedObject_Kind() {}

func (*SavedObject_Function) isSavedObject_Kind() {}

func (*SavedObject_Variable) isSavedObject_Kind() {}

func (*SavedObject_BareConcreteFunction) isSavedObject_Kind() {}

func (*SavedObject_Constant) isSavedObject_Kind() {}

func (*SavedObject_Resource) isSavedObject_Kind() {}

func (*SavedObject_CapturedTensor) isSavedObject_Kind() {}

// A SavedUserObject is an object (in the object-oriented language of the
// TensorFlow program) of some user- or framework-defined class other than
// those handled specifically by the other kinds of SavedObjects.
//
// This object cannot be evaluated as a tensor, and therefore cannot be bound
// to an input of a function.
type SavedUserObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Corresponds to a registration of the type to use in the loading program.
	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// Version information from the producer of this SavedUserObject.
	Version *versions_go_proto.VersionDef `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// Metadata for deserializing this object.
	//
	// Deprecated! At the time of deprecation, Keras was the only user of this
	// field, and its saving and loading code will be updated shortly.
	// Please save your application-specific metadata to a separate file.
	//
	// Deprecated: Do not use.
	Metadata string `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *SavedUserObject) Reset() {
	*x = SavedUserObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavedUserObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavedUserObject) ProtoMessage() {}

func (x *SavedUserObject) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavedUserObject.ProtoReflect.Descriptor instead.
func (*SavedUserObject) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescGZIP(), []int{2}
}

func (x *SavedUserObject) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *SavedUserObject) GetVersion() *versions_go_proto.VersionDef {
	if x != nil {
		return x.Version
	}
	return nil
}

// Deprecated: Do not use.
func (x *SavedUserObject) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

// A SavedAsset points to an asset in the MetaGraph.
//
// When bound to a function this object evaluates to a tensor with the absolute
// filename. Users should not depend on a particular part of the filename to
// remain stable (e.g. basename could be changed).
type SavedAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Index into `MetaGraphDef.asset_file_def[]` that describes the Asset.
	//
	// Only the field `AssetFileDef.filename` is used. Other fields, such as
	// `AssetFileDef.tensor_info`, MUST be ignored.
	AssetFileDefIndex int32 `protobuf:"varint,1,opt,name=asset_file_def_index,json=assetFileDefIndex,proto3" json:"asset_file_def_index,omitempty"`
}

func (x *SavedAsset) Reset() {
	*x = SavedAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavedAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavedAsset) ProtoMessage() {}

func (x *SavedAsset) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavedAsset.ProtoReflect.Descriptor instead.
func (*SavedAsset) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescGZIP(), []int{3}
}

func (x *SavedAsset) GetAssetFileDefIndex() int32 {
	if x != nil {
		return x.AssetFileDefIndex
	}
	return 0
}

// A function with multiple signatures, possibly with non-Tensor arguments.
type SavedFunction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConcreteFunctions []string      `protobuf:"bytes,1,rep,name=concrete_functions,json=concreteFunctions,proto3" json:"concrete_functions,omitempty"`
	FunctionSpec      *FunctionSpec `protobuf:"bytes,2,opt,name=function_spec,json=functionSpec,proto3" json:"function_spec,omitempty"`
}

func (x *SavedFunction) Reset() {
	*x = SavedFunction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavedFunction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavedFunction) ProtoMessage() {}

func (x *SavedFunction) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavedFunction.ProtoReflect.Descriptor instead.
func (*SavedFunction) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescGZIP(), []int{4}
}

func (x *SavedFunction) GetConcreteFunctions() []string {
	if x != nil {
		return x.ConcreteFunctions
	}
	return nil
}

func (x *SavedFunction) GetFunctionSpec() *FunctionSpec {
	if x != nil {
		return x.FunctionSpec
	}
	return nil
}

type CapturedTensor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name of captured tensor
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Name of concrete function which contains the computed graph tensor.
	ConcreteFunction string `protobuf:"bytes,2,opt,name=concrete_function,json=concreteFunction,proto3" json:"concrete_function,omitempty"`
}

func (x *CapturedTensor) Reset() {
	*x = CapturedTensor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CapturedTensor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CapturedTensor) ProtoMessage() {}

func (x *CapturedTensor) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CapturedTensor.ProtoReflect.Descriptor instead.
func (*CapturedTensor) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescGZIP(), []int{5}
}

func (x *CapturedTensor) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CapturedTensor) GetConcreteFunction() string {
	if x != nil {
		return x.ConcreteFunction
	}
	return ""
}

// Stores low-level information about a concrete function. Referenced in either
// a SavedFunction or a SavedBareConcreteFunction.
type SavedConcreteFunction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BoundInputs []int32 `protobuf:"varint,2,rep,packed,name=bound_inputs,json=boundInputs,proto3" json:"bound_inputs,omitempty"`
	// Input in canonicalized form that was received to create this concrete
	// function.
	CanonicalizedInputSignature *StructuredValue `protobuf:"bytes,3,opt,name=canonicalized_input_signature,json=canonicalizedInputSignature,proto3" json:"canonicalized_input_signature,omitempty"`
	// Output that was the return value of this function after replacing all
	// Tensors with TensorSpecs. This can be an arbitrary nested function and will
	// be used to reconstruct the full structure from pure tensors.
	OutputSignature *StructuredValue `protobuf:"bytes,4,opt,name=output_signature,json=outputSignature,proto3" json:"output_signature,omitempty"`
}

func (x *SavedConcreteFunction) Reset() {
	*x = SavedConcreteFunction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavedConcreteFunction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavedConcreteFunction) ProtoMessage() {}

func (x *SavedConcreteFunction) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavedConcreteFunction.ProtoReflect.Descriptor instead.
func (*SavedConcreteFunction) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescGZIP(), []int{6}
}

func (x *SavedConcreteFunction) GetBoundInputs() []int32 {
	if x != nil {
		return x.BoundInputs
	}
	return nil
}

func (x *SavedConcreteFunction) GetCanonicalizedInputSignature() *StructuredValue {
	if x != nil {
		return x.CanonicalizedInputSignature
	}
	return nil
}

func (x *SavedConcreteFunction) GetOutputSignature() *StructuredValue {
	if x != nil {
		return x.OutputSignature
	}
	return nil
}

type SavedBareConcreteFunction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifies a SavedConcreteFunction.
	ConcreteFunctionName string `protobuf:"bytes,1,opt,name=concrete_function_name,json=concreteFunctionName,proto3" json:"concrete_function_name,omitempty"`
	// A sequence of unique strings, one per Tensor argument.
	ArgumentKeywords []string `protobuf:"bytes,2,rep,name=argument_keywords,json=argumentKeywords,proto3" json:"argument_keywords,omitempty"`
	// The prefix of `argument_keywords` which may be identified by position.
	AllowedPositionalArguments int64 `protobuf:"varint,3,opt,name=allowed_positional_arguments,json=allowedPositionalArguments,proto3" json:"allowed_positional_arguments,omitempty"`
	// The spec of the function that this ConcreteFunction is traced from. This
	// allows the ConcreteFunction to be called with nest structure inputs. This
	// field may not be populated. If this field is absent, the concrete function
	// can only be called with flat inputs.
	// TODO(b/169361281): support calling saved ConcreteFunction with structured
	// inputs in C++ SavedModel API.
	FunctionSpec *FunctionSpec `protobuf:"bytes,4,opt,name=function_spec,json=functionSpec,proto3" json:"function_spec,omitempty"`
}

func (x *SavedBareConcreteFunction) Reset() {
	*x = SavedBareConcreteFunction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavedBareConcreteFunction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavedBareConcreteFunction) ProtoMessage() {}

func (x *SavedBareConcreteFunction) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavedBareConcreteFunction.ProtoReflect.Descriptor instead.
func (*SavedBareConcreteFunction) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescGZIP(), []int{7}
}

func (x *SavedBareConcreteFunction) GetConcreteFunctionName() string {
	if x != nil {
		return x.ConcreteFunctionName
	}
	return ""
}

func (x *SavedBareConcreteFunction) GetArgumentKeywords() []string {
	if x != nil {
		return x.ArgumentKeywords
	}
	return nil
}

func (x *SavedBareConcreteFunction) GetAllowedPositionalArguments() int64 {
	if x != nil {
		return x.AllowedPositionalArguments
	}
	return 0
}

func (x *SavedBareConcreteFunction) GetFunctionSpec() *FunctionSpec {
	if x != nil {
		return x.FunctionSpec
	}
	return nil
}

type SavedConstant struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// An Operation name for a ConstantOp in this SavedObjectGraph's MetaGraph.
	Operation string `protobuf:"bytes,1,opt,name=operation,proto3" json:"operation,omitempty"`
}

func (x *SavedConstant) Reset() {
	*x = SavedConstant{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavedConstant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavedConstant) ProtoMessage() {}

func (x *SavedConstant) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavedConstant.ProtoReflect.Descriptor instead.
func (*SavedConstant) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescGZIP(), []int{8}
}

func (x *SavedConstant) GetOperation() string {
	if x != nil {
		return x.Operation
	}
	return ""
}

// Represents a Variable that is initialized by loading the contents from the
// checkpoint.
type SavedVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dtype           types_go_proto.DataType                   `protobuf:"varint,1,opt,name=dtype,proto3,enum=tensorflow.DataType" json:"dtype,omitempty"`
	Shape           *tensor_shape_go_proto.TensorShapeProto   `protobuf:"bytes,2,opt,name=shape,proto3" json:"shape,omitempty"`
	Trainable       bool                                      `protobuf:"varint,3,opt,name=trainable,proto3" json:"trainable,omitempty"`
	Synchronization variable_go_proto.VariableSynchronization `protobuf:"varint,4,opt,name=synchronization,proto3,enum=tensorflow.VariableSynchronization" json:"synchronization,omitempty"`
	Aggregation     variable_go_proto.VariableAggregation     `protobuf:"varint,5,opt,name=aggregation,proto3,enum=tensorflow.VariableAggregation" json:"aggregation,omitempty"`
	Name            string                                    `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	Device          string                                    `protobuf:"bytes,7,opt,name=device,proto3" json:"device,omitempty"`
	// List of component variables for a distributed variable.
	//
	// When this field is non-empty, the SavedVariable will be assumed
	// to be a distributed variable defined by the components listed here.
	//
	// This is only supported by experimental loaders at the moment.
	ExperimentalDistributedVariableComponents []*SavedVariable `protobuf:"bytes,8,rep,name=experimental_distributed_variable_components,json=experimentalDistributedVariableComponents,proto3" json:"experimental_distributed_variable_components,omitempty"`
}

func (x *SavedVariable) Reset() {
	*x = SavedVariable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavedVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavedVariable) ProtoMessage() {}

func (x *SavedVariable) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavedVariable.ProtoReflect.Descriptor instead.
func (*SavedVariable) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescGZIP(), []int{9}
}

func (x *SavedVariable) GetDtype() types_go_proto.DataType {
	if x != nil {
		return x.Dtype
	}
	return types_go_proto.DataType_DT_INVALID
}

func (x *SavedVariable) GetShape() *tensor_shape_go_proto.TensorShapeProto {
	if x != nil {
		return x.Shape
	}
	return nil
}

func (x *SavedVariable) GetTrainable() bool {
	if x != nil {
		return x.Trainable
	}
	return false
}

func (x *SavedVariable) GetSynchronization() variable_go_proto.VariableSynchronization {
	if x != nil {
		return x.Synchronization
	}
	return variable_go_proto.VariableSynchronization_VARIABLE_SYNCHRONIZATION_AUTO
}

func (x *SavedVariable) GetAggregation() variable_go_proto.VariableAggregation {
	if x != nil {
		return x.Aggregation
	}
	return variable_go_proto.VariableAggregation_VARIABLE_AGGREGATION_NONE
}

func (x *SavedVariable) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SavedVariable) GetDevice() string {
	if x != nil {
		return x.Device
	}
	return ""
}

func (x *SavedVariable) GetExperimentalDistributedVariableComponents() []*SavedVariable {
	if x != nil {
		return x.ExperimentalDistributedVariableComponents
	}
	return nil
}

// Represents `FunctionSpec` used in `Function`. This represents a
// function that has been wrapped as a TensorFlow `Function`.
type FunctionSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Full arg spec from inspect.getfullargspec().
	Fullargspec *StructuredValue `protobuf:"bytes,1,opt,name=fullargspec,proto3" json:"fullargspec,omitempty"`
	// Whether this represents a class method.
	IsMethod bool `protobuf:"varint,2,opt,name=is_method,json=isMethod,proto3" json:"is_method,omitempty"`
	// The input signature, if specified.
	InputSignature *StructuredValue        `protobuf:"bytes,5,opt,name=input_signature,json=inputSignature,proto3" json:"input_signature,omitempty"`
	JitCompile     FunctionSpec_JitCompile `protobuf:"varint,6,opt,name=jit_compile,json=jitCompile,proto3,enum=tensorflow.FunctionSpec_JitCompile" json:"jit_compile,omitempty"`
}

func (x *FunctionSpec) Reset() {
	*x = FunctionSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FunctionSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunctionSpec) ProtoMessage() {}

func (x *FunctionSpec) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunctionSpec.ProtoReflect.Descriptor instead.
func (*FunctionSpec) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescGZIP(), []int{10}
}

func (x *FunctionSpec) GetFullargspec() *StructuredValue {
	if x != nil {
		return x.Fullargspec
	}
	return nil
}

func (x *FunctionSpec) GetIsMethod() bool {
	if x != nil {
		return x.IsMethod
	}
	return false
}

func (x *FunctionSpec) GetInputSignature() *StructuredValue {
	if x != nil {
		return x.InputSignature
	}
	return nil
}

func (x *FunctionSpec) GetJitCompile() FunctionSpec_JitCompile {
	if x != nil {
		return x.JitCompile
	}
	return FunctionSpec_DEFAULT
}

// A SavedResource represents a TF object that holds state during its lifetime.
// An object of this type can have a reference to a:
// create_resource() and an initialize() function.
type SavedResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A device specification indicating a required placement for the resource
	// creation function, e.g. "CPU". An empty string allows the user to select a
	// device.
	Device string `protobuf:"bytes,1,opt,name=device,proto3" json:"device,omitempty"`
}

func (x *SavedResource) Reset() {
	*x = SavedResource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavedResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavedResource) ProtoMessage() {}

func (x *SavedResource) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavedResource.ProtoReflect.Descriptor instead.
func (*SavedResource) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescGZIP(), []int{11}
}

func (x *SavedResource) GetDevice() string {
	if x != nil {
		return x.Device
	}
	return ""
}

type SaveableObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Node ids of concrete functions for saving and loading from a checkpoint.
	// These functions save and restore directly from tensors.
	SaveFunction    int32 `protobuf:"varint,2,opt,name=save_function,json=saveFunction,proto3" json:"save_function,omitempty"`
	RestoreFunction int32 `protobuf:"varint,3,opt,name=restore_function,json=restoreFunction,proto3" json:"restore_function,omitempty"`
}

func (x *SaveableObject) Reset() {
	*x = SaveableObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveableObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveableObject) ProtoMessage() {}

func (x *SaveableObject) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveableObject.ProtoReflect.Descriptor instead.
func (*SaveableObject) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescGZIP(), []int{12}
}

func (x *SaveableObject) GetSaveFunction() int32 {
	if x != nil {
		return x.SaveFunction
	}
	return 0
}

func (x *SaveableObject) GetRestoreFunction() int32 {
	if x != nil {
		return x.RestoreFunction
	}
	return 0
}

var File_tensorflow_core_protobuf_saved_object_graph_proto protoreflect.FileDescriptor

var file_tensorflow_core_protobuf_saved_object_graph_proto_rawDesc = []byte{
	0x0a, 0x31, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x61, 0x76, 0x65, 0x64,
	0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x1a,
	0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d,
	0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x73, 0x68, 0x61,
	0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77,
	0x6f, 0x72, 0x6b, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x28, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65,
	0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f,
	0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x8e, 0x02, 0x0a, 0x10, 0x53, 0x61, 0x76, 0x65, 0x64, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x47, 0x72, 0x61, 0x70, 0x68, 0x12, 0x2d, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x64, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x05,
	0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x62, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x63, 0x72, 0x65, 0x74,
	0x65, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53,
	0x61, 0x76, 0x65, 0x64, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x47, 0x72, 0x61, 0x70, 0x68, 0x2e,
	0x43, 0x6f, 0x6e, 0x63, 0x72, 0x65, 0x74, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x63, 0x72, 0x65, 0x74, 0x65,
	0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x67, 0x0a, 0x16, 0x43, 0x6f, 0x6e,
	0x63, 0x72, 0x65, 0x74, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x37, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x63, 0x72, 0x65, 0x74, 0x65, 0x46,
	0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xab, 0x09, 0x0a, 0x0b, 0x53, 0x61, 0x76, 0x65, 0x64, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x12, 0x5c, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x47, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x61, 0x62, 0x6c, 0x65,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e,
	0x12, 0x64, 0x0a, 0x0c, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73,
	0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x47, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x61, 0x62,
	0x6c, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64,
	0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x12, 0x6d, 0x0a, 0x0e, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x76,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x47, 0x72, 0x61, 0x70, 0x68,
	0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0d, 0x73, 0x6c, 0x6f, 0x74, 0x56, 0x61, 0x72, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x73, 0x12, 0x3e, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x64, 0x55, 0x73, 0x65,
	0x72, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x2e, 0x0a, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x00, 0x52, 0x05,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x12, 0x37, 0x0a, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x64, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x00, 0x52, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37,
	0x0a, 0x08, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x61,
	0x76, 0x65, 0x64, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x08, 0x76,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x5d, 0x0a, 0x16, 0x62, 0x61, 0x72, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x63, 0x72, 0x65, 0x74, 0x65, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x64, 0x42, 0x61, 0x72, 0x65, 0x43, 0x6f,
	0x6e, 0x63, 0x72, 0x65, 0x74, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00,
	0x52, 0x14, 0x62, 0x61, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x63, 0x72, 0x65, 0x74, 0x65, 0x46, 0x75,
	0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x12,
	0x37, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53,
	0x61, 0x76, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x48, 0x00, 0x52, 0x08,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x45, 0x0a, 0x0f, 0x63, 0x61, 0x70, 0x74,
	0x75, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43,
	0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x64, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x48, 0x00, 0x52,
	0x0e, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x64, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12,
	0x57, 0x0a, 0x10, 0x73, 0x61, 0x76, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x64, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x73, 0x61, 0x76, 0x65, 0x61, 0x62, 0x6c,
	0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x65, 0x64, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x48, 0x0a, 0x15, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x13, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x69, 0x7a,
	0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x29, 0x0a, 0x10, 0x72,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x61, 0x76, 0x65, 0x72, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x65,
	0x64, 0x53, 0x61, 0x76, 0x65, 0x72, 0x1a, 0x5e, 0x0a, 0x14, 0x53, 0x61, 0x76, 0x65, 0x61, 0x62,
	0x6c, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x30, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x61, 0x76,
	0x65, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x4a, 0x04,
	0x08, 0x02, 0x10, 0x03, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x22, 0x83, 0x01, 0x0a, 0x0f, 0x53, 0x61, 0x76, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3d, 0x0a, 0x0a, 0x53, 0x61, 0x76, 0x65, 0x64, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x12, 0x2f, 0x0a, 0x14, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x11, 0x61, 0x73, 0x73, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x66,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x7d, 0x0a, 0x0d, 0x53, 0x61, 0x76, 0x65, 0x64, 0x46, 0x75,
	0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x63, 0x72, 0x65,
	0x74, 0x65, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x63, 0x72, 0x65, 0x74, 0x65, 0x46, 0x75, 0x6e, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0c, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x70, 0x65, 0x63, 0x22, 0x51, 0x0a, 0x0e, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x64,
	0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x63, 0x6f,
	0x6e, 0x63, 0x72, 0x65, 0x74, 0x65, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x63, 0x72, 0x65, 0x74, 0x65, 0x46,
	0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe3, 0x01, 0x0a, 0x15, 0x53, 0x61, 0x76, 0x65,
	0x64, 0x43, 0x6f, 0x6e, 0x63, 0x72, 0x65, 0x74, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0b, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x73, 0x12, 0x5f, 0x0a, 0x1d, 0x63, 0x61, 0x6e, 0x6f, 0x6e, 0x69, 0x63, 0x61,
	0x6c, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75,
	0x72, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x1b, 0x63, 0x61, 0x6e, 0x6f, 0x6e, 0x69,
	0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x46, 0x0a, 0x10, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x6f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22, 0xff, 0x01,
	0x0a, 0x19, 0x53, 0x61, 0x76, 0x65, 0x64, 0x42, 0x61, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x63, 0x72,
	0x65, 0x74, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x16, 0x63,
	0x6f, 0x6e, 0x63, 0x72, 0x65, 0x74, 0x65, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x6f, 0x6e,
	0x63, 0x72, 0x65, 0x74, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x72, 0x67, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x61, 0x72,
	0x67, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x40,
	0x0a, 0x1c, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x72, 0x67, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x1a, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x72, 0x67, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x3d, 0x0a, 0x0d, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x70, 0x65,
	0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x65,
	0x63, 0x52, 0x0c, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x65, 0x63, 0x22,
	0x2d, 0x0a, 0x0d, 0x53, 0x61, 0x76, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xc7,
	0x03, 0x0a, 0x0d, 0x53, 0x61, 0x76, 0x65, 0x64, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x2a, 0x0a, 0x05, 0x64, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x64, 0x74, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x05,
	0x73, 0x68, 0x61, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53,
	0x68, 0x61, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x05, 0x73, 0x68, 0x61, 0x70, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x4d,
	0x0a, 0x0f, 0x73, 0x79, 0x6e, 0x63, 0x68, 0x72, 0x6f, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x79, 0x6e,
	0x63, 0x68, 0x72, 0x6f, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x73, 0x79,
	0x6e, 0x63, 0x68, 0x72, 0x6f, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a,
	0x0b, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7a, 0x0a, 0x2c,
	0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x53, 0x61, 0x76, 0x65, 0x64, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x29, 0x65,
	0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x44, 0x69, 0x73, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xae, 0x02, 0x0a, 0x0c, 0x46, 0x75, 0x6e,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x65, 0x63, 0x12, 0x3d, 0x0a, 0x0b, 0x66, 0x75, 0x6c,
	0x6c, 0x61, 0x72, 0x67, 0x73, 0x70, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x75, 0x72, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x66, 0x75, 0x6c,
	0x6c, 0x61, 0x72, 0x67, 0x73, 0x70, 0x65, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x44, 0x0a, 0x0f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x75, 0x72, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x44, 0x0a, 0x0b, 0x6a,
	0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x69, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x23, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46, 0x75,
	0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x4a, 0x69, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x69, 0x6c, 0x65, 0x52, 0x0a, 0x6a, 0x69, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x69, 0x6c,
	0x65, 0x22, 0x2a, 0x0a, 0x0a, 0x4a, 0x69, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x69, 0x6c, 0x65, 0x12,
	0x0b, 0x0a, 0x07, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4e, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x4f, 0x46, 0x46, 0x10, 0x02, 0x4a, 0x04, 0x08,
	0x03, 0x10, 0x04, 0x4a, 0x04, 0x08, 0x04, 0x10, 0x05, 0x22, 0x27, 0x0a, 0x0d, 0x53, 0x61, 0x76,
	0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x22, 0x60, 0x0a, 0x0e, 0x53, 0x61, 0x76, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x61, 0x76, 0x65, 0x5f, 0x66, 0x75, 0x6e,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x61, 0x76,
	0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0f, 0x72, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x46, 0x75, 0x6e, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x5a, 0x5a, 0x55, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2f, 0x67, 0x6f, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x73, 0x5f, 0x67, 0x6f, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0xf8, 0x01, 0x01,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescOnce sync.Once
	file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescData = file_tensorflow_core_protobuf_saved_object_graph_proto_rawDesc
)

func file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescGZIP() []byte {
	file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescOnce.Do(func() {
		file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescData = protoimpl.X.CompressGZIP(file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescData)
	})
	return file_tensorflow_core_protobuf_saved_object_graph_proto_rawDescData
}

var file_tensorflow_core_protobuf_saved_object_graph_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_tensorflow_core_protobuf_saved_object_graph_proto_goTypes = []interface{}{
	(FunctionSpec_JitCompile)(0),      // 0: tensorflow.FunctionSpec.JitCompile
	(*SavedObjectGraph)(nil),          // 1: tensorflow.SavedObjectGraph
	(*SavedObject)(nil),               // 2: tensorflow.SavedObject
	(*SavedUserObject)(nil),           // 3: tensorflow.SavedUserObject
	(*SavedAsset)(nil),                // 4: tensorflow.SavedAsset
	(*SavedFunction)(nil),             // 5: tensorflow.SavedFunction
	(*CapturedTensor)(nil),            // 6: tensorflow.CapturedTensor
	(*SavedConcreteFunction)(nil),     // 7: tensorflow.SavedConcreteFunction
	(*SavedBareConcreteFunction)(nil), // 8: tensorflow.SavedBareConcreteFunction
	(*SavedConstant)(nil),             // 9: tensorflow.SavedConstant
	(*SavedVariable)(nil),             // 10: tensorflow.SavedVariable
	(*FunctionSpec)(nil),              // 11: tensorflow.FunctionSpec
	(*SavedResource)(nil),             // 12: tensorflow.SavedResource
	(*SaveableObject)(nil),            // 13: tensorflow.SaveableObject
	nil,                               // 14: tensorflow.SavedObjectGraph.ConcreteFunctionsEntry
	nil,                               // 15: tensorflow.SavedObject.SaveableObjectsEntry
	(*TrackableObjectGraph_TrackableObject_ObjectReference)(nil),       // 16: tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference
	(*TrackableObjectGraph_TrackableObject_SlotVariableReference)(nil), // 17: tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference
	(*anypb.Any)(nil),                              // 18: google.protobuf.Any
	(*versions_go_proto.VersionDef)(nil),           // 19: tensorflow.VersionDef
	(*StructuredValue)(nil),                        // 20: tensorflow.StructuredValue
	(types_go_proto.DataType)(0),                   // 21: tensorflow.DataType
	(*tensor_shape_go_proto.TensorShapeProto)(nil), // 22: tensorflow.TensorShapeProto
	(variable_go_proto.VariableSynchronization)(0), // 23: tensorflow.VariableSynchronization
	(variable_go_proto.VariableAggregation)(0),     // 24: tensorflow.VariableAggregation
}
var file_tensorflow_core_protobuf_saved_object_graph_proto_depIdxs = []int32{
	2,  // 0: tensorflow.SavedObjectGraph.nodes:type_name -> tensorflow.SavedObject
	14, // 1: tensorflow.SavedObjectGraph.concrete_functions:type_name -> tensorflow.SavedObjectGraph.ConcreteFunctionsEntry
	16, // 2: tensorflow.SavedObject.children:type_name -> tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference
	16, // 3: tensorflow.SavedObject.dependencies:type_name -> tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference
	17, // 4: tensorflow.SavedObject.slot_variables:type_name -> tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference
	3,  // 5: tensorflow.SavedObject.user_object:type_name -> tensorflow.SavedUserObject
	4,  // 6: tensorflow.SavedObject.asset:type_name -> tensorflow.SavedAsset
	5,  // 7: tensorflow.SavedObject.function:type_name -> tensorflow.SavedFunction
	10, // 8: tensorflow.SavedObject.variable:type_name -> tensorflow.SavedVariable
	8,  // 9: tensorflow.SavedObject.bare_concrete_function:type_name -> tensorflow.SavedBareConcreteFunction
	9,  // 10: tensorflow.SavedObject.constant:type_name -> tensorflow.SavedConstant
	12, // 11: tensorflow.SavedObject.resource:type_name -> tensorflow.SavedResource
	6,  // 12: tensorflow.SavedObject.captured_tensor:type_name -> tensorflow.CapturedTensor
	15, // 13: tensorflow.SavedObject.saveable_objects:type_name -> tensorflow.SavedObject.SaveableObjectsEntry
	18, // 14: tensorflow.SavedObject.serialized_user_proto:type_name -> google.protobuf.Any
	19, // 15: tensorflow.SavedUserObject.version:type_name -> tensorflow.VersionDef
	11, // 16: tensorflow.SavedFunction.function_spec:type_name -> tensorflow.FunctionSpec
	20, // 17: tensorflow.SavedConcreteFunction.canonicalized_input_signature:type_name -> tensorflow.StructuredValue
	20, // 18: tensorflow.SavedConcreteFunction.output_signature:type_name -> tensorflow.StructuredValue
	11, // 19: tensorflow.SavedBareConcreteFunction.function_spec:type_name -> tensorflow.FunctionSpec
	21, // 20: tensorflow.SavedVariable.dtype:type_name -> tensorflow.DataType
	22, // 21: tensorflow.SavedVariable.shape:type_name -> tensorflow.TensorShapeProto
	23, // 22: tensorflow.SavedVariable.synchronization:type_name -> tensorflow.VariableSynchronization
	24, // 23: tensorflow.SavedVariable.aggregation:type_name -> tensorflow.VariableAggregation
	10, // 24: tensorflow.SavedVariable.experimental_distributed_variable_components:type_name -> tensorflow.SavedVariable
	20, // 25: tensorflow.FunctionSpec.fullargspec:type_name -> tensorflow.StructuredValue
	20, // 26: tensorflow.FunctionSpec.input_signature:type_name -> tensorflow.StructuredValue
	0,  // 27: tensorflow.FunctionSpec.jit_compile:type_name -> tensorflow.FunctionSpec.JitCompile
	7,  // 28: tensorflow.SavedObjectGraph.ConcreteFunctionsEntry.value:type_name -> tensorflow.SavedConcreteFunction
	13, // 29: tensorflow.SavedObject.SaveableObjectsEntry.value:type_name -> tensorflow.SaveableObject
	30, // [30:30] is the sub-list for method output_type
	30, // [30:30] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_tensorflow_core_protobuf_saved_object_graph_proto_init() }
func file_tensorflow_core_protobuf_saved_object_graph_proto_init() {
	if File_tensorflow_core_protobuf_saved_object_graph_proto != nil {
		return
	}
	file_tensorflow_core_protobuf_struct_proto_init()
	file_tensorflow_core_protobuf_trackable_object_graph_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavedObjectGraph); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavedObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavedUserObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavedAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavedFunction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CapturedTensor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavedConcreteFunction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavedBareConcreteFunction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavedConstant); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavedVariable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FunctionSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavedResource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveableObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*SavedObject_UserObject)(nil),
		(*SavedObject_Asset)(nil),
		(*SavedObject_Function)(nil),
		(*SavedObject_Variable)(nil),
		(*SavedObject_BareConcreteFunction)(nil),
		(*SavedObject_Constant)(nil),
		(*SavedObject_Resource)(nil),
		(*SavedObject_CapturedTensor)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tensorflow_core_protobuf_saved_object_graph_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_core_protobuf_saved_object_graph_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_protobuf_saved_object_graph_proto_depIdxs,
		EnumInfos:         file_tensorflow_core_protobuf_saved_object_graph_proto_enumTypes,
		MessageInfos:      file_tensorflow_core_protobuf_saved_object_graph_proto_msgTypes,
	}.Build()
	File_tensorflow_core_protobuf_saved_object_graph_proto = out.File
	file_tensorflow_core_protobuf_saved_object_graph_proto_rawDesc = nil
	file_tensorflow_core_protobuf_saved_object_graph_proto_goTypes = nil
	file_tensorflow_core_protobuf_saved_object_graph_proto_depIdxs = nil
}
