// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: tensorflow/core/protobuf/eager_service.proto

package for_core_protos_go_proto

import (
	context "context"
	attr_value_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/attr_value_go_proto"
	device_attributes_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/device_attributes_go_proto"
	function_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/function_go_proto"
	tensor_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto"
	tensor_shape_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto"
	versions_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/versions_go_proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// A proto representation of an eager operation.
type Operation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A unique identifier for the operation. Set by the client so that the client
	// can uniquely identify the outputs of the scheduled operation.
	//
	// In the initial implementation, sending duplicate IDs has undefined
	// behaviour, but additional constraints may be placed upon this in the
	// future.
	Id       int64              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name     string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	OpInputs []*Operation_Input `protobuf:"bytes,10,rep,name=op_inputs,json=opInputs,proto3" json:"op_inputs,omitempty"`
	// Control Operation IDs that will be respected when ops are re-ordered by
	// async execution. If async execution (+ op re-ordering) is not enabled, this
	// should have no effect.
	ControlOpIds []int64                                   `protobuf:"varint,4,rep,packed,name=control_op_ids,json=controlOpIds,proto3" json:"control_op_ids,omitempty"`
	Attrs        map[string]*attr_value_go_proto.AttrValue `protobuf:"bytes,5,rep,name=attrs,proto3" json:"attrs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Device       string                                    `protobuf:"bytes,6,opt,name=device,proto3" json:"device,omitempty"`
	// Indicates whether the op is a component of a multi-device function.
	IsComponentFunction bool `protobuf:"varint,7,opt,name=is_component_function,json=isComponentFunction,proto3" json:"is_component_function,omitempty"`
	// Set when is_component_function is true. It's initially generated
	// when we create an FunctionLibraryRuntime::Options (negative value) and used
	// to create Rendezvous for function execution. All components of a
	// multi-device function should use the same step id to make sure that they
	// can communicate through Send/Recv ops.
	FuncStepId int64 `protobuf:"varint,8,opt,name=func_step_id,json=funcStepId,proto3" json:"func_step_id,omitempty"`
	// Indicates whether the op is a function.
	IsFunction bool `protobuf:"varint,9,opt,name=is_function,json=isFunction,proto3" json:"is_function,omitempty"`
}

func (x *Operation) Reset() {
	*x = Operation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Operation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Operation) ProtoMessage() {}

func (x *Operation) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Operation.ProtoReflect.Descriptor instead.
func (*Operation) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{0}
}

func (x *Operation) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Operation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Operation) GetOpInputs() []*Operation_Input {
	if x != nil {
		return x.OpInputs
	}
	return nil
}

func (x *Operation) GetControlOpIds() []int64 {
	if x != nil {
		return x.ControlOpIds
	}
	return nil
}

func (x *Operation) GetAttrs() map[string]*attr_value_go_proto.AttrValue {
	if x != nil {
		return x.Attrs
	}
	return nil
}

func (x *Operation) GetDevice() string {
	if x != nil {
		return x.Device
	}
	return ""
}

func (x *Operation) GetIsComponentFunction() bool {
	if x != nil {
		return x.IsComponentFunction
	}
	return false
}

func (x *Operation) GetFuncStepId() int64 {
	if x != nil {
		return x.FuncStepId
	}
	return 0
}

func (x *Operation) GetIsFunction() bool {
	if x != nil {
		return x.IsFunction
	}
	return false
}

type QueueItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The remote executor should be able to handle either executing ops directly,
	// or releasing any unused tensor handles, since the tensor lifetime is
	// maintained by the client.
	//
	// Types that are assignable to Item:
	//
	//	*QueueItem_HandleToDecref
	//	*QueueItem_Operation
	//	*QueueItem_SendTensor
	//	*QueueItem_RegisterFunction
	//	*QueueItem_CleanupFunction
	//	*QueueItem_SyncRemoteExecutorForStream
	//	*QueueItem_SendPackedHandle
	Item isQueueItem_Item `protobuf_oneof:"item"`
}

func (x *QueueItem) Reset() {
	*x = QueueItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueueItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueueItem) ProtoMessage() {}

func (x *QueueItem) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueueItem.ProtoReflect.Descriptor instead.
func (*QueueItem) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{1}
}

func (m *QueueItem) GetItem() isQueueItem_Item {
	if m != nil {
		return m.Item
	}
	return nil
}

func (x *QueueItem) GetHandleToDecref() *RemoteTensorHandle {
	if x, ok := x.GetItem().(*QueueItem_HandleToDecref); ok {
		return x.HandleToDecref
	}
	return nil
}

func (x *QueueItem) GetOperation() *Operation {
	if x, ok := x.GetItem().(*QueueItem_Operation); ok {
		return x.Operation
	}
	return nil
}

func (x *QueueItem) GetSendTensor() *SendTensorOp {
	if x, ok := x.GetItem().(*QueueItem_SendTensor); ok {
		return x.SendTensor
	}
	return nil
}

func (x *QueueItem) GetRegisterFunction() *RegisterFunctionOp {
	if x, ok := x.GetItem().(*QueueItem_RegisterFunction); ok {
		return x.RegisterFunction
	}
	return nil
}

func (x *QueueItem) GetCleanupFunction() *CleanupFunctionOp {
	if x, ok := x.GetItem().(*QueueItem_CleanupFunction); ok {
		return x.CleanupFunction
	}
	return nil
}

func (x *QueueItem) GetSyncRemoteExecutorForStream() *SyncRemoteExecutorForStream {
	if x, ok := x.GetItem().(*QueueItem_SyncRemoteExecutorForStream); ok {
		return x.SyncRemoteExecutorForStream
	}
	return nil
}

func (x *QueueItem) GetSendPackedHandle() *SendPackedHandleOp {
	if x, ok := x.GetItem().(*QueueItem_SendPackedHandle); ok {
		return x.SendPackedHandle
	}
	return nil
}

type isQueueItem_Item interface {
	isQueueItem_Item()
}

type QueueItem_HandleToDecref struct {
	HandleToDecref *RemoteTensorHandle `protobuf:"bytes,1,opt,name=handle_to_decref,json=handleToDecref,proto3,oneof"`
}

type QueueItem_Operation struct {
	Operation *Operation `protobuf:"bytes,2,opt,name=operation,proto3,oneof"`
}

type QueueItem_SendTensor struct {
	SendTensor *SendTensorOp `protobuf:"bytes,3,opt,name=send_tensor,json=sendTensor,proto3,oneof"`
}

type QueueItem_RegisterFunction struct {
	// Takes a FunctionDef and makes it enqueable on the remote worker.
	RegisterFunction *RegisterFunctionOp `protobuf:"bytes,4,opt,name=register_function,json=registerFunction,proto3,oneof"`
}

type QueueItem_CleanupFunction struct {
	CleanupFunction *CleanupFunctionOp `protobuf:"bytes,5,opt,name=cleanup_function,json=cleanupFunction,proto3,oneof"`
}

type QueueItem_SyncRemoteExecutorForStream struct {
	// A remote executor is created to execute ops/functions asynchronously
	// enqueued in streaming call. Request with this item type waits for pending
	// nodes to finish on the remote executor and report status.
	SyncRemoteExecutorForStream *SyncRemoteExecutorForStream `protobuf:"bytes,6,opt,name=sync_remote_executor_for_stream,json=syncRemoteExecutorForStream,proto3,oneof"`
}

type QueueItem_SendPackedHandle struct {
	SendPackedHandle *SendPackedHandleOp `protobuf:"bytes,7,opt,name=send_packed_handle,json=sendPackedHandle,proto3,oneof"`
}

func (*QueueItem_HandleToDecref) isQueueItem_Item() {}

func (*QueueItem_Operation) isQueueItem_Item() {}

func (*QueueItem_SendTensor) isQueueItem_Item() {}

func (*QueueItem_RegisterFunction) isQueueItem_Item() {}

func (*QueueItem_CleanupFunction) isQueueItem_Item() {}

func (*QueueItem_SyncRemoteExecutorForStream) isQueueItem_Item() {}

func (*QueueItem_SendPackedHandle) isQueueItem_Item() {}

type QueueResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// `shape` and `tensor` cannot be set in the same response.
	// Shapes of output tensors for creating remote TensorHandles.
	Shape []*tensor_shape_go_proto.TensorShapeProto `protobuf:"bytes,1,rep,name=shape,proto3" json:"shape,omitempty"`
	// Optional. If set, represents the output devices of a function.
	Device []string `protobuf:"bytes,3,rep,name=device,proto3" json:"device,omitempty"`
	// Output tensors of a remote function. Set when Operation.id is invalid.
	Tensor []*tensor_go_proto.TensorProto `protobuf:"bytes,2,rep,name=tensor,proto3" json:"tensor,omitempty"`
}

func (x *QueueResponse) Reset() {
	*x = QueueResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueueResponse) ProtoMessage() {}

func (x *QueueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueueResponse.ProtoReflect.Descriptor instead.
func (*QueueResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{2}
}

func (x *QueueResponse) GetShape() []*tensor_shape_go_proto.TensorShapeProto {
	if x != nil {
		return x.Shape
	}
	return nil
}

func (x *QueueResponse) GetDevice() []string {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *QueueResponse) GetTensor() []*tensor_go_proto.TensorProto {
	if x != nil {
		return x.Tensor
	}
	return nil
}

type CreateContextRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifies the full cluster, and this particular worker's position within.
	ServerDef *ServerDef `protobuf:"bytes,1,opt,name=server_def,json=serverDef,proto3" json:"server_def,omitempty"`
	// Whether the ops on the worker should be executed synchronously or
	// asynchronously. By default, ops are executed synchronously.
	Async bool `protobuf:"varint,2,opt,name=async,proto3" json:"async,omitempty"`
	// Number of seconds to keep the context alive. If more than keep_alive_secs
	// has passed since a particular context has been communicated with, it will
	// be garbage collected.
	KeepAliveSecs int64 `protobuf:"varint,3,opt,name=keep_alive_secs,json=keepAliveSecs,proto3" json:"keep_alive_secs,omitempty"`
	// This is the version for all the ops that will be enqueued by the client.
	VersionDef *versions_go_proto.VersionDef `protobuf:"bytes,4,opt,name=version_def,json=versionDef,proto3" json:"version_def,omitempty"`
	// Device attributes in the cluster
	ClusterDeviceAttributes []*device_attributes_go_proto.DeviceAttributes `protobuf:"bytes,6,rep,name=cluster_device_attributes,json=clusterDeviceAttributes,proto3" json:"cluster_device_attributes,omitempty"`
	// The ID of the created context. This is usually a randomly generated number,
	// that will be used to identify the context in future requests to the
	// service. Contexts are not persisted through server restarts.
	// This ID will be used for all future communications as well. It is essential
	// that both ends use this ID for selecting a rendezvous to get everything to
	// match.
	ContextId uint64 `protobuf:"fixed64,7,opt,name=context_id,json=contextId,proto3" json:"context_id,omitempty"`
	// The view ID of the context.
	ContextViewId uint64 `protobuf:"fixed64,8,opt,name=context_view_id,json=contextViewId,proto3" json:"context_view_id,omitempty"`
	// For a multi device function, if false, eagerly copy all remote inputs to
	// the default function device; if true, lazily copy remote inputs to their
	// target devices after function instantiation to avoid redundant copies.
	LazyCopyRemoteFunctionInputs bool `protobuf:"varint,9,opt,name=lazy_copy_remote_function_inputs,json=lazyCopyRemoteFunctionInputs,proto3" json:"lazy_copy_remote_function_inputs,omitempty"`
}

func (x *CreateContextRequest) Reset() {
	*x = CreateContextRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateContextRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateContextRequest) ProtoMessage() {}

func (x *CreateContextRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateContextRequest.ProtoReflect.Descriptor instead.
func (*CreateContextRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{3}
}

func (x *CreateContextRequest) GetServerDef() *ServerDef {
	if x != nil {
		return x.ServerDef
	}
	return nil
}

func (x *CreateContextRequest) GetAsync() bool {
	if x != nil {
		return x.Async
	}
	return false
}

func (x *CreateContextRequest) GetKeepAliveSecs() int64 {
	if x != nil {
		return x.KeepAliveSecs
	}
	return 0
}

func (x *CreateContextRequest) GetVersionDef() *versions_go_proto.VersionDef {
	if x != nil {
		return x.VersionDef
	}
	return nil
}

func (x *CreateContextRequest) GetClusterDeviceAttributes() []*device_attributes_go_proto.DeviceAttributes {
	if x != nil {
		return x.ClusterDeviceAttributes
	}
	return nil
}

func (x *CreateContextRequest) GetContextId() uint64 {
	if x != nil {
		return x.ContextId
	}
	return 0
}

func (x *CreateContextRequest) GetContextViewId() uint64 {
	if x != nil {
		return x.ContextViewId
	}
	return 0
}

func (x *CreateContextRequest) GetLazyCopyRemoteFunctionInputs() bool {
	if x != nil {
		return x.LazyCopyRemoteFunctionInputs
	}
	return false
}

type CreateContextResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of devices that are locally accessible to the worker.
	DeviceAttributes []*device_attributes_go_proto.DeviceAttributes `protobuf:"bytes,2,rep,name=device_attributes,json=deviceAttributes,proto3" json:"device_attributes,omitempty"`
}

func (x *CreateContextResponse) Reset() {
	*x = CreateContextResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateContextResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateContextResponse) ProtoMessage() {}

func (x *CreateContextResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateContextResponse.ProtoReflect.Descriptor instead.
func (*CreateContextResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateContextResponse) GetDeviceAttributes() []*device_attributes_go_proto.DeviceAttributes {
	if x != nil {
		return x.DeviceAttributes
	}
	return nil
}

type UpdateContextRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifies the full cluster, and this particular worker's position within.
	ServerDef *ServerDef `protobuf:"bytes,1,opt,name=server_def,json=serverDef,proto3" json:"server_def,omitempty"`
	// Device attributes in the cluster.
	// If this field is empty, it indicates that this is a simple update request
	// that only increments the cluster view ID and does not require changes to
	// the workers it connects to.
	ClusterDeviceAttributes []*device_attributes_go_proto.DeviceAttributes `protobuf:"bytes,2,rep,name=cluster_device_attributes,json=clusterDeviceAttributes,proto3" json:"cluster_device_attributes,omitempty"`
	// The ID of the context to be updated. A context with the specified ID must
	// already exist on the recepient server of this request.
	ContextId uint64 `protobuf:"fixed64,3,opt,name=context_id,json=contextId,proto3" json:"context_id,omitempty"`
	// The view ID of the context, which should be contiguously incremented when
	// updating the same context.
	ContextViewId uint64 `protobuf:"fixed64,4,opt,name=context_view_id,json=contextViewId,proto3" json:"context_view_id,omitempty"`
}

func (x *UpdateContextRequest) Reset() {
	*x = UpdateContextRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateContextRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContextRequest) ProtoMessage() {}

func (x *UpdateContextRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContextRequest.ProtoReflect.Descriptor instead.
func (*UpdateContextRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateContextRequest) GetServerDef() *ServerDef {
	if x != nil {
		return x.ServerDef
	}
	return nil
}

func (x *UpdateContextRequest) GetClusterDeviceAttributes() []*device_attributes_go_proto.DeviceAttributes {
	if x != nil {
		return x.ClusterDeviceAttributes
	}
	return nil
}

func (x *UpdateContextRequest) GetContextId() uint64 {
	if x != nil {
		return x.ContextId
	}
	return 0
}

func (x *UpdateContextRequest) GetContextViewId() uint64 {
	if x != nil {
		return x.ContextViewId
	}
	return 0
}

type UpdateContextResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of devices that are locally accessible to the worker.
	DeviceAttributes []*device_attributes_go_proto.DeviceAttributes `protobuf:"bytes,1,rep,name=device_attributes,json=deviceAttributes,proto3" json:"device_attributes,omitempty"`
}

func (x *UpdateContextResponse) Reset() {
	*x = UpdateContextResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateContextResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContextResponse) ProtoMessage() {}

func (x *UpdateContextResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContextResponse.ProtoReflect.Descriptor instead.
func (*UpdateContextResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateContextResponse) GetDeviceAttributes() []*device_attributes_go_proto.DeviceAttributes {
	if x != nil {
		return x.DeviceAttributes
	}
	return nil
}

type EnqueueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContextId uint64       `protobuf:"fixed64,1,opt,name=context_id,json=contextId,proto3" json:"context_id,omitempty"`
	Queue     []*QueueItem `protobuf:"bytes,3,rep,name=queue,proto3" json:"queue,omitempty"`
}

func (x *EnqueueRequest) Reset() {
	*x = EnqueueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnqueueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnqueueRequest) ProtoMessage() {}

func (x *EnqueueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnqueueRequest.ProtoReflect.Descriptor instead.
func (*EnqueueRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{7}
}

func (x *EnqueueRequest) GetContextId() uint64 {
	if x != nil {
		return x.ContextId
	}
	return 0
}

func (x *EnqueueRequest) GetQueue() []*QueueItem {
	if x != nil {
		return x.Queue
	}
	return nil
}

type EnqueueResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A single operation response for every item in the request.
	QueueResponse []*QueueResponse `protobuf:"bytes,1,rep,name=queue_response,json=queueResponse,proto3" json:"queue_response,omitempty"`
}

func (x *EnqueueResponse) Reset() {
	*x = EnqueueResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnqueueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnqueueResponse) ProtoMessage() {}

func (x *EnqueueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnqueueResponse.ProtoReflect.Descriptor instead.
func (*EnqueueResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{8}
}

func (x *EnqueueResponse) GetQueueResponse() []*QueueResponse {
	if x != nil {
		return x.QueueResponse
	}
	return nil
}

type WaitQueueDoneRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContextId uint64 `protobuf:"fixed64,1,opt,name=context_id,json=contextId,proto3" json:"context_id,omitempty"`
	// Ids to wait on. If empty, wait on everything currently pending.
	OpId []int64 `protobuf:"varint,2,rep,packed,name=op_id,json=opId,proto3" json:"op_id,omitempty"`
}

func (x *WaitQueueDoneRequest) Reset() {
	*x = WaitQueueDoneRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaitQueueDoneRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaitQueueDoneRequest) ProtoMessage() {}

func (x *WaitQueueDoneRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaitQueueDoneRequest.ProtoReflect.Descriptor instead.
func (*WaitQueueDoneRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{9}
}

func (x *WaitQueueDoneRequest) GetContextId() uint64 {
	if x != nil {
		return x.ContextId
	}
	return 0
}

func (x *WaitQueueDoneRequest) GetOpId() []int64 {
	if x != nil {
		return x.OpId
	}
	return nil
}

type WaitQueueDoneResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WaitQueueDoneResponse) Reset() {
	*x = WaitQueueDoneResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaitQueueDoneResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaitQueueDoneResponse) ProtoMessage() {}

func (x *WaitQueueDoneResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaitQueueDoneResponse.ProtoReflect.Descriptor instead.
func (*WaitQueueDoneResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{10}
}

type RunComponentFunctionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContextId uint64     `protobuf:"fixed64,1,opt,name=context_id,json=contextId,proto3" json:"context_id,omitempty"`
	Operation *Operation `protobuf:"bytes,2,opt,name=operation,proto3" json:"operation,omitempty"`
	// The output indices of its parent function.
	OutputNum []int32 `protobuf:"varint,3,rep,packed,name=output_num,json=outputNum,proto3" json:"output_num,omitempty"`
}

func (x *RunComponentFunctionRequest) Reset() {
	*x = RunComponentFunctionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunComponentFunctionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunComponentFunctionRequest) ProtoMessage() {}

func (x *RunComponentFunctionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunComponentFunctionRequest.ProtoReflect.Descriptor instead.
func (*RunComponentFunctionRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{11}
}

func (x *RunComponentFunctionRequest) GetContextId() uint64 {
	if x != nil {
		return x.ContextId
	}
	return 0
}

func (x *RunComponentFunctionRequest) GetOperation() *Operation {
	if x != nil {
		return x.Operation
	}
	return nil
}

func (x *RunComponentFunctionRequest) GetOutputNum() []int32 {
	if x != nil {
		return x.OutputNum
	}
	return nil
}

type RunComponentFunctionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Shape  []*tensor_shape_go_proto.TensorShapeProto `protobuf:"bytes,1,rep,name=shape,proto3" json:"shape,omitempty"`
	Tensor []*tensor_go_proto.TensorProto            `protobuf:"bytes,2,rep,name=tensor,proto3" json:"tensor,omitempty"`
}

func (x *RunComponentFunctionResponse) Reset() {
	*x = RunComponentFunctionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunComponentFunctionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunComponentFunctionResponse) ProtoMessage() {}

func (x *RunComponentFunctionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunComponentFunctionResponse.ProtoReflect.Descriptor instead.
func (*RunComponentFunctionResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{12}
}

func (x *RunComponentFunctionResponse) GetShape() []*tensor_shape_go_proto.TensorShapeProto {
	if x != nil {
		return x.Shape
	}
	return nil
}

func (x *RunComponentFunctionResponse) GetTensor() []*tensor_go_proto.TensorProto {
	if x != nil {
		return x.Tensor
	}
	return nil
}

type KeepAliveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContextId uint64 `protobuf:"fixed64,1,opt,name=context_id,json=contextId,proto3" json:"context_id,omitempty"`
}

func (x *KeepAliveRequest) Reset() {
	*x = KeepAliveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeepAliveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeepAliveRequest) ProtoMessage() {}

func (x *KeepAliveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeepAliveRequest.ProtoReflect.Descriptor instead.
func (*KeepAliveRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{13}
}

func (x *KeepAliveRequest) GetContextId() uint64 {
	if x != nil {
		return x.ContextId
	}
	return 0
}

type KeepAliveResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// If the requested context_id is on the remote host, set the context view ID.
	ContextViewId uint64 `protobuf:"fixed64,1,opt,name=context_view_id,json=contextViewId,proto3" json:"context_view_id,omitempty"`
}

func (x *KeepAliveResponse) Reset() {
	*x = KeepAliveResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeepAliveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeepAliveResponse) ProtoMessage() {}

func (x *KeepAliveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeepAliveResponse.ProtoReflect.Descriptor instead.
func (*KeepAliveResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{14}
}

func (x *KeepAliveResponse) GetContextViewId() uint64 {
	if x != nil {
		return x.ContextViewId
	}
	return 0
}

type CloseContextRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContextId     uint64 `protobuf:"fixed64,1,opt,name=context_id,json=contextId,proto3" json:"context_id,omitempty"`
	ContextViewId uint64 `protobuf:"fixed64,2,opt,name=context_view_id,json=contextViewId,proto3" json:"context_view_id,omitempty"`
}

func (x *CloseContextRequest) Reset() {
	*x = CloseContextRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseContextRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseContextRequest) ProtoMessage() {}

func (x *CloseContextRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseContextRequest.ProtoReflect.Descriptor instead.
func (*CloseContextRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{15}
}

func (x *CloseContextRequest) GetContextId() uint64 {
	if x != nil {
		return x.ContextId
	}
	return 0
}

func (x *CloseContextRequest) GetContextViewId() uint64 {
	if x != nil {
		return x.ContextViewId
	}
	return 0
}

type CloseContextResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CloseContextResponse) Reset() {
	*x = CloseContextResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseContextResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseContextResponse) ProtoMessage() {}

func (x *CloseContextResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseContextResponse.ProtoReflect.Descriptor instead.
func (*CloseContextResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{16}
}

type RegisterFunctionOp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FunctionDef *function_go_proto.FunctionDef `protobuf:"bytes,1,opt,name=function_def,json=functionDef,proto3" json:"function_def,omitempty"`
	// If true, it means that function_def is produced by graph partition during
	// multi-device function instantiation.
	IsComponentFunction bool `protobuf:"varint,2,opt,name=is_component_function,json=isComponentFunction,proto3" json:"is_component_function,omitempty"`
	// All necessary FunctionDefs and GradientDefs to expand `function_def`.
	// When is_component_function is true, `function_def` could be a nested
	// function, since some nodes in its parent's function body could be
	// replaced with a new function by the graph optimization passes. No need to
	// add FunctionDefs here to the function cache in EagerContext since they
	// won't be executed as KernelAndDevices.
	Library *function_go_proto.FunctionDefLibrary `protobuf:"bytes,3,opt,name=library,proto3" json:"library,omitempty"`
}

func (x *RegisterFunctionOp) Reset() {
	*x = RegisterFunctionOp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterFunctionOp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterFunctionOp) ProtoMessage() {}

func (x *RegisterFunctionOp) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterFunctionOp.ProtoReflect.Descriptor instead.
func (*RegisterFunctionOp) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{17}
}

func (x *RegisterFunctionOp) GetFunctionDef() *function_go_proto.FunctionDef {
	if x != nil {
		return x.FunctionDef
	}
	return nil
}

func (x *RegisterFunctionOp) GetIsComponentFunction() bool {
	if x != nil {
		return x.IsComponentFunction
	}
	return false
}

func (x *RegisterFunctionOp) GetLibrary() *function_go_proto.FunctionDefLibrary {
	if x != nil {
		return x.Library
	}
	return nil
}

// Cleanup the step state of a multi-device function (e.g. tensors buffered by
// a `Send` op but not picked up by its corresponding `Recv` op).
type CleanupFunctionOp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StepId int64 `protobuf:"varint,1,opt,name=step_id,json=stepId,proto3" json:"step_id,omitempty"`
}

func (x *CleanupFunctionOp) Reset() {
	*x = CleanupFunctionOp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CleanupFunctionOp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanupFunctionOp) ProtoMessage() {}

func (x *CleanupFunctionOp) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanupFunctionOp.ProtoReflect.Descriptor instead.
func (*CleanupFunctionOp) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{18}
}

func (x *CleanupFunctionOp) GetStepId() int64 {
	if x != nil {
		return x.StepId
	}
	return 0
}

type SyncRemoteExecutorForStream struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SyncRemoteExecutorForStream) Reset() {
	*x = SyncRemoteExecutorForStream{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncRemoteExecutorForStream) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncRemoteExecutorForStream) ProtoMessage() {}

func (x *SyncRemoteExecutorForStream) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncRemoteExecutorForStream.ProtoReflect.Descriptor instead.
func (*SyncRemoteExecutorForStream) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{19}
}

type SendTensorOp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// All remote tensors are identified by <Op ID, Output num>. To mimic this
	// situation when directly sending tensors, we include an "artificial" op ID
	// (which would have corresponded to the _Recv op when not using SendTensor).
	OpId int64 `protobuf:"varint,1,opt,name=op_id,json=opId,proto3" json:"op_id,omitempty"`
	// The index within the repeated field is the output number that will help
	// uniquely identify (along with the above op_id) the particular tensor.
	Tensors []*tensor_go_proto.TensorProto `protobuf:"bytes,2,rep,name=tensors,proto3" json:"tensors,omitempty"`
	// The device on which the tensors should be resident.
	DeviceName string `protobuf:"bytes,3,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`
}

func (x *SendTensorOp) Reset() {
	*x = SendTensorOp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendTensorOp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTensorOp) ProtoMessage() {}

func (x *SendTensorOp) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTensorOp.ProtoReflect.Descriptor instead.
func (*SendTensorOp) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{20}
}

func (x *SendTensorOp) GetOpId() int64 {
	if x != nil {
		return x.OpId
	}
	return 0
}

func (x *SendTensorOp) GetTensors() []*tensor_go_proto.TensorProto {
	if x != nil {
		return x.Tensors
	}
	return nil
}

func (x *SendTensorOp) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

// Send a packed TensorHandle to a remote worker.
type SendPackedHandleOp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Op id of the remote packed TensorHandle.
	OpId       int64                        `protobuf:"varint,1,opt,name=op_id,json=opId,proto3" json:"op_id,omitempty"`
	Handles    []*SendPackedHandleOp_Handle `protobuf:"bytes,2,rep,name=handles,proto3" json:"handles,omitempty"`
	DeviceName string                       `protobuf:"bytes,3,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`
}

func (x *SendPackedHandleOp) Reset() {
	*x = SendPackedHandleOp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendPackedHandleOp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPackedHandleOp) ProtoMessage() {}

func (x *SendPackedHandleOp) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPackedHandleOp.ProtoReflect.Descriptor instead.
func (*SendPackedHandleOp) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{21}
}

func (x *SendPackedHandleOp) GetOpId() int64 {
	if x != nil {
		return x.OpId
	}
	return 0
}

func (x *SendPackedHandleOp) GetHandles() []*SendPackedHandleOp_Handle {
	if x != nil {
		return x.Handles
	}
	return nil
}

func (x *SendPackedHandleOp) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

type Operation_Input struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Item:
	//
	//	*Operation_Input_RemoteHandle
	//	*Operation_Input_Tensor
	Item isOperation_Input_Item `protobuf_oneof:"item"`
}

func (x *Operation_Input) Reset() {
	*x = Operation_Input{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Operation_Input) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Operation_Input) ProtoMessage() {}

func (x *Operation_Input) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Operation_Input.ProtoReflect.Descriptor instead.
func (*Operation_Input) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{0, 0}
}

func (m *Operation_Input) GetItem() isOperation_Input_Item {
	if m != nil {
		return m.Item
	}
	return nil
}

func (x *Operation_Input) GetRemoteHandle() *RemoteTensorHandle {
	if x, ok := x.GetItem().(*Operation_Input_RemoteHandle); ok {
		return x.RemoteHandle
	}
	return nil
}

func (x *Operation_Input) GetTensor() *tensor_go_proto.TensorProto {
	if x, ok := x.GetItem().(*Operation_Input_Tensor); ok {
		return x.Tensor
	}
	return nil
}

type isOperation_Input_Item interface {
	isOperation_Input_Item()
}

type Operation_Input_RemoteHandle struct {
	RemoteHandle *RemoteTensorHandle `protobuf:"bytes,1,opt,name=remote_handle,json=remoteHandle,proto3,oneof"`
}

type Operation_Input_Tensor struct {
	Tensor *tensor_go_proto.TensorProto `protobuf:"bytes,2,opt,name=tensor,proto3,oneof"`
}

func (*Operation_Input_RemoteHandle) isOperation_Input_Item() {}

func (*Operation_Input_Tensor) isOperation_Input_Item() {}

type SendPackedHandleOp_LocalTensorHandle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tensor *tensor_go_proto.TensorProto `protobuf:"bytes,1,opt,name=tensor,proto3" json:"tensor,omitempty"`
	// Device where the tensor is produced.
	Device string `protobuf:"bytes,2,opt,name=device,proto3" json:"device,omitempty"`
}

func (x *SendPackedHandleOp_LocalTensorHandle) Reset() {
	*x = SendPackedHandleOp_LocalTensorHandle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendPackedHandleOp_LocalTensorHandle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPackedHandleOp_LocalTensorHandle) ProtoMessage() {}

func (x *SendPackedHandleOp_LocalTensorHandle) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPackedHandleOp_LocalTensorHandle.ProtoReflect.Descriptor instead.
func (*SendPackedHandleOp_LocalTensorHandle) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{21, 0}
}

func (x *SendPackedHandleOp_LocalTensorHandle) GetTensor() *tensor_go_proto.TensorProto {
	if x != nil {
		return x.Tensor
	}
	return nil
}

func (x *SendPackedHandleOp_LocalTensorHandle) GetDevice() string {
	if x != nil {
		return x.Device
	}
	return ""
}

type SendPackedHandleOp_Handle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Item:
	//
	//	*SendPackedHandleOp_Handle_LocalHandle
	//	*SendPackedHandleOp_Handle_RemoteHandle
	Item isSendPackedHandleOp_Handle_Item `protobuf_oneof:"item"`
}

func (x *SendPackedHandleOp_Handle) Reset() {
	*x = SendPackedHandleOp_Handle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendPackedHandleOp_Handle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPackedHandleOp_Handle) ProtoMessage() {}

func (x *SendPackedHandleOp_Handle) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_protobuf_eager_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPackedHandleOp_Handle.ProtoReflect.Descriptor instead.
func (*SendPackedHandleOp_Handle) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP(), []int{21, 1}
}

func (m *SendPackedHandleOp_Handle) GetItem() isSendPackedHandleOp_Handle_Item {
	if m != nil {
		return m.Item
	}
	return nil
}

func (x *SendPackedHandleOp_Handle) GetLocalHandle() *SendPackedHandleOp_LocalTensorHandle {
	if x, ok := x.GetItem().(*SendPackedHandleOp_Handle_LocalHandle); ok {
		return x.LocalHandle
	}
	return nil
}

func (x *SendPackedHandleOp_Handle) GetRemoteHandle() *RemoteTensorHandle {
	if x, ok := x.GetItem().(*SendPackedHandleOp_Handle_RemoteHandle); ok {
		return x.RemoteHandle
	}
	return nil
}

type isSendPackedHandleOp_Handle_Item interface {
	isSendPackedHandleOp_Handle_Item()
}

type SendPackedHandleOp_Handle_LocalHandle struct {
	LocalHandle *SendPackedHandleOp_LocalTensorHandle `protobuf:"bytes,1,opt,name=local_handle,json=localHandle,proto3,oneof"`
}

type SendPackedHandleOp_Handle_RemoteHandle struct {
	RemoteHandle *RemoteTensorHandle `protobuf:"bytes,2,opt,name=remote_handle,json=remoteHandle,proto3,oneof"`
}

func (*SendPackedHandleOp_Handle_LocalHandle) isSendPackedHandleOp_Handle_Item() {}

func (*SendPackedHandleOp_Handle_RemoteHandle) isSendPackedHandleOp_Handle_Item() {}

var File_tensorflow_core_protobuf_eager_service_proto protoreflect.FileDescriptor

var file_tensorflow_core_protobuf_eager_service_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x61, 0x67, 0x65, 0x72,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72,
	0x1a, 0x2a, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x61, 0x74, 0x74, 0x72,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72,
	0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x28, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x66, 0x75, 0x6e, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65,
	0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2c, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f,
	0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x5f, 0x73, 0x68, 0x61, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x28, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xcb, 0x04, 0x0a, 0x09, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x09, 0x6f, 0x70, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18,
	0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x08, 0x6f, 0x70, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x6f, 0x70,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x4f, 0x70, 0x49, 0x64, 0x73, 0x12, 0x3c, 0x0a, 0x05, 0x61, 0x74, 0x74, 0x72,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x32,
	0x0a, 0x15, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x66,
	0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69,
	0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0c, 0x66, 0x75, 0x6e, 0x63, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x66, 0x75, 0x6e, 0x63, 0x53, 0x74,
	0x65, 0x70, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x46, 0x75, 0x6e,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x8f, 0x01, 0x0a, 0x05, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12,
	0x4b, 0x0a, 0x0d, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x0c,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x31, 0x0a, 0x06,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x48, 0x00, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x42,
	0x06, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x1a, 0x4f, 0x0a, 0x0a, 0x41, 0x74, 0x74, 0x72, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x4a, 0x04, 0x08, 0x03, 0x10, 0x04, 0x22, 0xd9,
	0x04, 0x0a, 0x09, 0x51, 0x75, 0x65, 0x75, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x50, 0x0a, 0x10,
	0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x64, 0x65, 0x63, 0x72, 0x65, 0x66,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x0e,
	0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x54, 0x6f, 0x44, 0x65, 0x63, 0x72, 0x65, 0x66, 0x12, 0x3b,
	0x0a, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00,
	0x52, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x0b, 0x73,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x4f, 0x70,
	0x48, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x53,
	0x0a, 0x11, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x48,
	0x00, 0x52, 0x10, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x46, 0x75, 0x6e, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x10, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x5f, 0x66,
	0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x4f, 0x70, 0x48, 0x00, 0x52, 0x0f, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x46, 0x75, 0x6e,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x75, 0x0a, 0x1f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x72, 0x65,
	0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x5f, 0x66, 0x6f,
	0x72, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x6f, 0x72, 0x46, 0x6f, 0x72, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x48, 0x00, 0x52,
	0x1b, 0x73, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x6f, 0x72, 0x46, 0x6f, 0x72, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x54, 0x0a, 0x12,
	0x73, 0x65, 0x6e, 0x64, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x68, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64,
	0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x4f, 0x70, 0x48, 0x00,
	0x52, 0x10, 0x73, 0x65, 0x6e, 0x64, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x22, 0x8c, 0x01, 0x0a, 0x0d, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x05,
	0x73, 0x68, 0x61, 0x70, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53,
	0x68, 0x61, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x05, 0x73, 0x68, 0x61, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x22, 0xb2, 0x03, 0x0a, 0x14, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x34, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x66,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x44, 0x65, 0x66, 0x52, 0x09, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x44, 0x65, 0x66, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x73, 0x79, 0x6e,
	0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x12, 0x26,
	0x0a, 0x0f, 0x6b, 0x65, 0x65, 0x70, 0x5f, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x65, 0x63,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6b, 0x65, 0x65, 0x70, 0x41, 0x6c, 0x69,
	0x76, 0x65, 0x53, 0x65, 0x63, 0x73, 0x12, 0x37, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x66, 0x52, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12,
	0x58, 0x0a, 0x19, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x52, 0x17, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x06, 0x52, 0x09, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x06, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x56, 0x69, 0x65, 0x77, 0x49, 0x64,
	0x12, 0x46, 0x0a, 0x20, 0x6c, 0x61, 0x7a, 0x79, 0x5f, 0x63, 0x6f, 0x70, 0x79, 0x5f, 0x72, 0x65,
	0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e,
	0x70, 0x75, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1c, 0x6c, 0x61, 0x7a, 0x79,
	0x43, 0x6f, 0x70, 0x79, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x4a, 0x04, 0x08, 0x05, 0x10, 0x06, 0x22, 0x68,
	0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x11, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x52, 0x10, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x73, 0x4a, 0x04, 0x08, 0x01, 0x10, 0x02, 0x22, 0xed, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x34, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x66, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x44, 0x65, 0x66, 0x52, 0x09, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x44, 0x65, 0x66, 0x12, 0x58, 0x0a, 0x19, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x17, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x06, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x06, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x56, 0x69, 0x65, 0x77, 0x49, 0x64, 0x22, 0x62, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x49, 0x0a, 0x11, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x10, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x22, 0x62, 0x0a, 0x0e,
	0x45, 0x6e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x06, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x49, 0x64, 0x12, 0x31, 0x0a,
	0x05, 0x71, 0x75, 0x65, 0x75, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x22, 0x59, 0x0a, 0x0f, 0x45, 0x6e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0d, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4a, 0x0a, 0x14, 0x57,
	0x61, 0x69, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x44, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x49, 0x64, 0x12, 0x13, 0x0a, 0x05, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x04, 0x6f, 0x70, 0x49, 0x64, 0x22, 0x17, 0x0a, 0x15, 0x57, 0x61, 0x69, 0x74, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x44, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x96, 0x01, 0x0a, 0x1b, 0x52, 0x75, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x06, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x49, 0x64, 0x12,
	0x39, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x09,
	0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4e, 0x75, 0x6d, 0x22, 0x83, 0x01, 0x0a, 0x1c, 0x52, 0x75,
	0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x73, 0x68,
	0x61, 0x70, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53, 0x68, 0x61,
	0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x05, 0x73, 0x68, 0x61, 0x70, 0x65, 0x12, 0x2f,
	0x0a, 0x06, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x22,
	0x31, 0x0a, 0x10, 0x4b, 0x65, 0x65, 0x70, 0x41, 0x6c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x49, 0x64, 0x22, 0x3b, 0x0a, 0x11, 0x4b, 0x65, 0x65, 0x70, 0x41, 0x6c, 0x69, 0x76, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06,
	0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x56, 0x69, 0x65, 0x77, 0x49, 0x64, 0x22,
	0x5c, 0x0a, 0x13, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x5f, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x06, 0x52, 0x0d,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x56, 0x69, 0x65, 0x77, 0x49, 0x64, 0x22, 0x16, 0x0a,
	0x14, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xbe, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x12, 0x3a, 0x0a, 0x0c,
	0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x0b, 0x66, 0x75, 0x6e,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x32, 0x0a, 0x15, 0x69, 0x73, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x07,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x52, 0x07, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x22, 0x2c, 0x0a, 0x11, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75,
	0x70, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x73,
	0x74, 0x65, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74,
	0x65, 0x70, 0x49, 0x64, 0x22, 0x1d, 0x0a, 0x1b, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x46, 0x6f, 0x72, 0x53, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x22, 0x77, 0x0a, 0x0c, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x4f, 0x70, 0x12, 0x13, 0x0a, 0x05, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x6f, 0x70, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x07, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x52, 0x07, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xac, 0x03, 0x0a,
	0x12, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x4f, 0x70, 0x12, 0x13, 0x0a, 0x05, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x6f, 0x70, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x07, 0x68, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e,
	0x64, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x4f, 0x70, 0x2e,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x07, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x1a, 0x5c, 0x0a, 0x11, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x06,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x1a, 0xba,
	0x01, 0x0a, 0x06, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x5b, 0x0a, 0x0c, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x4f, 0x70, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x54, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x6c, 0x6f, 0x63, 0x61, 0x6c,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x4b, 0x0a, 0x0d, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x32, 0x8d, 0x06, 0x0a, 0x0c,
	0x45, 0x61, 0x67, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x60, 0x0a, 0x0d,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x26, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x60,
	0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12,
	0x26, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4e, 0x0a, 0x07, 0x45, 0x6e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x12, 0x20, 0x2e, 0x74, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45,
	0x6e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x45, 0x6e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x5b, 0x0a, 0x10, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x12, 0x20, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x28, 0x01, 0x30, 0x01, 0x12, 0x60, 0x0a,
	0x0d, 0x57, 0x61, 0x69, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x44, 0x6f, 0x6e, 0x65, 0x12, 0x26,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x44, 0x6f, 0x6e, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x51, 0x75,
	0x65, 0x75, 0x65, 0x44, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x75, 0x0a, 0x14, 0x52, 0x75, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x46,
	0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x09, 0x4b, 0x65, 0x65, 0x70, 0x41, 0x6c,
	0x69, 0x76, 0x65, 0x12, 0x22, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4b, 0x65, 0x65, 0x70, 0x41, 0x6c, 0x69, 0x76, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4b, 0x65, 0x65, 0x70, 0x41,
	0x6c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x0c,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x25, 0x2e, 0x74,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x65, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x57, 0x5a, 0x55, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x67, 0x6f, 0x2f, 0x63, 0x6f,
	0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x6f, 0x72, 0x5f,
	0x63, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x5f, 0x67, 0x6f, 0x5f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tensorflow_core_protobuf_eager_service_proto_rawDescOnce sync.Once
	file_tensorflow_core_protobuf_eager_service_proto_rawDescData = file_tensorflow_core_protobuf_eager_service_proto_rawDesc
)

func file_tensorflow_core_protobuf_eager_service_proto_rawDescGZIP() []byte {
	file_tensorflow_core_protobuf_eager_service_proto_rawDescOnce.Do(func() {
		file_tensorflow_core_protobuf_eager_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_tensorflow_core_protobuf_eager_service_proto_rawDescData)
	})
	return file_tensorflow_core_protobuf_eager_service_proto_rawDescData
}

var file_tensorflow_core_protobuf_eager_service_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_tensorflow_core_protobuf_eager_service_proto_goTypes = []interface{}{
	(*Operation)(nil),                                   // 0: tensorflow.eager.Operation
	(*QueueItem)(nil),                                   // 1: tensorflow.eager.QueueItem
	(*QueueResponse)(nil),                               // 2: tensorflow.eager.QueueResponse
	(*CreateContextRequest)(nil),                        // 3: tensorflow.eager.CreateContextRequest
	(*CreateContextResponse)(nil),                       // 4: tensorflow.eager.CreateContextResponse
	(*UpdateContextRequest)(nil),                        // 5: tensorflow.eager.UpdateContextRequest
	(*UpdateContextResponse)(nil),                       // 6: tensorflow.eager.UpdateContextResponse
	(*EnqueueRequest)(nil),                              // 7: tensorflow.eager.EnqueueRequest
	(*EnqueueResponse)(nil),                             // 8: tensorflow.eager.EnqueueResponse
	(*WaitQueueDoneRequest)(nil),                        // 9: tensorflow.eager.WaitQueueDoneRequest
	(*WaitQueueDoneResponse)(nil),                       // 10: tensorflow.eager.WaitQueueDoneResponse
	(*RunComponentFunctionRequest)(nil),                 // 11: tensorflow.eager.RunComponentFunctionRequest
	(*RunComponentFunctionResponse)(nil),                // 12: tensorflow.eager.RunComponentFunctionResponse
	(*KeepAliveRequest)(nil),                            // 13: tensorflow.eager.KeepAliveRequest
	(*KeepAliveResponse)(nil),                           // 14: tensorflow.eager.KeepAliveResponse
	(*CloseContextRequest)(nil),                         // 15: tensorflow.eager.CloseContextRequest
	(*CloseContextResponse)(nil),                        // 16: tensorflow.eager.CloseContextResponse
	(*RegisterFunctionOp)(nil),                          // 17: tensorflow.eager.RegisterFunctionOp
	(*CleanupFunctionOp)(nil),                           // 18: tensorflow.eager.CleanupFunctionOp
	(*SyncRemoteExecutorForStream)(nil),                 // 19: tensorflow.eager.SyncRemoteExecutorForStream
	(*SendTensorOp)(nil),                                // 20: tensorflow.eager.SendTensorOp
	(*SendPackedHandleOp)(nil),                          // 21: tensorflow.eager.SendPackedHandleOp
	(*Operation_Input)(nil),                             // 22: tensorflow.eager.Operation.Input
	nil,                                                 // 23: tensorflow.eager.Operation.AttrsEntry
	(*SendPackedHandleOp_LocalTensorHandle)(nil),        // 24: tensorflow.eager.SendPackedHandleOp.LocalTensorHandle
	(*SendPackedHandleOp_Handle)(nil),                   // 25: tensorflow.eager.SendPackedHandleOp.Handle
	(*RemoteTensorHandle)(nil),                          // 26: tensorflow.eager.RemoteTensorHandle
	(*tensor_shape_go_proto.TensorShapeProto)(nil),      // 27: tensorflow.TensorShapeProto
	(*tensor_go_proto.TensorProto)(nil),                 // 28: tensorflow.TensorProto
	(*ServerDef)(nil),                                   // 29: tensorflow.ServerDef
	(*versions_go_proto.VersionDef)(nil),                // 30: tensorflow.VersionDef
	(*device_attributes_go_proto.DeviceAttributes)(nil), // 31: tensorflow.DeviceAttributes
	(*function_go_proto.FunctionDef)(nil),               // 32: tensorflow.FunctionDef
	(*function_go_proto.FunctionDefLibrary)(nil),        // 33: tensorflow.FunctionDefLibrary
	(*attr_value_go_proto.AttrValue)(nil),               // 34: tensorflow.AttrValue
}
var file_tensorflow_core_protobuf_eager_service_proto_depIdxs = []int32{
	22, // 0: tensorflow.eager.Operation.op_inputs:type_name -> tensorflow.eager.Operation.Input
	23, // 1: tensorflow.eager.Operation.attrs:type_name -> tensorflow.eager.Operation.AttrsEntry
	26, // 2: tensorflow.eager.QueueItem.handle_to_decref:type_name -> tensorflow.eager.RemoteTensorHandle
	0,  // 3: tensorflow.eager.QueueItem.operation:type_name -> tensorflow.eager.Operation
	20, // 4: tensorflow.eager.QueueItem.send_tensor:type_name -> tensorflow.eager.SendTensorOp
	17, // 5: tensorflow.eager.QueueItem.register_function:type_name -> tensorflow.eager.RegisterFunctionOp
	18, // 6: tensorflow.eager.QueueItem.cleanup_function:type_name -> tensorflow.eager.CleanupFunctionOp
	19, // 7: tensorflow.eager.QueueItem.sync_remote_executor_for_stream:type_name -> tensorflow.eager.SyncRemoteExecutorForStream
	21, // 8: tensorflow.eager.QueueItem.send_packed_handle:type_name -> tensorflow.eager.SendPackedHandleOp
	27, // 9: tensorflow.eager.QueueResponse.shape:type_name -> tensorflow.TensorShapeProto
	28, // 10: tensorflow.eager.QueueResponse.tensor:type_name -> tensorflow.TensorProto
	29, // 11: tensorflow.eager.CreateContextRequest.server_def:type_name -> tensorflow.ServerDef
	30, // 12: tensorflow.eager.CreateContextRequest.version_def:type_name -> tensorflow.VersionDef
	31, // 13: tensorflow.eager.CreateContextRequest.cluster_device_attributes:type_name -> tensorflow.DeviceAttributes
	31, // 14: tensorflow.eager.CreateContextResponse.device_attributes:type_name -> tensorflow.DeviceAttributes
	29, // 15: tensorflow.eager.UpdateContextRequest.server_def:type_name -> tensorflow.ServerDef
	31, // 16: tensorflow.eager.UpdateContextRequest.cluster_device_attributes:type_name -> tensorflow.DeviceAttributes
	31, // 17: tensorflow.eager.UpdateContextResponse.device_attributes:type_name -> tensorflow.DeviceAttributes
	1,  // 18: tensorflow.eager.EnqueueRequest.queue:type_name -> tensorflow.eager.QueueItem
	2,  // 19: tensorflow.eager.EnqueueResponse.queue_response:type_name -> tensorflow.eager.QueueResponse
	0,  // 20: tensorflow.eager.RunComponentFunctionRequest.operation:type_name -> tensorflow.eager.Operation
	27, // 21: tensorflow.eager.RunComponentFunctionResponse.shape:type_name -> tensorflow.TensorShapeProto
	28, // 22: tensorflow.eager.RunComponentFunctionResponse.tensor:type_name -> tensorflow.TensorProto
	32, // 23: tensorflow.eager.RegisterFunctionOp.function_def:type_name -> tensorflow.FunctionDef
	33, // 24: tensorflow.eager.RegisterFunctionOp.library:type_name -> tensorflow.FunctionDefLibrary
	28, // 25: tensorflow.eager.SendTensorOp.tensors:type_name -> tensorflow.TensorProto
	25, // 26: tensorflow.eager.SendPackedHandleOp.handles:type_name -> tensorflow.eager.SendPackedHandleOp.Handle
	26, // 27: tensorflow.eager.Operation.Input.remote_handle:type_name -> tensorflow.eager.RemoteTensorHandle
	28, // 28: tensorflow.eager.Operation.Input.tensor:type_name -> tensorflow.TensorProto
	34, // 29: tensorflow.eager.Operation.AttrsEntry.value:type_name -> tensorflow.AttrValue
	28, // 30: tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.tensor:type_name -> tensorflow.TensorProto
	24, // 31: tensorflow.eager.SendPackedHandleOp.Handle.local_handle:type_name -> tensorflow.eager.SendPackedHandleOp.LocalTensorHandle
	26, // 32: tensorflow.eager.SendPackedHandleOp.Handle.remote_handle:type_name -> tensorflow.eager.RemoteTensorHandle
	3,  // 33: tensorflow.eager.EagerService.CreateContext:input_type -> tensorflow.eager.CreateContextRequest
	5,  // 34: tensorflow.eager.EagerService.UpdateContext:input_type -> tensorflow.eager.UpdateContextRequest
	7,  // 35: tensorflow.eager.EagerService.Enqueue:input_type -> tensorflow.eager.EnqueueRequest
	7,  // 36: tensorflow.eager.EagerService.StreamingEnqueue:input_type -> tensorflow.eager.EnqueueRequest
	9,  // 37: tensorflow.eager.EagerService.WaitQueueDone:input_type -> tensorflow.eager.WaitQueueDoneRequest
	11, // 38: tensorflow.eager.EagerService.RunComponentFunction:input_type -> tensorflow.eager.RunComponentFunctionRequest
	13, // 39: tensorflow.eager.EagerService.KeepAlive:input_type -> tensorflow.eager.KeepAliveRequest
	15, // 40: tensorflow.eager.EagerService.CloseContext:input_type -> tensorflow.eager.CloseContextRequest
	4,  // 41: tensorflow.eager.EagerService.CreateContext:output_type -> tensorflow.eager.CreateContextResponse
	6,  // 42: tensorflow.eager.EagerService.UpdateContext:output_type -> tensorflow.eager.UpdateContextResponse
	8,  // 43: tensorflow.eager.EagerService.Enqueue:output_type -> tensorflow.eager.EnqueueResponse
	8,  // 44: tensorflow.eager.EagerService.StreamingEnqueue:output_type -> tensorflow.eager.EnqueueResponse
	10, // 45: tensorflow.eager.EagerService.WaitQueueDone:output_type -> tensorflow.eager.WaitQueueDoneResponse
	12, // 46: tensorflow.eager.EagerService.RunComponentFunction:output_type -> tensorflow.eager.RunComponentFunctionResponse
	14, // 47: tensorflow.eager.EagerService.KeepAlive:output_type -> tensorflow.eager.KeepAliveResponse
	16, // 48: tensorflow.eager.EagerService.CloseContext:output_type -> tensorflow.eager.CloseContextResponse
	41, // [41:49] is the sub-list for method output_type
	33, // [33:41] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	33, // [33:33] is the sub-list for extension extendee
	0,  // [0:33] is the sub-list for field type_name
}

func init() { file_tensorflow_core_protobuf_eager_service_proto_init() }
func file_tensorflow_core_protobuf_eager_service_proto_init() {
	if File_tensorflow_core_protobuf_eager_service_proto != nil {
		return
	}
	file_tensorflow_core_protobuf_remote_tensor_handle_proto_init()
	file_tensorflow_core_protobuf_tensorflow_server_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Operation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueueItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueueResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateContextRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateContextResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateContextRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateContextResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnqueueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnqueueResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaitQueueDoneRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaitQueueDoneResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunComponentFunctionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunComponentFunctionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeepAliveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeepAliveResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseContextRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseContextResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterFunctionOp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CleanupFunctionOp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncRemoteExecutorForStream); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendTensorOp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendPackedHandleOp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Operation_Input); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendPackedHandleOp_LocalTensorHandle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_protobuf_eager_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendPackedHandleOp_Handle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_tensorflow_core_protobuf_eager_service_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*QueueItem_HandleToDecref)(nil),
		(*QueueItem_Operation)(nil),
		(*QueueItem_SendTensor)(nil),
		(*QueueItem_RegisterFunction)(nil),
		(*QueueItem_CleanupFunction)(nil),
		(*QueueItem_SyncRemoteExecutorForStream)(nil),
		(*QueueItem_SendPackedHandle)(nil),
	}
	file_tensorflow_core_protobuf_eager_service_proto_msgTypes[22].OneofWrappers = []interface{}{
		(*Operation_Input_RemoteHandle)(nil),
		(*Operation_Input_Tensor)(nil),
	}
	file_tensorflow_core_protobuf_eager_service_proto_msgTypes[25].OneofWrappers = []interface{}{
		(*SendPackedHandleOp_Handle_LocalHandle)(nil),
		(*SendPackedHandleOp_Handle_RemoteHandle)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tensorflow_core_protobuf_eager_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tensorflow_core_protobuf_eager_service_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_protobuf_eager_service_proto_depIdxs,
		MessageInfos:      file_tensorflow_core_protobuf_eager_service_proto_msgTypes,
	}.Build()
	File_tensorflow_core_protobuf_eager_service_proto = out.File
	file_tensorflow_core_protobuf_eager_service_proto_rawDesc = nil
	file_tensorflow_core_protobuf_eager_service_proto_goTypes = nil
	file_tensorflow_core_protobuf_eager_service_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// EagerServiceClient is the client API for EagerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type EagerServiceClient interface {
	// This initializes the worker, informing it about the other workers in the
	// cluster and exchanging authentication tokens which will be used in all
	// other RPCs to detect whether the worker has restarted.
	CreateContext(ctx context.Context, in *CreateContextRequest, opts ...grpc.CallOption) (*CreateContextResponse, error)
	// This updates the eager context on an existing worker when updating the set
	// of servers in a distributed eager cluster.
	UpdateContext(ctx context.Context, in *UpdateContextRequest, opts ...grpc.CallOption) (*UpdateContextResponse, error)
	// This takes a list of Execute and DeleteTensorHandle operations and enqueues
	// (in async mode) or executes (in sync mode) them on the remote server.
	// All outputs of ops which were not explicitly deleted with
	// DeleteTensorHandle entries will be assumed to be alive and are usable by
	// future calls to Enqueue.
	Enqueue(ctx context.Context, in *EnqueueRequest, opts ...grpc.CallOption) (*EnqueueResponse, error)
	// A streaming version of Enqueue.
	// Current server implementation sends one response per received request.
	// The benefit for using a streaming version is that subsequent requests
	// can be sent without waiting for a response to the previous request. This
	// synchronization is required in the regular Enqueue call because gRPC does
	// not guarantee to preserve request order.
	StreamingEnqueue(ctx context.Context, opts ...grpc.CallOption) (EagerService_StreamingEnqueueClient, error)
	// Takes a set of op IDs and waits until those ops are done. Returns any error
	// in the stream so far.
	WaitQueueDone(ctx context.Context, in *WaitQueueDoneRequest, opts ...grpc.CallOption) (*WaitQueueDoneResponse, error)
	// This takes an Eager operation and executes it in async mode on the remote
	// server. Different from EnqueueRequest, ops/functions sent through this
	// type of requests are allowed to execute in parallel and no ordering is
	// preserved by RPC stream or executor.
	// This request type should only be used for executing component functions.
	// Ordering of component functions should be enforced by their corresponding
	// main functions. The runtime ensures the following invarients for component
	// functions (CFs) and their main functions (MFs):
	// (1) MF1 -> MF2 ==> CF1 -> CF2 ("->" indicates order of execution);
	// (2) MF1 || MF2 ==> CF1 || CF2 ("||" indicates possible parallel execution);
	// (3) For CF1 and CF2 that come from the same MF, CF1 || CF2
	// For executing ops/main functions, use Enqueue or StreamingEnqueue instead
	// for correct ordering.
	RunComponentFunction(ctx context.Context, in *RunComponentFunctionRequest, opts ...grpc.CallOption) (*RunComponentFunctionResponse, error)
	// Contexts are always created with a deadline and no RPCs within a deadline
	// will trigger a context garbage collection. KeepAlive calls can be used to
	// delay this. It can also be used to validate the existence of a context ID
	// on remote eager worker. If the context is on remote worker, return the same
	// ID and the current context view ID. This is useful for checking if the
	// remote worker (potentially with the same task name and hostname / port) is
	// replaced with a new process.
	KeepAlive(ctx context.Context, in *KeepAliveRequest, opts ...grpc.CallOption) (*KeepAliveResponse, error)
	// Closes the context. No calls to other methods using the existing context ID
	// are valid after this.
	CloseContext(ctx context.Context, in *CloseContextRequest, opts ...grpc.CallOption) (*CloseContextResponse, error)
}

type eagerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEagerServiceClient(cc grpc.ClientConnInterface) EagerServiceClient {
	return &eagerServiceClient{cc}
}

func (c *eagerServiceClient) CreateContext(ctx context.Context, in *CreateContextRequest, opts ...grpc.CallOption) (*CreateContextResponse, error) {
	out := new(CreateContextResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.eager.EagerService/CreateContext", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eagerServiceClient) UpdateContext(ctx context.Context, in *UpdateContextRequest, opts ...grpc.CallOption) (*UpdateContextResponse, error) {
	out := new(UpdateContextResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.eager.EagerService/UpdateContext", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eagerServiceClient) Enqueue(ctx context.Context, in *EnqueueRequest, opts ...grpc.CallOption) (*EnqueueResponse, error) {
	out := new(EnqueueResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.eager.EagerService/Enqueue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eagerServiceClient) StreamingEnqueue(ctx context.Context, opts ...grpc.CallOption) (EagerService_StreamingEnqueueClient, error) {
	stream, err := c.cc.NewStream(ctx, &_EagerService_serviceDesc.Streams[0], "/tensorflow.eager.EagerService/StreamingEnqueue", opts...)
	if err != nil {
		return nil, err
	}
	x := &eagerServiceStreamingEnqueueClient{stream}
	return x, nil
}

type EagerService_StreamingEnqueueClient interface {
	Send(*EnqueueRequest) error
	Recv() (*EnqueueResponse, error)
	grpc.ClientStream
}

type eagerServiceStreamingEnqueueClient struct {
	grpc.ClientStream
}

func (x *eagerServiceStreamingEnqueueClient) Send(m *EnqueueRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *eagerServiceStreamingEnqueueClient) Recv() (*EnqueueResponse, error) {
	m := new(EnqueueResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *eagerServiceClient) WaitQueueDone(ctx context.Context, in *WaitQueueDoneRequest, opts ...grpc.CallOption) (*WaitQueueDoneResponse, error) {
	out := new(WaitQueueDoneResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.eager.EagerService/WaitQueueDone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eagerServiceClient) RunComponentFunction(ctx context.Context, in *RunComponentFunctionRequest, opts ...grpc.CallOption) (*RunComponentFunctionResponse, error) {
	out := new(RunComponentFunctionResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.eager.EagerService/RunComponentFunction", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eagerServiceClient) KeepAlive(ctx context.Context, in *KeepAliveRequest, opts ...grpc.CallOption) (*KeepAliveResponse, error) {
	out := new(KeepAliveResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.eager.EagerService/KeepAlive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eagerServiceClient) CloseContext(ctx context.Context, in *CloseContextRequest, opts ...grpc.CallOption) (*CloseContextResponse, error) {
	out := new(CloseContextResponse)
	err := c.cc.Invoke(ctx, "/tensorflow.eager.EagerService/CloseContext", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EagerServiceServer is the server API for EagerService service.
type EagerServiceServer interface {
	// This initializes the worker, informing it about the other workers in the
	// cluster and exchanging authentication tokens which will be used in all
	// other RPCs to detect whether the worker has restarted.
	CreateContext(context.Context, *CreateContextRequest) (*CreateContextResponse, error)
	// This updates the eager context on an existing worker when updating the set
	// of servers in a distributed eager cluster.
	UpdateContext(context.Context, *UpdateContextRequest) (*UpdateContextResponse, error)
	// This takes a list of Execute and DeleteTensorHandle operations and enqueues
	// (in async mode) or executes (in sync mode) them on the remote server.
	// All outputs of ops which were not explicitly deleted with
	// DeleteTensorHandle entries will be assumed to be alive and are usable by
	// future calls to Enqueue.
	Enqueue(context.Context, *EnqueueRequest) (*EnqueueResponse, error)
	// A streaming version of Enqueue.
	// Current server implementation sends one response per received request.
	// The benefit for using a streaming version is that subsequent requests
	// can be sent without waiting for a response to the previous request. This
	// synchronization is required in the regular Enqueue call because gRPC does
	// not guarantee to preserve request order.
	StreamingEnqueue(EagerService_StreamingEnqueueServer) error
	// Takes a set of op IDs and waits until those ops are done. Returns any error
	// in the stream so far.
	WaitQueueDone(context.Context, *WaitQueueDoneRequest) (*WaitQueueDoneResponse, error)
	// This takes an Eager operation and executes it in async mode on the remote
	// server. Different from EnqueueRequest, ops/functions sent through this
	// type of requests are allowed to execute in parallel and no ordering is
	// preserved by RPC stream or executor.
	// This request type should only be used for executing component functions.
	// Ordering of component functions should be enforced by their corresponding
	// main functions. The runtime ensures the following invarients for component
	// functions (CFs) and their main functions (MFs):
	// (1) MF1 -> MF2 ==> CF1 -> CF2 ("->" indicates order of execution);
	// (2) MF1 || MF2 ==> CF1 || CF2 ("||" indicates possible parallel execution);
	// (3) For CF1 and CF2 that come from the same MF, CF1 || CF2
	// For executing ops/main functions, use Enqueue or StreamingEnqueue instead
	// for correct ordering.
	RunComponentFunction(context.Context, *RunComponentFunctionRequest) (*RunComponentFunctionResponse, error)
	// Contexts are always created with a deadline and no RPCs within a deadline
	// will trigger a context garbage collection. KeepAlive calls can be used to
	// delay this. It can also be used to validate the existence of a context ID
	// on remote eager worker. If the context is on remote worker, return the same
	// ID and the current context view ID. This is useful for checking if the
	// remote worker (potentially with the same task name and hostname / port) is
	// replaced with a new process.
	KeepAlive(context.Context, *KeepAliveRequest) (*KeepAliveResponse, error)
	// Closes the context. No calls to other methods using the existing context ID
	// are valid after this.
	CloseContext(context.Context, *CloseContextRequest) (*CloseContextResponse, error)
}

// UnimplementedEagerServiceServer can be embedded to have forward compatible implementations.
type UnimplementedEagerServiceServer struct {
}

func (*UnimplementedEagerServiceServer) CreateContext(context.Context, *CreateContextRequest) (*CreateContextResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateContext not implemented")
}
func (*UnimplementedEagerServiceServer) UpdateContext(context.Context, *UpdateContextRequest) (*UpdateContextResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateContext not implemented")
}
func (*UnimplementedEagerServiceServer) Enqueue(context.Context, *EnqueueRequest) (*EnqueueResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Enqueue not implemented")
}
func (*UnimplementedEagerServiceServer) StreamingEnqueue(EagerService_StreamingEnqueueServer) error {
	return status.Errorf(codes.Unimplemented, "method StreamingEnqueue not implemented")
}
func (*UnimplementedEagerServiceServer) WaitQueueDone(context.Context, *WaitQueueDoneRequest) (*WaitQueueDoneResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WaitQueueDone not implemented")
}
func (*UnimplementedEagerServiceServer) RunComponentFunction(context.Context, *RunComponentFunctionRequest) (*RunComponentFunctionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunComponentFunction not implemented")
}
func (*UnimplementedEagerServiceServer) KeepAlive(context.Context, *KeepAliveRequest) (*KeepAliveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KeepAlive not implemented")
}
func (*UnimplementedEagerServiceServer) CloseContext(context.Context, *CloseContextRequest) (*CloseContextResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloseContext not implemented")
}

func RegisterEagerServiceServer(s *grpc.Server, srv EagerServiceServer) {
	s.RegisterService(&_EagerService_serviceDesc, srv)
}

func _EagerService_CreateContext_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateContextRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EagerServiceServer).CreateContext(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.eager.EagerService/CreateContext",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EagerServiceServer).CreateContext(ctx, req.(*CreateContextRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EagerService_UpdateContext_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateContextRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EagerServiceServer).UpdateContext(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.eager.EagerService/UpdateContext",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EagerServiceServer).UpdateContext(ctx, req.(*UpdateContextRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EagerService_Enqueue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnqueueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EagerServiceServer).Enqueue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.eager.EagerService/Enqueue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EagerServiceServer).Enqueue(ctx, req.(*EnqueueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EagerService_StreamingEnqueue_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(EagerServiceServer).StreamingEnqueue(&eagerServiceStreamingEnqueueServer{stream})
}

type EagerService_StreamingEnqueueServer interface {
	Send(*EnqueueResponse) error
	Recv() (*EnqueueRequest, error)
	grpc.ServerStream
}

type eagerServiceStreamingEnqueueServer struct {
	grpc.ServerStream
}

func (x *eagerServiceStreamingEnqueueServer) Send(m *EnqueueResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *eagerServiceStreamingEnqueueServer) Recv() (*EnqueueRequest, error) {
	m := new(EnqueueRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _EagerService_WaitQueueDone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WaitQueueDoneRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EagerServiceServer).WaitQueueDone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.eager.EagerService/WaitQueueDone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EagerServiceServer).WaitQueueDone(ctx, req.(*WaitQueueDoneRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EagerService_RunComponentFunction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunComponentFunctionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EagerServiceServer).RunComponentFunction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.eager.EagerService/RunComponentFunction",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EagerServiceServer).RunComponentFunction(ctx, req.(*RunComponentFunctionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EagerService_KeepAlive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KeepAliveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EagerServiceServer).KeepAlive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.eager.EagerService/KeepAlive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EagerServiceServer).KeepAlive(ctx, req.(*KeepAliveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EagerService_CloseContext_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseContextRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EagerServiceServer).CloseContext(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tensorflow.eager.EagerService/CloseContext",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EagerServiceServer).CloseContext(ctx, req.(*CloseContextRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _EagerService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "tensorflow.eager.EagerService",
	HandlerType: (*EagerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateContext",
			Handler:    _EagerService_CreateContext_Handler,
		},
		{
			MethodName: "UpdateContext",
			Handler:    _EagerService_UpdateContext_Handler,
		},
		{
			MethodName: "Enqueue",
			Handler:    _EagerService_Enqueue_Handler,
		},
		{
			MethodName: "WaitQueueDone",
			Handler:    _EagerService_WaitQueueDone_Handler,
		},
		{
			MethodName: "RunComponentFunction",
			Handler:    _EagerService_RunComponentFunction_Handler,
		},
		{
			MethodName: "KeepAlive",
			Handler:    _EagerService_KeepAlive_Handler,
		},
		{
			MethodName: "CloseContext",
			Handler:    _EagerService_CloseContext_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "StreamingEnqueue",
			Handler:       _EagerService_StreamingEnqueue_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "tensorflow/core/protobuf/eager_service.proto",
}
