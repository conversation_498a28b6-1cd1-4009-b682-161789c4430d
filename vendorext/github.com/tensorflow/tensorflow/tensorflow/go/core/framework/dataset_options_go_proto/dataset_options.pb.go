// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: tensorflow/core/framework/dataset_options.proto

package dataset_options_go_proto

import (
	model_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/model_go_proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents the type of auto-sharding we enable.
type AutoShardPolicy int32

const (
	// AUTO: Attempts FILE-based sharding, falling back to DATA-based sharding.
	AutoShardPolicy_AUTO AutoShardPolicy = 0
	// FILE: Shards by input files (i.e. each worker will get a set of files to
	// process). When this option is selected, make sure that there is at least as
	// many files as workers. If there are fewer input files than workers, a
	// runtime error will be raised.
	AutoShardPolicy_FILE AutoShardPolicy = 1
	// DATA: Shards by elements produced by the dataset. Each worker will process
	// the whole dataset and discard the portion that is not for itself. Note that
	// for this mode to correctly partitions the dataset elements, the dataset
	// needs to produce elements in a deterministic order.
	AutoShardPolicy_DATA AutoShardPolicy = 2
	// HINT: Looks for the presence of `shard(SHARD_HINT, ...)` which is treated
	// as a placeholder to replace with `shard(num_workers, worker_index)`.
	AutoShardPolicy_HINT AutoShardPolicy = 3
	// OFF: No sharding will be performed.
	AutoShardPolicy_OFF AutoShardPolicy = -1
)

// Enum value maps for AutoShardPolicy.
var (
	AutoShardPolicy_name = map[int32]string{
		0:  "AUTO",
		1:  "FILE",
		2:  "DATA",
		3:  "HINT",
		-1: "OFF",
	}
	AutoShardPolicy_value = map[string]int32{
		"AUTO": 0,
		"FILE": 1,
		"DATA": 2,
		"HINT": 3,
		"OFF":  -1,
	}
)

func (x AutoShardPolicy) Enum() *AutoShardPolicy {
	p := new(AutoShardPolicy)
	*p = x
	return p
}

func (x AutoShardPolicy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AutoShardPolicy) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_core_framework_dataset_options_proto_enumTypes[0].Descriptor()
}

func (AutoShardPolicy) Type() protoreflect.EnumType {
	return &file_tensorflow_core_framework_dataset_options_proto_enumTypes[0]
}

func (x AutoShardPolicy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AutoShardPolicy.Descriptor instead.
func (AutoShardPolicy) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_dataset_options_proto_rawDescGZIP(), []int{0}
}

// Represents how to handle external state during serialization.
type ExternalStatePolicy int32

const (
	ExternalStatePolicy_POLICY_WARN   ExternalStatePolicy = 0
	ExternalStatePolicy_POLICY_IGNORE ExternalStatePolicy = 1
	ExternalStatePolicy_POLICY_FAIL   ExternalStatePolicy = 2
)

// Enum value maps for ExternalStatePolicy.
var (
	ExternalStatePolicy_name = map[int32]string{
		0: "POLICY_WARN",
		1: "POLICY_IGNORE",
		2: "POLICY_FAIL",
	}
	ExternalStatePolicy_value = map[string]int32{
		"POLICY_WARN":   0,
		"POLICY_IGNORE": 1,
		"POLICY_FAIL":   2,
	}
)

func (x ExternalStatePolicy) Enum() *ExternalStatePolicy {
	p := new(ExternalStatePolicy)
	*p = x
	return p
}

func (x ExternalStatePolicy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExternalStatePolicy) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_core_framework_dataset_options_proto_enumTypes[1].Descriptor()
}

func (ExternalStatePolicy) Type() protoreflect.EnumType {
	return &file_tensorflow_core_framework_dataset_options_proto_enumTypes[1]
}

func (x ExternalStatePolicy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExternalStatePolicy.Descriptor instead.
func (ExternalStatePolicy) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_dataset_options_proto_rawDescGZIP(), []int{1}
}

type CardinalityOptions_ComputeLevel int32

const (
	CardinalityOptions_CARDINALITY_COMPUTE_UNSPECIFIED CardinalityOptions_ComputeLevel = 0
	// Cardinality will only be computed if it can be determined in a cheap
	// manner (ie. without reading from file sources). If the cardinality would
	// be nontrivial to compute, Cardinality() will return UNKNOWN_CARDINALITY.
	CardinalityOptions_CARDINALITY_COMPUTE_LOW CardinalityOptions_ComputeLevel = 1
	// Moderate effort will be made to determine cardinality, such as reading
	// index data from source files. If significant work is needed to compute
	// cardinality (e.g. reading entire source file contents or executing user
	// defined functions), Cardinality() will return UNKNOWN_CARDINALITY.
	CardinalityOptions_CARDINALITY_COMPUTE_MODERATE CardinalityOptions_ComputeLevel = 2
)

// Enum value maps for CardinalityOptions_ComputeLevel.
var (
	CardinalityOptions_ComputeLevel_name = map[int32]string{
		0: "CARDINALITY_COMPUTE_UNSPECIFIED",
		1: "CARDINALITY_COMPUTE_LOW",
		2: "CARDINALITY_COMPUTE_MODERATE",
	}
	CardinalityOptions_ComputeLevel_value = map[string]int32{
		"CARDINALITY_COMPUTE_UNSPECIFIED": 0,
		"CARDINALITY_COMPUTE_LOW":         1,
		"CARDINALITY_COMPUTE_MODERATE":    2,
	}
)

func (x CardinalityOptions_ComputeLevel) Enum() *CardinalityOptions_ComputeLevel {
	p := new(CardinalityOptions_ComputeLevel)
	*p = x
	return p
}

func (x CardinalityOptions_ComputeLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardinalityOptions_ComputeLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_core_framework_dataset_options_proto_enumTypes[2].Descriptor()
}

func (CardinalityOptions_ComputeLevel) Type() protoreflect.EnumType {
	return &file_tensorflow_core_framework_dataset_options_proto_enumTypes[2]
}

func (x CardinalityOptions_ComputeLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardinalityOptions_ComputeLevel.Descriptor instead.
func (CardinalityOptions_ComputeLevel) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_dataset_options_proto_rawDescGZIP(), []int{1, 0}
}

// next: 5
type AutotuneOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether to automatically tune performance knobs.
	//
	// Types that are assignable to OptionalEnabled:
	//
	//	*AutotuneOptions_Enabled
	OptionalEnabled isAutotuneOptions_OptionalEnabled `protobuf_oneof:"optional_enabled"`
	// When autotuning is enabled (through autotune), determines the CPU budget to
	// use. Values greater than the number of schedulable CPU cores are allowed
	// but may result in CPU contention.
	//
	// Types that are assignable to OptionalCpuBudget:
	//
	//	*AutotuneOptions_CpuBudget
	OptionalCpuBudget isAutotuneOptions_OptionalCpuBudget `protobuf_oneof:"optional_cpu_budget"`
	// When autotuning is enabled (through autotune), determines the RAM budget to
	// use. Values greater than the available RAM in bytes may result in OOM. If
	// 0, defaults to half of the available RAM in bytes.
	//
	// Types that are assignable to OptionalRamBudget:
	//
	//	*AutotuneOptions_RamBudget
	OptionalRamBudget isAutotuneOptions_OptionalRamBudget `protobuf_oneof:"optional_ram_budget"`
	// When autotuning is enabled (through autotune), determines the algorithm to
	// use. If not explicitly set by user, autotuning will follow HILL_CLIMB
	// algorithm but has more flexibility to tune parameters more aggressively,
	// in which case the behavior is implementation specific and may change over
	// time.
	//
	// Types that are assignable to OptionalAutotuneAlgorithm:
	//
	//	*AutotuneOptions_AutotuneAlgorithm
	OptionalAutotuneAlgorithm isAutotuneOptions_OptionalAutotuneAlgorithm `protobuf_oneof:"optional_autotune_algorithm"`
}

func (x *AutotuneOptions) Reset() {
	*x = AutotuneOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_framework_dataset_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutotuneOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutotuneOptions) ProtoMessage() {}

func (x *AutotuneOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_framework_dataset_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutotuneOptions.ProtoReflect.Descriptor instead.
func (*AutotuneOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_dataset_options_proto_rawDescGZIP(), []int{0}
}

func (m *AutotuneOptions) GetOptionalEnabled() isAutotuneOptions_OptionalEnabled {
	if m != nil {
		return m.OptionalEnabled
	}
	return nil
}

func (x *AutotuneOptions) GetEnabled() bool {
	if x, ok := x.GetOptionalEnabled().(*AutotuneOptions_Enabled); ok {
		return x.Enabled
	}
	return false
}

func (m *AutotuneOptions) GetOptionalCpuBudget() isAutotuneOptions_OptionalCpuBudget {
	if m != nil {
		return m.OptionalCpuBudget
	}
	return nil
}

func (x *AutotuneOptions) GetCpuBudget() int32 {
	if x, ok := x.GetOptionalCpuBudget().(*AutotuneOptions_CpuBudget); ok {
		return x.CpuBudget
	}
	return 0
}

func (m *AutotuneOptions) GetOptionalRamBudget() isAutotuneOptions_OptionalRamBudget {
	if m != nil {
		return m.OptionalRamBudget
	}
	return nil
}

func (x *AutotuneOptions) GetRamBudget() int64 {
	if x, ok := x.GetOptionalRamBudget().(*AutotuneOptions_RamBudget); ok {
		return x.RamBudget
	}
	return 0
}

func (m *AutotuneOptions) GetOptionalAutotuneAlgorithm() isAutotuneOptions_OptionalAutotuneAlgorithm {
	if m != nil {
		return m.OptionalAutotuneAlgorithm
	}
	return nil
}

func (x *AutotuneOptions) GetAutotuneAlgorithm() model_go_proto.AutotuneAlgorithm {
	if x, ok := x.GetOptionalAutotuneAlgorithm().(*AutotuneOptions_AutotuneAlgorithm); ok {
		return x.AutotuneAlgorithm
	}
	return model_go_proto.AutotuneAlgorithm_DEFAULT
}

type isAutotuneOptions_OptionalEnabled interface {
	isAutotuneOptions_OptionalEnabled()
}

type AutotuneOptions_Enabled struct {
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3,oneof"`
}

func (*AutotuneOptions_Enabled) isAutotuneOptions_OptionalEnabled() {}

type isAutotuneOptions_OptionalCpuBudget interface {
	isAutotuneOptions_OptionalCpuBudget()
}

type AutotuneOptions_CpuBudget struct {
	CpuBudget int32 `protobuf:"varint,2,opt,name=cpu_budget,json=cpuBudget,proto3,oneof"`
}

func (*AutotuneOptions_CpuBudget) isAutotuneOptions_OptionalCpuBudget() {}

type isAutotuneOptions_OptionalRamBudget interface {
	isAutotuneOptions_OptionalRamBudget()
}

type AutotuneOptions_RamBudget struct {
	RamBudget int64 `protobuf:"varint,3,opt,name=ram_budget,json=ramBudget,proto3,oneof"`
}

func (*AutotuneOptions_RamBudget) isAutotuneOptions_OptionalRamBudget() {}

type isAutotuneOptions_OptionalAutotuneAlgorithm interface {
	isAutotuneOptions_OptionalAutotuneAlgorithm()
}

type AutotuneOptions_AutotuneAlgorithm struct {
	AutotuneAlgorithm model_go_proto.AutotuneAlgorithm `protobuf:"varint,4,opt,name=autotune_algorithm,json=autotuneAlgorithm,proto3,enum=tensorflow.data.model.AutotuneAlgorithm,oneof"`
}

func (*AutotuneOptions_AutotuneAlgorithm) isAutotuneOptions_OptionalAutotuneAlgorithm() {}

// next: 2
type CardinalityOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComputeLevel CardinalityOptions_ComputeLevel `protobuf:"varint,1,opt,name=compute_level,json=computeLevel,proto3,enum=tensorflow.data.CardinalityOptions_ComputeLevel" json:"compute_level,omitempty"`
}

func (x *CardinalityOptions) Reset() {
	*x = CardinalityOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_framework_dataset_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardinalityOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardinalityOptions) ProtoMessage() {}

func (x *CardinalityOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_framework_dataset_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardinalityOptions.ProtoReflect.Descriptor instead.
func (*CardinalityOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_dataset_options_proto_rawDescGZIP(), []int{1}
}

func (x *CardinalityOptions) GetComputeLevel() CardinalityOptions_ComputeLevel {
	if x != nil {
		return x.ComputeLevel
	}
	return CardinalityOptions_CARDINALITY_COMPUTE_UNSPECIFIED
}

// next: 3
type DistributeOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AutoShardPolicy AutoShardPolicy `protobuf:"varint,1,opt,name=auto_shard_policy,json=autoShardPolicy,proto3,enum=tensorflow.data.AutoShardPolicy" json:"auto_shard_policy,omitempty"`
	// The number of devices attached to this input pipeline.
	//
	// Types that are assignable to OptionalNumDevices:
	//
	//	*DistributeOptions_NumDevices
	OptionalNumDevices isDistributeOptions_OptionalNumDevices `protobuf_oneof:"optional_num_devices"`
}

func (x *DistributeOptions) Reset() {
	*x = DistributeOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_framework_dataset_options_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DistributeOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistributeOptions) ProtoMessage() {}

func (x *DistributeOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_framework_dataset_options_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistributeOptions.ProtoReflect.Descriptor instead.
func (*DistributeOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_dataset_options_proto_rawDescGZIP(), []int{2}
}

func (x *DistributeOptions) GetAutoShardPolicy() AutoShardPolicy {
	if x != nil {
		return x.AutoShardPolicy
	}
	return AutoShardPolicy_AUTO
}

func (m *DistributeOptions) GetOptionalNumDevices() isDistributeOptions_OptionalNumDevices {
	if m != nil {
		return m.OptionalNumDevices
	}
	return nil
}

func (x *DistributeOptions) GetNumDevices() int32 {
	if x, ok := x.GetOptionalNumDevices().(*DistributeOptions_NumDevices); ok {
		return x.NumDevices
	}
	return 0
}

type isDistributeOptions_OptionalNumDevices interface {
	isDistributeOptions_OptionalNumDevices()
}

type DistributeOptions_NumDevices struct {
	NumDevices int32 `protobuf:"varint,2,opt,name=num_devices,json=numDevices,proto3,oneof"`
}

func (*DistributeOptions_NumDevices) isDistributeOptions_OptionalNumDevices() {}

// next: 18
type OptimizationOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether to apply default graph optimizations. If False, only graph
	// optimizations that have been explicitly enabled will be applied.
	//
	// Types that are assignable to OptionalApplyDefaultOptimizations:
	//
	//	*OptimizationOptions_ApplyDefaultOptimizations
	OptionalApplyDefaultOptimizations isOptimizationOptions_OptionalApplyDefaultOptimizations `protobuf_oneof:"optional_apply_default_optimizations"`
	// Whether to fuse filter transformations.
	//
	// Types that are assignable to OptionalFilterFusion:
	//
	//	*OptimizationOptions_FilterFusion
	OptionalFilterFusion isOptimizationOptions_OptionalFilterFusion `protobuf_oneof:"optional_filter_fusion"`
	// Whether to fuse map and batch transformations.
	//
	// Types that are assignable to OptionalMapAndBatchFusion:
	//
	//	*OptimizationOptions_MapAndBatchFusion
	OptionalMapAndBatchFusion isOptimizationOptions_OptionalMapAndBatchFusion `protobuf_oneof:"optional_map_and_batch_fusion"`
	// Whether to fuse map and filter transformations.
	//
	// Types that are assignable to OptionalMapAndFilterFusion:
	//
	//	*OptimizationOptions_MapAndFilterFusion
	OptionalMapAndFilterFusion isOptimizationOptions_OptionalMapAndFilterFusion `protobuf_oneof:"optional_map_and_filter_fusion"`
	// Whether to fuse map transformations.
	//
	// Types that are assignable to OptionalMapFusion:
	//
	//	*OptimizationOptions_MapFusion
	OptionalMapFusion isOptimizationOptions_OptionalMapFusion `protobuf_oneof:"optional_map_fusion"`
	// Whether to parallelize stateless map transformations.
	//
	// Types that are assignable to OptionalMapParallelization:
	//
	//	*OptimizationOptions_MapParallelization
	OptionalMapParallelization isOptimizationOptions_OptionalMapParallelization `protobuf_oneof:"optional_map_parallelization"`
	// Whether to eliminate no-op transformations.
	//
	// Types that are assignable to OptionalNoopElimination:
	//
	//	*OptimizationOptions_NoopElimination
	OptionalNoopElimination isOptimizationOptions_OptionalNoopElimination `protobuf_oneof:"optional_noop_elimination"`
	// Whether to parallelize copying of batch elements. This optimization is
	// highly experimental and can cause performance degradation (e.g. when the
	// parallelization overhead exceeds the benefits of performing the data copies
	// in parallel). You should only enable this optimization if a) your input
	// pipeline is bottlenecked on batching and b) you have validated that this
	// optimization improves performance.
	//
	// Types that are assignable to OptionalParallelBatch:
	//
	//	*OptimizationOptions_ParallelBatch
	OptionalParallelBatch isOptimizationOptions_OptionalParallelBatch `protobuf_oneof:"optional_parallel_batch"`
	// Whether to fuse shuffle and repeat transformations.
	//
	// Types that are assignable to OptionalShuffleAndRepeatFusion:
	//
	//	*OptimizationOptions_ShuffleAndRepeatFusion
	OptionalShuffleAndRepeatFusion isOptimizationOptions_OptionalShuffleAndRepeatFusion `protobuf_oneof:"optional_shuffle_and_repeat_fusion"`
}

func (x *OptimizationOptions) Reset() {
	*x = OptimizationOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_framework_dataset_options_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OptimizationOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptimizationOptions) ProtoMessage() {}

func (x *OptimizationOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_framework_dataset_options_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptimizationOptions.ProtoReflect.Descriptor instead.
func (*OptimizationOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_dataset_options_proto_rawDescGZIP(), []int{3}
}

func (m *OptimizationOptions) GetOptionalApplyDefaultOptimizations() isOptimizationOptions_OptionalApplyDefaultOptimizations {
	if m != nil {
		return m.OptionalApplyDefaultOptimizations
	}
	return nil
}

func (x *OptimizationOptions) GetApplyDefaultOptimizations() bool {
	if x, ok := x.GetOptionalApplyDefaultOptimizations().(*OptimizationOptions_ApplyDefaultOptimizations); ok {
		return x.ApplyDefaultOptimizations
	}
	return false
}

func (m *OptimizationOptions) GetOptionalFilterFusion() isOptimizationOptions_OptionalFilterFusion {
	if m != nil {
		return m.OptionalFilterFusion
	}
	return nil
}

func (x *OptimizationOptions) GetFilterFusion() bool {
	if x, ok := x.GetOptionalFilterFusion().(*OptimizationOptions_FilterFusion); ok {
		return x.FilterFusion
	}
	return false
}

func (m *OptimizationOptions) GetOptionalMapAndBatchFusion() isOptimizationOptions_OptionalMapAndBatchFusion {
	if m != nil {
		return m.OptionalMapAndBatchFusion
	}
	return nil
}

func (x *OptimizationOptions) GetMapAndBatchFusion() bool {
	if x, ok := x.GetOptionalMapAndBatchFusion().(*OptimizationOptions_MapAndBatchFusion); ok {
		return x.MapAndBatchFusion
	}
	return false
}

func (m *OptimizationOptions) GetOptionalMapAndFilterFusion() isOptimizationOptions_OptionalMapAndFilterFusion {
	if m != nil {
		return m.OptionalMapAndFilterFusion
	}
	return nil
}

func (x *OptimizationOptions) GetMapAndFilterFusion() bool {
	if x, ok := x.GetOptionalMapAndFilterFusion().(*OptimizationOptions_MapAndFilterFusion); ok {
		return x.MapAndFilterFusion
	}
	return false
}

func (m *OptimizationOptions) GetOptionalMapFusion() isOptimizationOptions_OptionalMapFusion {
	if m != nil {
		return m.OptionalMapFusion
	}
	return nil
}

func (x *OptimizationOptions) GetMapFusion() bool {
	if x, ok := x.GetOptionalMapFusion().(*OptimizationOptions_MapFusion); ok {
		return x.MapFusion
	}
	return false
}

func (m *OptimizationOptions) GetOptionalMapParallelization() isOptimizationOptions_OptionalMapParallelization {
	if m != nil {
		return m.OptionalMapParallelization
	}
	return nil
}

func (x *OptimizationOptions) GetMapParallelization() bool {
	if x, ok := x.GetOptionalMapParallelization().(*OptimizationOptions_MapParallelization); ok {
		return x.MapParallelization
	}
	return false
}

func (m *OptimizationOptions) GetOptionalNoopElimination() isOptimizationOptions_OptionalNoopElimination {
	if m != nil {
		return m.OptionalNoopElimination
	}
	return nil
}

func (x *OptimizationOptions) GetNoopElimination() bool {
	if x, ok := x.GetOptionalNoopElimination().(*OptimizationOptions_NoopElimination); ok {
		return x.NoopElimination
	}
	return false
}

func (m *OptimizationOptions) GetOptionalParallelBatch() isOptimizationOptions_OptionalParallelBatch {
	if m != nil {
		return m.OptionalParallelBatch
	}
	return nil
}

func (x *OptimizationOptions) GetParallelBatch() bool {
	if x, ok := x.GetOptionalParallelBatch().(*OptimizationOptions_ParallelBatch); ok {
		return x.ParallelBatch
	}
	return false
}

func (m *OptimizationOptions) GetOptionalShuffleAndRepeatFusion() isOptimizationOptions_OptionalShuffleAndRepeatFusion {
	if m != nil {
		return m.OptionalShuffleAndRepeatFusion
	}
	return nil
}

func (x *OptimizationOptions) GetShuffleAndRepeatFusion() bool {
	if x, ok := x.GetOptionalShuffleAndRepeatFusion().(*OptimizationOptions_ShuffleAndRepeatFusion); ok {
		return x.ShuffleAndRepeatFusion
	}
	return false
}

type isOptimizationOptions_OptionalApplyDefaultOptimizations interface {
	isOptimizationOptions_OptionalApplyDefaultOptimizations()
}

type OptimizationOptions_ApplyDefaultOptimizations struct {
	ApplyDefaultOptimizations bool `protobuf:"varint,1,opt,name=apply_default_optimizations,json=applyDefaultOptimizations,proto3,oneof"`
}

func (*OptimizationOptions_ApplyDefaultOptimizations) isOptimizationOptions_OptionalApplyDefaultOptimizations() {
}

type isOptimizationOptions_OptionalFilterFusion interface {
	isOptimizationOptions_OptionalFilterFusion()
}

type OptimizationOptions_FilterFusion struct {
	FilterFusion bool `protobuf:"varint,6,opt,name=filter_fusion,json=filterFusion,proto3,oneof"`
}

func (*OptimizationOptions_FilterFusion) isOptimizationOptions_OptionalFilterFusion() {}

type isOptimizationOptions_OptionalMapAndBatchFusion interface {
	isOptimizationOptions_OptionalMapAndBatchFusion()
}

type OptimizationOptions_MapAndBatchFusion struct {
	MapAndBatchFusion bool `protobuf:"varint,9,opt,name=map_and_batch_fusion,json=mapAndBatchFusion,proto3,oneof"`
}

func (*OptimizationOptions_MapAndBatchFusion) isOptimizationOptions_OptionalMapAndBatchFusion() {}

type isOptimizationOptions_OptionalMapAndFilterFusion interface {
	isOptimizationOptions_OptionalMapAndFilterFusion()
}

type OptimizationOptions_MapAndFilterFusion struct {
	MapAndFilterFusion bool `protobuf:"varint,10,opt,name=map_and_filter_fusion,json=mapAndFilterFusion,proto3,oneof"`
}

func (*OptimizationOptions_MapAndFilterFusion) isOptimizationOptions_OptionalMapAndFilterFusion() {}

type isOptimizationOptions_OptionalMapFusion interface {
	isOptimizationOptions_OptionalMapFusion()
}

type OptimizationOptions_MapFusion struct {
	MapFusion bool `protobuf:"varint,11,opt,name=map_fusion,json=mapFusion,proto3,oneof"`
}

func (*OptimizationOptions_MapFusion) isOptimizationOptions_OptionalMapFusion() {}

type isOptimizationOptions_OptionalMapParallelization interface {
	isOptimizationOptions_OptionalMapParallelization()
}

type OptimizationOptions_MapParallelization struct {
	MapParallelization bool `protobuf:"varint,12,opt,name=map_parallelization,json=mapParallelization,proto3,oneof"`
}

func (*OptimizationOptions_MapParallelization) isOptimizationOptions_OptionalMapParallelization() {}

type isOptimizationOptions_OptionalNoopElimination interface {
	isOptimizationOptions_OptionalNoopElimination()
}

type OptimizationOptions_NoopElimination struct {
	NoopElimination bool `protobuf:"varint,14,opt,name=noop_elimination,json=noopElimination,proto3,oneof"`
}

func (*OptimizationOptions_NoopElimination) isOptimizationOptions_OptionalNoopElimination() {}

type isOptimizationOptions_OptionalParallelBatch interface {
	isOptimizationOptions_OptionalParallelBatch()
}

type OptimizationOptions_ParallelBatch struct {
	ParallelBatch bool `protobuf:"varint,15,opt,name=parallel_batch,json=parallelBatch,proto3,oneof"`
}

func (*OptimizationOptions_ParallelBatch) isOptimizationOptions_OptionalParallelBatch() {}

type isOptimizationOptions_OptionalShuffleAndRepeatFusion interface {
	isOptimizationOptions_OptionalShuffleAndRepeatFusion()
}

type OptimizationOptions_ShuffleAndRepeatFusion struct {
	ShuffleAndRepeatFusion bool `protobuf:"varint,17,opt,name=shuffle_and_repeat_fusion,json=shuffleAndRepeatFusion,proto3,oneof"`
}

func (*OptimizationOptions_ShuffleAndRepeatFusion) isOptimizationOptions_OptionalShuffleAndRepeatFusion() {
}

// next: 3
type ThreadingOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// If set, it overrides the maximum degree of intra-op parallelism.
	//
	// Types that are assignable to OptionalMaxIntraOpParallelism:
	//
	//	*ThreadingOptions_MaxIntraOpParallelism
	OptionalMaxIntraOpParallelism isThreadingOptions_OptionalMaxIntraOpParallelism `protobuf_oneof:"optional_max_intra_op_parallelism"`
	// If set, the dataset will use a private threadpool of the given size.
	//
	// Types that are assignable to OptionalPrivateThreadpoolSize:
	//
	//	*ThreadingOptions_PrivateThreadpoolSize
	OptionalPrivateThreadpoolSize isThreadingOptions_OptionalPrivateThreadpoolSize `protobuf_oneof:"optional_private_threadpool_size"`
}

func (x *ThreadingOptions) Reset() {
	*x = ThreadingOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_framework_dataset_options_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThreadingOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadingOptions) ProtoMessage() {}

func (x *ThreadingOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_framework_dataset_options_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadingOptions.ProtoReflect.Descriptor instead.
func (*ThreadingOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_dataset_options_proto_rawDescGZIP(), []int{4}
}

func (m *ThreadingOptions) GetOptionalMaxIntraOpParallelism() isThreadingOptions_OptionalMaxIntraOpParallelism {
	if m != nil {
		return m.OptionalMaxIntraOpParallelism
	}
	return nil
}

func (x *ThreadingOptions) GetMaxIntraOpParallelism() int32 {
	if x, ok := x.GetOptionalMaxIntraOpParallelism().(*ThreadingOptions_MaxIntraOpParallelism); ok {
		return x.MaxIntraOpParallelism
	}
	return 0
}

func (m *ThreadingOptions) GetOptionalPrivateThreadpoolSize() isThreadingOptions_OptionalPrivateThreadpoolSize {
	if m != nil {
		return m.OptionalPrivateThreadpoolSize
	}
	return nil
}

func (x *ThreadingOptions) GetPrivateThreadpoolSize() int32 {
	if x, ok := x.GetOptionalPrivateThreadpoolSize().(*ThreadingOptions_PrivateThreadpoolSize); ok {
		return x.PrivateThreadpoolSize
	}
	return 0
}

type isThreadingOptions_OptionalMaxIntraOpParallelism interface {
	isThreadingOptions_OptionalMaxIntraOpParallelism()
}

type ThreadingOptions_MaxIntraOpParallelism struct {
	MaxIntraOpParallelism int32 `protobuf:"varint,1,opt,name=max_intra_op_parallelism,json=maxIntraOpParallelism,proto3,oneof"`
}

func (*ThreadingOptions_MaxIntraOpParallelism) isThreadingOptions_OptionalMaxIntraOpParallelism() {}

type isThreadingOptions_OptionalPrivateThreadpoolSize interface {
	isThreadingOptions_OptionalPrivateThreadpoolSize()
}

type ThreadingOptions_PrivateThreadpoolSize struct {
	PrivateThreadpoolSize int32 `protobuf:"varint,2,opt,name=private_threadpool_size,json=privateThreadpoolSize,proto3,oneof"`
}

func (*ThreadingOptions_PrivateThreadpoolSize) isThreadingOptions_OptionalPrivateThreadpoolSize() {}

// Message stored with Dataset objects to control how datasets are processed and
// optimized.
//
// next: 8
type Options struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether the outputs need to be produced in deterministic order.
	//
	// Types that are assignable to OptionalDeterministic:
	//
	//	*Options_Deterministic
	OptionalDeterministic isOptions_OptionalDeterministic `protobuf_oneof:"optional_deterministic"`
	// The distribution strategy options associated with the dataset.
	AutotuneOptions *AutotuneOptions `protobuf:"bytes,7,opt,name=autotune_options,json=autotuneOptions,proto3" json:"autotune_options,omitempty"`
	// The distribution strategy options associated with the dataset.
	DistributeOptions *DistributeOptions `protobuf:"bytes,2,opt,name=distribute_options,json=distributeOptions,proto3" json:"distribute_options,omitempty"`
	// The optimization options associated with the dataset.
	OptimizationOptions *OptimizationOptions `protobuf:"bytes,3,opt,name=optimization_options,json=optimizationOptions,proto3" json:"optimization_options,omitempty"`
	// Whether to introduce 'slack' in the last `prefetch` of the input pipeline,
	// if it exists. This may reduce CPU contention with accelerator host-side
	// activity at the start of a step. The slack frequency is determined by the
	// number of devices attached to this input pipeline.
	//
	// Types that are assignable to OptionalSlack:
	//
	//	*Options_Slack
	OptionalSlack isOptions_OptionalSlack `protobuf_oneof:"optional_slack"`
	// The threading options associated with the dataset.
	ThreadingOptions *ThreadingOptions `protobuf:"bytes,5,opt,name=threading_options,json=threadingOptions,proto3" json:"threading_options,omitempty"`
	// This option can be used to override the default policy for how to handle
	// external state when serializing a dataset or checkpointing its iterator.
	// There are three settings available - IGNORE: External state is ignored
	// without a warning; WARN: External state is ignored and a warning is logged;
	// FAIL: External state results in an error.
	//
	// Types that are assignable to OptionalExternalStatePolicy:
	//
	//	*Options_ExternalStatePolicy
	OptionalExternalStatePolicy isOptions_OptionalExternalStatePolicy `protobuf_oneof:"optional_external_state_policy"`
}

func (x *Options) Reset() {
	*x = Options{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_core_framework_dataset_options_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Options) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Options) ProtoMessage() {}

func (x *Options) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_framework_dataset_options_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Options.ProtoReflect.Descriptor instead.
func (*Options) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_dataset_options_proto_rawDescGZIP(), []int{5}
}

func (m *Options) GetOptionalDeterministic() isOptions_OptionalDeterministic {
	if m != nil {
		return m.OptionalDeterministic
	}
	return nil
}

func (x *Options) GetDeterministic() bool {
	if x, ok := x.GetOptionalDeterministic().(*Options_Deterministic); ok {
		return x.Deterministic
	}
	return false
}

func (x *Options) GetAutotuneOptions() *AutotuneOptions {
	if x != nil {
		return x.AutotuneOptions
	}
	return nil
}

func (x *Options) GetDistributeOptions() *DistributeOptions {
	if x != nil {
		return x.DistributeOptions
	}
	return nil
}

func (x *Options) GetOptimizationOptions() *OptimizationOptions {
	if x != nil {
		return x.OptimizationOptions
	}
	return nil
}

func (m *Options) GetOptionalSlack() isOptions_OptionalSlack {
	if m != nil {
		return m.OptionalSlack
	}
	return nil
}

func (x *Options) GetSlack() bool {
	if x, ok := x.GetOptionalSlack().(*Options_Slack); ok {
		return x.Slack
	}
	return false
}

func (x *Options) GetThreadingOptions() *ThreadingOptions {
	if x != nil {
		return x.ThreadingOptions
	}
	return nil
}

func (m *Options) GetOptionalExternalStatePolicy() isOptions_OptionalExternalStatePolicy {
	if m != nil {
		return m.OptionalExternalStatePolicy
	}
	return nil
}

func (x *Options) GetExternalStatePolicy() ExternalStatePolicy {
	if x, ok := x.GetOptionalExternalStatePolicy().(*Options_ExternalStatePolicy); ok {
		return x.ExternalStatePolicy
	}
	return ExternalStatePolicy_POLICY_WARN
}

type isOptions_OptionalDeterministic interface {
	isOptions_OptionalDeterministic()
}

type Options_Deterministic struct {
	Deterministic bool `protobuf:"varint,1,opt,name=deterministic,proto3,oneof"`
}

func (*Options_Deterministic) isOptions_OptionalDeterministic() {}

type isOptions_OptionalSlack interface {
	isOptions_OptionalSlack()
}

type Options_Slack struct {
	Slack bool `protobuf:"varint,4,opt,name=slack,proto3,oneof"`
}

func (*Options_Slack) isOptions_OptionalSlack() {}

type isOptions_OptionalExternalStatePolicy interface {
	isOptions_OptionalExternalStatePolicy()
}

type Options_ExternalStatePolicy struct {
	ExternalStatePolicy ExternalStatePolicy `protobuf:"varint,6,opt,name=external_state_policy,json=externalStatePolicy,proto3,enum=tensorflow.data.ExternalStatePolicy,oneof"`
}

func (*Options_ExternalStatePolicy) isOptions_OptionalExternalStatePolicy() {}

var File_tensorflow_core_framework_dataset_options_proto protoreflect.FileDescriptor

var file_tensorflow_core_framework_dataset_options_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x64, 0x61, 0x74, 0x61,
	0x73, 0x65, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x1a, 0x25, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63,
	0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xab, 0x02, 0x0a, 0x0f, 0x41, 0x75,
	0x74, 0x6f, 0x74, 0x75, 0x6e, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1a, 0x0a,
	0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00,
	0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0a, 0x63, 0x70, 0x75,
	0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52,
	0x09, 0x63, 0x70, 0x75, 0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x12, 0x1f, 0x0a, 0x0a, 0x72, 0x61,
	0x6d, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02,
	0x52, 0x09, 0x72, 0x61, 0x6d, 0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x12, 0x59, 0x0a, 0x12, 0x61,
	0x75, 0x74, 0x6f, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68,
	0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x41, 0x75, 0x74, 0x6f, 0x74, 0x75, 0x6e, 0x65, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68,
	0x6d, 0x48, 0x03, 0x52, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x74, 0x75, 0x6e, 0x65, 0x41, 0x6c, 0x67,
	0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x42, 0x12, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x42, 0x15, 0x0a, 0x13, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x70, 0x75, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65,
	0x74, 0x42, 0x15, 0x0a, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x61,
	0x6d, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x42, 0x1d, 0x0a, 0x1b, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x61, 0x6c,
	0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x22, 0xdf, 0x01, 0x0a, 0x12, 0x43, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x55,
	0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x6c,
	0x69, 0x74, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x75,
	0x74, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x72, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x41,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x41,
	0x52, 0x44, 0x49, 0x4e, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54,
	0x45, 0x5f, 0x4c, 0x4f, 0x57, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x52, 0x44, 0x49,
	0x4e, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x4d,
	0x4f, 0x44, 0x45, 0x52, 0x41, 0x54, 0x45, 0x10, 0x02, 0x22, 0x9c, 0x01, 0x0a, 0x11, 0x44, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x4c, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x75, 0x74,
	0x6f, 0x53, 0x68, 0x61, 0x72, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x0f, 0x61, 0x75,
	0x74, 0x6f, 0x53, 0x68, 0x61, 0x72, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x21, 0x0a,
	0x0b, 0x6e, 0x75, 0x6d, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x48, 0x00, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x42, 0x16, 0x0a, 0x14, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d,
	0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0x97, 0x06, 0x0a, 0x13, 0x4f, 0x70, 0x74,
	0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x40, 0x0a, 0x1b, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x19, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x25, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x66, 0x75, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x0c, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x46, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x14, 0x6d, 0x61, 0x70,
	0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x66, 0x75, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x11, 0x6d, 0x61, 0x70, 0x41, 0x6e,
	0x64, 0x42, 0x61, 0x74, 0x63, 0x68, 0x46, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x15,
	0x6d, 0x61, 0x70, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x66,
	0x75, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x12, 0x6d,
	0x61, 0x70, 0x41, 0x6e, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x46, 0x75, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x1f, 0x0a, 0x0a, 0x6d, 0x61, 0x70, 0x5f, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x08, 0x48, 0x04, 0x52, 0x09, 0x6d, 0x61, 0x70, 0x46, 0x75, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x13, 0x6d, 0x61, 0x70, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c,
	0x65, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x05, 0x52, 0x12, 0x6d, 0x61, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x10, 0x6e, 0x6f, 0x6f, 0x70, 0x5f, 0x65, 0x6c,
	0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x06, 0x52, 0x0f, 0x6e, 0x6f, 0x6f, 0x70, 0x45, 0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0e, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x5f, 0x62,
	0x61, 0x74, 0x63, 0x68, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x48, 0x07, 0x52, 0x0d, 0x70, 0x61,
	0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x3b, 0x0a, 0x19, 0x73,
	0x68, 0x75, 0x66, 0x66, 0x6c, 0x65, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x5f, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x48, 0x08,
	0x52, 0x16, 0x73, 0x68, 0x75, 0x66, 0x66, 0x6c, 0x65, 0x41, 0x6e, 0x64, 0x52, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x46, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x26, 0x0a, 0x24, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x42, 0x18, 0x0a, 0x16, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x5f, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x1f, 0x0a, 0x1d, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x62,
	0x61, 0x74, 0x63, 0x68, 0x5f, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x20, 0x0a, 0x1e, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x61, 0x6e, 0x64, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x15, 0x0a,
	0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x66, 0x75,
	0x73, 0x69, 0x6f, 0x6e, 0x42, 0x1e, 0x0a, 0x1c, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x1b, 0x0a, 0x19, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x6e, 0x6f, 0x6f, 0x70, 0x5f, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x19, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x61,
	0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x5f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x42, 0x24, 0x0a, 0x22,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x68, 0x75, 0x66, 0x66, 0x6c, 0x65,
	0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x66, 0x75, 0x73, 0x69,
	0x6f, 0x6e, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x4a, 0x04, 0x08, 0x03, 0x10, 0x04, 0x4a, 0x04,
	0x08, 0x04, 0x10, 0x05, 0x4a, 0x04, 0x08, 0x05, 0x10, 0x06, 0x4a, 0x04, 0x08, 0x07, 0x10, 0x08,
	0x4a, 0x04, 0x08, 0x08, 0x10, 0x09, 0x4a, 0x04, 0x08, 0x0d, 0x10, 0x0e, 0x4a, 0x04, 0x08, 0x10,
	0x10, 0x11, 0x22, 0xd0, 0x01, 0x0a, 0x10, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x39, 0x0a, 0x18, 0x6d, 0x61, 0x78, 0x5f, 0x69,
	0x6e, 0x74, 0x72, 0x61, 0x5f, 0x6f, 0x70, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c,
	0x69, 0x73, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x15, 0x6d, 0x61, 0x78,
	0x49, 0x6e, 0x74, 0x72, 0x61, 0x4f, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x69,
	0x73, 0x6d, 0x12, 0x38, 0x0a, 0x17, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x68,
	0x72, 0x65, 0x61, 0x64, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x15, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x54, 0x68,
	0x72, 0x65, 0x61, 0x64, 0x70, 0x6f, 0x6f, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x42, 0x23, 0x0a, 0x21,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x69, 0x6e, 0x74,
	0x72, 0x61, 0x5f, 0x6f, 0x70, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x69, 0x73,
	0x6d, 0x42, 0x22, 0x0a, 0x20, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x70, 0x6f, 0x6f, 0x6c,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x22, 0xbc, 0x04, 0x0a, 0x07, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x26, 0x0a, 0x0d, 0x64, 0x65, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0d, 0x64, 0x65, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x69, 0x73, 0x74, 0x69, 0x63, 0x12, 0x4b, 0x0a, 0x10, 0x61, 0x75, 0x74,
	0x6f, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x74, 0x75, 0x6e, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x74, 0x75, 0x6e, 0x65, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x51, 0x0a, 0x12, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x11, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x57, 0x0a, 0x14, 0x6f, 0x70, 0x74,
	0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x13, 0x6f,
	0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x16, 0x0a, 0x05, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x01, 0x52, 0x05, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x12, 0x4e, 0x0a, 0x11, 0x74, 0x68,
	0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e,
	0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x10, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64,
	0x69, 0x6e, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x5a, 0x0a, 0x15, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x48,
	0x02, 0x52, 0x13, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x42, 0x18, 0x0a, 0x16, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x42, 0x10, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x6c, 0x61,
	0x63, 0x6b, 0x42, 0x20, 0x0a, 0x1e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x2a, 0x4b, 0x0a, 0x0f, 0x41, 0x75, 0x74, 0x6f, 0x53, 0x68, 0x61, 0x72,
	0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x08, 0x0a, 0x04, 0x41, 0x55, 0x54, 0x4f, 0x10,
	0x00, 0x12, 0x08, 0x0a, 0x04, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x44,
	0x41, 0x54, 0x41, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x49, 0x4e, 0x54, 0x10, 0x03, 0x12,
	0x10, 0x0a, 0x03, 0x4f, 0x46, 0x46, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x01, 0x2a, 0x4a, 0x0a, 0x13, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x4f, 0x4c, 0x49,
	0x43, 0x59, 0x5f, 0x57, 0x41, 0x52, 0x4e, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x4f, 0x4c,
	0x49, 0x43, 0x59, 0x5f, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b,
	0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x42, 0x58, 0x5a,
	0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2f, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x67, 0x6f, 0x2f,
	0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x67,
	0x6f, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tensorflow_core_framework_dataset_options_proto_rawDescOnce sync.Once
	file_tensorflow_core_framework_dataset_options_proto_rawDescData = file_tensorflow_core_framework_dataset_options_proto_rawDesc
)

func file_tensorflow_core_framework_dataset_options_proto_rawDescGZIP() []byte {
	file_tensorflow_core_framework_dataset_options_proto_rawDescOnce.Do(func() {
		file_tensorflow_core_framework_dataset_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_tensorflow_core_framework_dataset_options_proto_rawDescData)
	})
	return file_tensorflow_core_framework_dataset_options_proto_rawDescData
}

var file_tensorflow_core_framework_dataset_options_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_tensorflow_core_framework_dataset_options_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_tensorflow_core_framework_dataset_options_proto_goTypes = []interface{}{
	(AutoShardPolicy)(0),                  // 0: tensorflow.data.AutoShardPolicy
	(ExternalStatePolicy)(0),              // 1: tensorflow.data.ExternalStatePolicy
	(CardinalityOptions_ComputeLevel)(0),  // 2: tensorflow.data.CardinalityOptions.ComputeLevel
	(*AutotuneOptions)(nil),               // 3: tensorflow.data.AutotuneOptions
	(*CardinalityOptions)(nil),            // 4: tensorflow.data.CardinalityOptions
	(*DistributeOptions)(nil),             // 5: tensorflow.data.DistributeOptions
	(*OptimizationOptions)(nil),           // 6: tensorflow.data.OptimizationOptions
	(*ThreadingOptions)(nil),              // 7: tensorflow.data.ThreadingOptions
	(*Options)(nil),                       // 8: tensorflow.data.Options
	(model_go_proto.AutotuneAlgorithm)(0), // 9: tensorflow.data.model.AutotuneAlgorithm
}
var file_tensorflow_core_framework_dataset_options_proto_depIdxs = []int32{
	9, // 0: tensorflow.data.AutotuneOptions.autotune_algorithm:type_name -> tensorflow.data.model.AutotuneAlgorithm
	2, // 1: tensorflow.data.CardinalityOptions.compute_level:type_name -> tensorflow.data.CardinalityOptions.ComputeLevel
	0, // 2: tensorflow.data.DistributeOptions.auto_shard_policy:type_name -> tensorflow.data.AutoShardPolicy
	3, // 3: tensorflow.data.Options.autotune_options:type_name -> tensorflow.data.AutotuneOptions
	5, // 4: tensorflow.data.Options.distribute_options:type_name -> tensorflow.data.DistributeOptions
	6, // 5: tensorflow.data.Options.optimization_options:type_name -> tensorflow.data.OptimizationOptions
	7, // 6: tensorflow.data.Options.threading_options:type_name -> tensorflow.data.ThreadingOptions
	1, // 7: tensorflow.data.Options.external_state_policy:type_name -> tensorflow.data.ExternalStatePolicy
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_tensorflow_core_framework_dataset_options_proto_init() }
func file_tensorflow_core_framework_dataset_options_proto_init() {
	if File_tensorflow_core_framework_dataset_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tensorflow_core_framework_dataset_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutotuneOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_framework_dataset_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardinalityOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_framework_dataset_options_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DistributeOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_framework_dataset_options_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OptimizationOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_framework_dataset_options_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThreadingOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_core_framework_dataset_options_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Options); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_tensorflow_core_framework_dataset_options_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*AutotuneOptions_Enabled)(nil),
		(*AutotuneOptions_CpuBudget)(nil),
		(*AutotuneOptions_RamBudget)(nil),
		(*AutotuneOptions_AutotuneAlgorithm)(nil),
	}
	file_tensorflow_core_framework_dataset_options_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*DistributeOptions_NumDevices)(nil),
	}
	file_tensorflow_core_framework_dataset_options_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*OptimizationOptions_ApplyDefaultOptimizations)(nil),
		(*OptimizationOptions_FilterFusion)(nil),
		(*OptimizationOptions_MapAndBatchFusion)(nil),
		(*OptimizationOptions_MapAndFilterFusion)(nil),
		(*OptimizationOptions_MapFusion)(nil),
		(*OptimizationOptions_MapParallelization)(nil),
		(*OptimizationOptions_NoopElimination)(nil),
		(*OptimizationOptions_ParallelBatch)(nil),
		(*OptimizationOptions_ShuffleAndRepeatFusion)(nil),
	}
	file_tensorflow_core_framework_dataset_options_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*ThreadingOptions_MaxIntraOpParallelism)(nil),
		(*ThreadingOptions_PrivateThreadpoolSize)(nil),
	}
	file_tensorflow_core_framework_dataset_options_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*Options_Deterministic)(nil),
		(*Options_Slack)(nil),
		(*Options_ExternalStatePolicy)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tensorflow_core_framework_dataset_options_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_core_framework_dataset_options_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_framework_dataset_options_proto_depIdxs,
		EnumInfos:         file_tensorflow_core_framework_dataset_options_proto_enumTypes,
		MessageInfos:      file_tensorflow_core_framework_dataset_options_proto_msgTypes,
	}.Build()
	File_tensorflow_core_framework_dataset_options_proto = out.File
	file_tensorflow_core_framework_dataset_options_proto_rawDesc = nil
	file_tensorflow_core_framework_dataset_options_proto_goTypes = nil
	file_tensorflow_core_framework_dataset_options_proto_depIdxs = nil
}
