#!/bin/bash

# Linux版本编译脚本 - 使用vendor模式

set -e

echo "=== 开始编译Linux版本 (vendor模式) ==="

# 项目信息
PROJECT_NAME="go-bid-gateway-tom"
BINARY_NAME="bid-gateway"
SIMPLE_PREDICT_BINARY="simple-predict"

# 构建目录
BUILD_DIR="build"
LINUX_BUILD_DIR="$BUILD_DIR/linux"

# 版本信息
VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "unknown")
BUILD_TIME=$(date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT=$(git rev-parse HEAD 2>/dev/null || echo "unknown")

# 编译标志
LDFLAGS="-ldflags \"-X main.Version=$VERSION -X main.BuildTime=$BUILD_TIME -X main.GitCommit=$GIT_COMMIT\""

echo "项目: $PROJECT_NAME"
echo "版本: $VERSION"
echo "构建时间: $BUILD_TIME"
echo "Git提交: $GIT_COMMIT"
echo ""

# 创建构建目录
echo "创建构建目录..."
mkdir -p "$LINUX_BUILD_DIR"

# 确保vendor目录存在
if [ ! -d "vendor" ]; then
    echo "vendor目录不存在，正在生成..."
    go mod vendor
fi

echo "使用vendor模式编译Linux版本..."

# 编译主服务
echo "编译主服务 ($BINARY_NAME)..."
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -mod=vendor -o "$LINUX_BUILD_DIR/$BINARY_NAME" ./cmd/server

# 编译简单预测工具
echo "编译简单预测工具 ($SIMPLE_PREDICT_BINARY)..."
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -mod=vendor -o "$LINUX_BUILD_DIR/$SIMPLE_PREDICT_BINARY" ./cmd/simple_predict

# 复制配置文件
echo "复制配置文件..."
cp -r config "$LINUX_BUILD_DIR/"

# 显示编译结果
echo ""
echo "=== 编译完成 ==="
echo "输出目录: $LINUX_BUILD_DIR"
echo "文件列表:"
ls -la "$LINUX_BUILD_DIR"

echo ""
echo "=== 使用方法 ==="
echo "1. 将整个 $LINUX_BUILD_DIR 目录上传到Linux服务器"
echo "2. 在Linux服务器上运行:"
echo "   chmod +x $LINUX_BUILD_DIR/$SIMPLE_PREDICT_BINARY"
echo "   ./$SIMPLE_PREDICT_BINARY -input=13792273858822192 -model=model -endpoint=localhost:8500"
echo ""
echo "参数说明:"
echo "  -input: intput0的输入值 (默认: 13792273858822192)"
echo "  -model: 模型名称 (默认: model)"
echo "  -endpoint: TensorFlow Serving地址 (默认: localhost:8500)"
echo "  -config: 配置文件路径 (默认: config/prod.yaml)"
echo ""
echo "示例命令:"
echo "  ./$SIMPLE_PREDICT_BINARY -input=13792273858822192 -model=model -endpoint=localhost:8500"
echo "  ./$SIMPLE_PREDICT_BINARY -input=999999999999999 -model=my_model -endpoint=*************:8500"
