// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: tensorflow_serving/config/logging_config.proto

package config

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SamplingConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Requests will be logged uniformly at random with this probability. Valid
	// range: [0, 1.0].
	SamplingRate float64 `protobuf:"fixed64,1,opt,name=sampling_rate,json=samplingRate,proto3" json:"sampling_rate,omitempty"`
}

func (x *SamplingConfig) Reset() {
	*x = SamplingConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_serving_config_logging_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SamplingConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SamplingConfig) ProtoMessage() {}

func (x *SamplingConfig) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_serving_config_logging_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SamplingConfig.ProtoReflect.Descriptor instead.
func (*SamplingConfig) Descriptor() ([]byte, []int) {
	return file_tensorflow_serving_config_logging_config_proto_rawDescGZIP(), []int{0}
}

func (x *SamplingConfig) GetSamplingRate() float64 {
	if x != nil {
		return x.SamplingRate
	}
	return 0
}

// Configuration for logging query/responses.
type LoggingConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LogCollectorConfig *LogCollectorConfig `protobuf:"bytes,1,opt,name=log_collector_config,json=logCollectorConfig,proto3" json:"log_collector_config,omitempty"`
	SamplingConfig     *SamplingConfig     `protobuf:"bytes,2,opt,name=sampling_config,json=samplingConfig,proto3" json:"sampling_config,omitempty"`
}

func (x *LoggingConfig) Reset() {
	*x = LoggingConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_serving_config_logging_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoggingConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoggingConfig) ProtoMessage() {}

func (x *LoggingConfig) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_serving_config_logging_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoggingConfig.ProtoReflect.Descriptor instead.
func (*LoggingConfig) Descriptor() ([]byte, []int) {
	return file_tensorflow_serving_config_logging_config_proto_rawDescGZIP(), []int{1}
}

func (x *LoggingConfig) GetLogCollectorConfig() *LogCollectorConfig {
	if x != nil {
		return x.LogCollectorConfig
	}
	return nil
}

func (x *LoggingConfig) GetSamplingConfig() *SamplingConfig {
	if x != nil {
		return x.SamplingConfig
	}
	return nil
}

var File_tensorflow_serving_config_logging_config_proto protoreflect.FileDescriptor

var file_tensorflow_serving_config_logging_config_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x6c, 0x6f, 0x67, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x12, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x1a, 0x34, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f,
	0x6c, 0x6f, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x35, 0x0a, 0x0e, 0x53, 0x61,
	0x6d, 0x70, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x61, 0x6d, 0x70, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x61, 0x74,
	0x65, 0x22, 0xb6, 0x01, 0x0a, 0x0d, 0x4c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x58, 0x0a, 0x14, 0x6c, 0x6f, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x12, 0x6c, 0x6f, 0x67, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4b, 0x0a,
	0x0f, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x61, 0x6d, 0x70,
	0x6c, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x73, 0x61, 0x6d, 0x70,
	0x6c, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x22, 0x5a, 0x1d, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x68, 0x6f, 0x77, 0x67, 0x65, 0x2f, 0x74,
	0x66, 0x73, 0x32, 0x38, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0xf8, 0x01, 0x01, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tensorflow_serving_config_logging_config_proto_rawDescOnce sync.Once
	file_tensorflow_serving_config_logging_config_proto_rawDescData = file_tensorflow_serving_config_logging_config_proto_rawDesc
)

func file_tensorflow_serving_config_logging_config_proto_rawDescGZIP() []byte {
	file_tensorflow_serving_config_logging_config_proto_rawDescOnce.Do(func() {
		file_tensorflow_serving_config_logging_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_tensorflow_serving_config_logging_config_proto_rawDescData)
	})
	return file_tensorflow_serving_config_logging_config_proto_rawDescData
}

var file_tensorflow_serving_config_logging_config_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_tensorflow_serving_config_logging_config_proto_goTypes = []interface{}{
	(*SamplingConfig)(nil),     // 0: tensorflow.serving.SamplingConfig
	(*LoggingConfig)(nil),      // 1: tensorflow.serving.LoggingConfig
	(*LogCollectorConfig)(nil), // 2: tensorflow.serving.LogCollectorConfig
}
var file_tensorflow_serving_config_logging_config_proto_depIdxs = []int32{
	2, // 0: tensorflow.serving.LoggingConfig.log_collector_config:type_name -> tensorflow.serving.LogCollectorConfig
	0, // 1: tensorflow.serving.LoggingConfig.sampling_config:type_name -> tensorflow.serving.SamplingConfig
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_tensorflow_serving_config_logging_config_proto_init() }
func file_tensorflow_serving_config_logging_config_proto_init() {
	if File_tensorflow_serving_config_logging_config_proto != nil {
		return
	}
	file_tensorflow_serving_config_log_collector_config_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tensorflow_serving_config_logging_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SamplingConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tensorflow_serving_config_logging_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoggingConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tensorflow_serving_config_logging_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_serving_config_logging_config_proto_goTypes,
		DependencyIndexes: file_tensorflow_serving_config_logging_config_proto_depIdxs,
		MessageInfos:      file_tensorflow_serving_config_logging_config_proto_msgTypes,
	}.Build()
	File_tensorflow_serving_config_logging_config_proto = out.File
	file_tensorflow_serving_config_logging_config_proto_rawDesc = nil
	file_tensorflow_serving_config_logging_config_proto_goTypes = nil
	file_tensorflow_serving_config_logging_config_proto_depIdxs = nil
}
