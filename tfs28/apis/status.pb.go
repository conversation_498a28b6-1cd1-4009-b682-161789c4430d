// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: tensorflow_serving/apis/status.proto

package apis

import (
	for_core_protos_go_proto "github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Status that corresponds to Status in
// third_party/tensorflow/core/lib/core/status.h.
type StatusProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Error code.
	ErrorCode for_core_protos_go_proto.Code `protobuf:"varint,1,opt,name=error_code,proto3,enum=tensorflow.error.Code" json:"error_code,omitempty"`
	// Error message. Will only be set if an error was encountered.
	ErrorMessage string `protobuf:"bytes,2,opt,name=error_message,proto3" json:"error_message,omitempty"`
}

func (x *StatusProto) Reset() {
	*x = StatusProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tensorflow_serving_apis_status_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusProto) ProtoMessage() {}

func (x *StatusProto) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_serving_apis_status_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusProto.ProtoReflect.Descriptor instead.
func (*StatusProto) Descriptor() ([]byte, []int) {
	return file_tensorflow_serving_apis_status_proto_rawDescGZIP(), []int{0}
}

func (x *StatusProto) GetErrorCode() for_core_protos_go_proto.Code {
	if x != nil {
		return x.ErrorCode
	}
	return for_core_protos_go_proto.Code_OK
}

func (x *StatusProto) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

var File_tensorflow_serving_apis_status_proto protoreflect.FileDescriptor

var file_tensorflow_serving_apis_status_proto_rawDesc = []byte{
	0x0a, 0x24, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x1a, 0x2a, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6b, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x36, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a,
	0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x42, 0x20, 0x5a, 0x1b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x68, 0x6f, 0x77, 0x67, 0x65, 0x2f, 0x74, 0x66, 0x73, 0x32, 0x38, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0xf8, 0x01, 0x01, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tensorflow_serving_apis_status_proto_rawDescOnce sync.Once
	file_tensorflow_serving_apis_status_proto_rawDescData = file_tensorflow_serving_apis_status_proto_rawDesc
)

func file_tensorflow_serving_apis_status_proto_rawDescGZIP() []byte {
	file_tensorflow_serving_apis_status_proto_rawDescOnce.Do(func() {
		file_tensorflow_serving_apis_status_proto_rawDescData = protoimpl.X.CompressGZIP(file_tensorflow_serving_apis_status_proto_rawDescData)
	})
	return file_tensorflow_serving_apis_status_proto_rawDescData
}

var file_tensorflow_serving_apis_status_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_tensorflow_serving_apis_status_proto_goTypes = []interface{}{
	(*StatusProto)(nil),                // 0: tensorflow.serving.StatusProto
	(for_core_protos_go_proto.Code)(0), // 1: tensorflow.error.Code
}
var file_tensorflow_serving_apis_status_proto_depIdxs = []int32{
	1, // 0: tensorflow.serving.StatusProto.error_code:type_name -> tensorflow.error.Code
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_tensorflow_serving_apis_status_proto_init() }
func file_tensorflow_serving_apis_status_proto_init() {
	if File_tensorflow_serving_apis_status_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tensorflow_serving_apis_status_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tensorflow_serving_apis_status_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_serving_apis_status_proto_goTypes,
		DependencyIndexes: file_tensorflow_serving_apis_status_proto_depIdxs,
		MessageInfos:      file_tensorflow_serving_apis_status_proto_msgTypes,
	}.Build()
	File_tensorflow_serving_apis_status_proto = out.File
	file_tensorflow_serving_apis_status_proto_rawDesc = nil
	file_tensorflow_serving_apis_status_proto_goTypes = nil
	file_tensorflow_serving_apis_status_proto_depIdxs = nil
}
