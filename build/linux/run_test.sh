#!/bin/bash

# intput0特征预测测试启动脚本

set -e

echo "=== 准备运行环境 ==="

# 创建日志目录
mkdir -p logs
echo "创建日志目录: ./logs"

# 检查TensorFlow Serving连接
ENDPOINT=${1:-"localhost:8500"}
INPUT_VALUE=${2:-"13792273858822192"}
MODEL_NAME=${3:-"model"}

echo "测试参数:"
echo "  TFS端点: $ENDPOINT"
echo "  输入值: $INPUT_VALUE"
echo "  模型名: $MODEL_NAME"
echo ""

# 运行测试
echo "=== 开始测试 ==="
./simple-predict -input=$INPUT_VALUE -model=$MODEL_NAME -endpoint=$ENDPOINT

echo ""
echo "=== 测试完成 ==="
echo "如果看到连接错误，请检查:"
echo "1. TensorFlow Serving是否在 $ENDPOINT 运行"
echo "2. 模型 '$MODEL_NAME' 是否已加载"
echo "3. 网络连接是否正常"
