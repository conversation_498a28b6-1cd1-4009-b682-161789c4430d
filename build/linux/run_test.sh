#!/bin/bash

# intput0特征预测测试启动脚本

set -e

echo "=== 准备运行环境 ==="

# 自动修复配置文件中的日志目录路径
if grep -q "/home/<USER>" config/prod.yaml; then
    echo "修复配置文件中的日志目录路径..."
    sed -i 's|dir: /home/<USER>/logs|g' config/prod.yaml
    echo "已将日志目录改为: ./logs"
fi

# 创建日志目录
mkdir -p logs
echo "创建日志目录: ./logs"

# 确保二进制文件有执行权限
if [ -f "simple-predict" ]; then
    chmod +x simple-predict
    echo "设置 simple-predict 执行权限"
else
    echo "错误: simple-predict 文件不存在"
    exit 1
fi

# 确保脚本本身有执行权限
chmod +x run_test.sh

# 创建必要的配置文件
if [ ! -f "config/simple_schema.yaml" ]; then
    echo "创建 simple_schema.yaml 配置文件..."
    cat > config/simple_schema.yaml << 'EOF'
model:
  user:
    intput0:
      data_type: LONG
      padding: 0
      default: "0"
      feat_type: user
EOF
    echo "已创建 config/simple_schema.yaml"
fi

# 检查TensorFlow Serving连接
ENDPOINT=${1:-"localhost:8500"}
INPUT_VALUE=${2:-"13792273858822192"}
MODEL_NAME=${3:-"model"}

echo "测试参数:"
echo "  TFS端点: $ENDPOINT"
echo "  输入值: $INPUT_VALUE"
echo "  模型名: $MODEL_NAME"
echo ""

# 运行测试
echo "=== 开始测试 ==="
./simple-predict -input=$INPUT_VALUE -model=$MODEL_NAME -endpoint=$ENDPOINT

echo ""
echo "=== 测试完成 ==="
echo "如果看到连接错误，请检查:"
echo "1. TensorFlow Serving是否在 $ENDPOINT 运行"
echo "2. 模型 '$MODEL_NAME' 是否已加载"
echo "3. 网络连接是否正常"
