#nasi_ctr_v1:
nasi_cvr_v3:
  #特征库
  ctx:
    deviceid:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    devicetype:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    os:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    osv:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    pub:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    subcat:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    tagid:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    make:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    model:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    country:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    state:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    city:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    language:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    #price double
    #win_price double
    released_at:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    modelprice:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    chipset:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    camera_pixels:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    bundle:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    #sip STRING
    ip:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    ua:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    #uamd5 STRING
    hour:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    appcat:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    display:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    reward:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    month:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    #day STRING
    # app_id STRING

  #listStyle
  item:
    advertiser_id:
      data_type: LIST_STR
      padding: 0
      default: ""
      feat_type: fl
    cid:
      data_type: LIST_STR
      padding: 0
      default: ""
      feat_type: fl
    crid:
      data_type: LIST_STR
      padding: 0
      default: ""
      feat_type: fl
    adid:
      data_type: LIST_STR
      padding: 0
      default: ""
      feat_type: fl
    adtype:
      data_type: LIST_STR
      padding: 0
      default: ""
      feat_type: fl
    size:
      data_type: LIST_STR
      padding: 0
      default: ""
      feat_type: fl


#nasi_ctr_v1:
nasi_ctr_v2:
  #特征库
  ctx:
    deviceid:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    devicetype:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    os:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    osv:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    pub:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    subcat:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    tagid:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    make:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    model:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    country:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    state:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    city:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    language:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    #price double
    #win_price double
    released_at:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    modelprice:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    chipset:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    camera_pixels:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    bundle:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    #sip STRING
    ip:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    ua:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    #uamd5 STRING
    hour:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    appcat:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    display:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    reward:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    month:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    #day STRING
    # app_id STRING

  #listStyle
  item:
    advertiser_id:
      data_type: LIST_STR
      padding: 0
      default: ""
      feat_type: fl
    cid:
      data_type: LIST_STR
      padding: 0
      default: ""
      feat_type: fl
    crid:
      data_type: LIST_STR
      padding: 0
      default: ""
      feat_type: fl
    adid:
      data_type: LIST_STR
      padding: 0
      default: ""
      feat_type: fl
    adtype:
      data_type: LIST_STR
      padding: 0
      default: ""
      feat_type: fl
    size:
      data_type: LIST_STR
      padding: 0
      default: ""
      feat_type: fl


dnn_winr_v1:
  ctx:
    deviceid:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    devicetype:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    bundle:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    osv:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    tagid:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    make:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    model:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    country:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    state:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    language:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    month:
      data_type: STR
      padding: 0
      default: ""
      feat_type: ctx
    label:
      data_type: FLOAT
      padding: 0
      default: ""
      feat_type: ctx
    price:
      data_type: FLOAT
      padding: 0
      default: ""
      feat_type: ctx