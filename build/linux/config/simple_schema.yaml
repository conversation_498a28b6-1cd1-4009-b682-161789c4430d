# 简化的特征配置文件 - 只包含intput0特征
# 用于处理简单的数值输入预测

# 特征类型定义
types:
  INT64: {dtype: tf.int64, default: 0}
  FLOAT: {dtype: tf.float32, default: 0.0}
  STRING: {dtype: tf.string, default: "-1024"}

# 模型版本配置
model:
  # 默认模型版本
  dnn_simple_v1:
    features:
      # 唯一的输入特征
      intput0:
        slot_id: 1
        field: user
        type: INT64
        desc: "简单数值输入特征"

# 标签定义 - 模型输出
labels:
  output: {type: FLOAT, desc: "模型预测输出"}

# 额外配置
extras:
  model_name: "model"
  signature_name: "serving_default"
  input_name: "intput0"
