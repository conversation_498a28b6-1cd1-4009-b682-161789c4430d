#!/bin/bash

# intput0特征预测 - 一键安装和运行脚本

set -e

echo "=== intput0特征预测 - 自动安装和运行 ==="
echo "当前目录: $(pwd)"
echo "时间: $(date)"
echo ""

# 检查必要文件
echo "=== 检查必要文件 ==="
REQUIRED_FILES=("simple-predict" "config/prod.yaml")
for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file 存在"
    else
        echo "✗ $file 不存在"
        echo "请确保您在正确的目录下运行此脚本"
        exit 1
    fi
done
echo ""

# 自动修复配置文件
echo "=== 修复配置文件 ==="
if grep -q "/home/<USER>" config/prod.yaml; then
    echo "修复配置文件中的日志目录路径..."
    sed -i 's|dir: /home/<USER>/logs|g' config/prod.yaml
    echo "✓ 已将日志目录改为: ./logs"
else
    echo "✓ 配置文件日志目录已正确"
fi

# 创建必要目录
echo ""
echo "=== 创建必要目录 ==="
mkdir -p logs
echo "✓ 创建日志目录: ./logs"

# 设置执行权限
echo ""
echo "=== 设置执行权限 ==="
chmod +x simple-predict
echo "✓ 设置 simple-predict 执行权限"

chmod +x run_test.sh 2>/dev/null || echo "run_test.sh 不存在，跳过"
chmod +x setup_and_run.sh
echo "✓ 设置脚本执行权限"

# 创建schema配置文件
echo ""
echo "=== 检查配置文件 ==="
if [ ! -f "config/simple_schema.yaml" ]; then
    echo "创建 simple_schema.yaml 配置文件..."
    cat > config/simple_schema.yaml << 'EOF'
model:
  user:
    intput0:
      data_type: LONG
      padding: 0
      default: "0"
      feat_type: user
EOF
    echo "✓ 已创建 config/simple_schema.yaml"
else
    echo "✓ config/simple_schema.yaml 已存在"
fi

# 显示当前配置
echo ""
echo "=== 当前配置 ==="
echo "TFS端点: $(grep 'endpoint:' config/prod.yaml | tail -1 | awk '{print $2}' | tr -d '"')"
echo "日志目录: $(grep 'dir:' config/prod.yaml | awk '{print $2}')"
echo "日志级别: $(grep 'level:' config/prod.yaml | awk '{print $2}' | tr -d '"')"
echo ""

# 获取参数
ENDPOINT=${1:-"localhost:8500"}
INPUT_VALUE=${2:-"13792273858822192"}
MODEL_NAME=${3:-"model"}

echo "=== 测试参数 ==="
echo "TFS端点: $ENDPOINT"
echo "输入值: $INPUT_VALUE"
echo "模型名: $MODEL_NAME"
echo ""

# 运行测试
echo "=== 开始测试 ==="
echo "运行命令: ./simple-predict -input=$INPUT_VALUE -model=$MODEL_NAME -endpoint=$ENDPOINT"
echo ""

./simple-predict -input=$INPUT_VALUE -model=$MODEL_NAME -endpoint=$ENDPOINT

echo ""
echo "=== 测试完成 ==="
echo ""
echo "如果看到连接错误，请检查:"
echo "1. TensorFlow Serving是否在 $ENDPOINT 运行"
echo "2. 模型 '$MODEL_NAME' 是否已加载"
echo "3. 网络连接是否正常"
echo ""
echo "使用方法:"
echo "  $0                                    # 使用默认参数"
echo "  $0 localhost:8500                    # 指定TFS端点"
echo "  $0 localhost:8500 999999999999999    # 指定端点和输入值"
echo "  $0 localhost:8500 999999999999999 my_model  # 指定所有参数"
