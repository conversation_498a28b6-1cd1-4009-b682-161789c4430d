#!/bin/bash

# 批量特征预测测试脚本

set -e

echo "=== 批量特征预测测试 ==="

# 自动修复配置
if grep -q "/home/<USER>" config/prod.yaml; then
    sed -i 's|dir: /home/<USER>/logs|g' config/prod.yaml
fi

# 创建必要目录和文件
mkdir -p logs

if [ ! -f "config/simple_schema.yaml" ]; then
    cat > config/simple_schema.yaml << 'EOF'
model:
  user:
    intput0:
      data_type: LONG
      padding: 0
      default: "0"
      feat_type: user
EOF
fi

# 设置权限
chmod +x full-predict 2>/dev/null || echo "full-predict 不存在"
chmod +x simple-predict

# 获取参数
ENDPOINT=${1:-"localhost:8500"}
BATCH_SIZE=${2:-"2"}
MODEL_NAME=${3:-"model"}
MODE=${4:-"full"}

echo "参数:"
echo "  端点: $ENDPOINT"
echo "  批量: $BATCH_SIZE"
echo "  模型: $MODEL_NAME"
echo "  模式: $MODE"
echo ""

# 运行测试
if [ "$MODE" = "full" ] && [ -f "full-predict" ]; then
    echo "运行完整特征测试..."
    ./full-predict -endpoint=$ENDPOINT -batch=$BATCH_SIZE -model=$MODEL_NAME -mode=full
elif [ "$MODE" = "simple" ]; then
    echo "运行简单特征测试..."
    ./simple-predict -input=13792273858822192 -model=$MODEL_NAME -endpoint=$ENDPOINT
else
    echo "运行简单特征测试 (full-predict不存在)..."
    ./simple-predict -input=13792273858822192 -model=$MODEL_NAME -endpoint=$ENDPOINT
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "使用方法:"
echo "  $0                                    # 默认参数"
echo "  $0 localhost:8500 2 model full       # 完整特征测试"
echo "  $0 localhost:8500 1 model simple     # 简单特征测试"
