# Go项目Makefile

# 项目信息
PROJECT_NAME := go-bid-gateway-tom
BINARY_NAME := bid-gateway
SIMPLE_PREDICT_BINARY := simple-predict

# Go相关配置
GO := go
GOMOD := $(GO) mod
GOBUILD := $(GO) build
GOCLEAN := $(GO) clean
GOTEST := $(GO) test
GOGET := $(GO) get

# 构建目录
BUILD_DIR := build
LINUX_BUILD_DIR := $(BUILD_DIR)/linux

# 版本信息
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "unknown")
BUILD_TIME := $(shell date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT := $(shell git rev-parse HEAD 2>/dev/null || echo "unknown")

# 编译标志
LDFLAGS := -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"

# 默认目标
.PHONY: all
all: clean build

# 清理
.PHONY: clean
clean:
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)

# 创建构建目录
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

$(LINUX_BUILD_DIR):
	mkdir -p $(LINUX_BUILD_DIR)

# 下载依赖
.PHONY: deps
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# vendor编译（使用vendor目录）
.PHONY: vendor
vendor:
	$(GOMOD) vendor

# 本地构建
.PHONY: build
build: $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) ./cmd/server
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(SIMPLE_PREDICT_BINARY) ./cmd/simple_predict

# Linux构建（交叉编译）
.PHONY: build-linux
build-linux: $(LINUX_BUILD_DIR)
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(LINUX_BUILD_DIR)/$(BINARY_NAME) ./cmd/server
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(LINUX_BUILD_DIR)/$(SIMPLE_PREDICT_BINARY) ./cmd/simple_predict

# vendor模式Linux构建
.PHONY: build-linux-vendor
build-linux-vendor: $(LINUX_BUILD_DIR) vendor
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -mod=vendor -o $(LINUX_BUILD_DIR)/$(BINARY_NAME) ./cmd/server
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -mod=vendor -o $(LINUX_BUILD_DIR)/$(SIMPLE_PREDICT_BINARY) ./cmd/simple_predict

# 运行测试
.PHONY: test
test:
	$(GOTEST) -v ./...

# 运行简单预测测试
.PHONY: test-simple
test-simple: build
	./$(BUILD_DIR)/$(SIMPLE_PREDICT_BINARY) -input=13792273858822192 -model=model -endpoint=localhost:8500

# 运行简单预测测试（Linux版本）
.PHONY: test-simple-linux
test-simple-linux: build-linux
	./$(LINUX_BUILD_DIR)/$(SIMPLE_PREDICT_BINARY) -input=13792273858822192 -model=model -endpoint=localhost:8500

# 格式化代码
.PHONY: fmt
fmt:
	$(GO) fmt ./...

# 代码检查
.PHONY: vet
vet:
	$(GO) vet ./...

# 显示帮助
.PHONY: help
help:
	@echo "可用的make目标:"
	@echo "  all              - 清理并构建项目"
	@echo "  build            - 构建本地版本"
	@echo "  build-linux      - 交叉编译Linux版本"
	@echo "  build-linux-vendor - 使用vendor模式编译Linux版本"
	@echo "  vendor           - 生成vendor目录"
	@echo "  test             - 运行测试"
	@echo "  test-simple      - 运行简单预测测试"
	@echo "  test-simple-linux - 运行Linux版本简单预测测试"
	@echo "  clean            - 清理构建文件"
	@echo "  deps             - 下载依赖"
	@echo "  fmt              - 格式化代码"
	@echo "  vet              - 代码检查"
	@echo "  help             - 显示此帮助信息"
	@echo ""
	@echo "示例用法:"
	@echo "  make build-linux-vendor  # 使用vendor编译Linux版本"
	@echo "  make test-simple         # 测试简单预测功能"
