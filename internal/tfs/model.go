package tfs

import (
	"bid-gateway/pkg/utils"
	"fmt"

	log "github.com/sirupsen/logrus"
	example "github.com/tensorflow/tensorflow/tensorflow/go/core/example/example_protos_go_proto"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

type FeatureMetaMap map[string]*FeatureMeta

// type featureCate interface {
// 	example.BytesList | example.Feature | example.FeatureList
// }

// type tfrecord[T featureCate] struct {
// 	cate *T
// }

// func (tr *tfrecord[T]) Default() *tfrecord[T] {
// 	return &tfrecord[T]{}
// }

// func (tr *tfrecord[T]) Reset() {
// 	tr.cate = nil
// }

var (
	fmp = utils.NewPool[*FeatureMeta]()
	// feature     = utils.NewPool[*tfrecord[example.Feature]]()
	// featureList = utils.NewPool[*tfrecord[example.FeatureList]]()
)

func (fm FeatureMetaMap) Build() ([]byte, error) {
	flm := make(map[string]*example.FeatureList)
	fmm := make(map[string]*example.Feature)
	for k, v := range fm {
		bytes, err := v.makeTfsRecord()
		if err != nil {
			return nil, err
		}
		if v.getFT() == "ctx" {
			var f example.Feature
			err := proto.Unmarshal(bytes, &f)
			if err != nil {
				return nil, err
			}
			fmm[k] = &f
		}

		if v.getFT() == "fl" {
			var f example.FeatureList
			err := proto.Unmarshal(bytes, &f)
			if err != nil {
				return nil, err
			}
			flm[k] = &f
		}
	}
	// label 处理
	fmm["label"] = &example.Feature{
		Kind: &example.Feature_FloatList{
			FloatList: &example.FloatList{
				Value: []float32{1.0},
			},
		},
	}
	fmm["label1"] = &example.Feature{
		Kind: &example.Feature_FloatList{
			FloatList: &example.FloatList{
				Value: []float32{1.0},
			},
		},
	}

	fmm["label2"] = &example.Feature{
		Kind: &example.Feature_FloatList{
			FloatList: &example.FloatList{
				Value: []float32{1.0},
			},
		},
	}

	es := example.SequenceExample{
		Context: &example.Features{
			Feature: fmm,
		},
		FeatureLists: &example.FeatureLists{
			FeatureList: flm,
		},
	}
	//
	exampleStr, _ := proto.Marshal(&es)
	jsonString := protojson.Format(es.Context)

	log.WithFields(log.Fields{
		"featsType": "ctx",
	}).Debugf("ctx tfrecords:%s", jsonString)

	return exampleStr, nil

}

// 用户输入 => 特征转换
// @param name 特征名
// @param value 特征值
// @param version 模型版本
// @param tpy  自定义特征集类别
func NewFeatureMeta(name string, value any, version string, tpy Type) (*FeatureMeta, error) {

	key := fmt.Sprintf("%s:%s:%s", version, tpy, name)
	t, err := TFS.features.Get(key)
	if err != nil {
		return nil, err
	}
	f := fmp.Get() //sync.pool
	defer fmp.Put(f)
	{
		f.Name = name
		f.Value = value
		f.Type = tpy
		f.meta = t
	}
	// f := &FeatureMeta{
	// 	Name:  name,
	// 	Value: value,
	// 	Type:  tpy,
	// 	meta:  t,
	// }
	m := *f
	return &m, nil
}
