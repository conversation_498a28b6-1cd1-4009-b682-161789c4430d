package tfs

import (
	"bid-gateway/proto/api/rank"
	"fmt"

	log "github.com/sirupsen/logrus"
)

// 生成上下文特征
func genFeatureMeta(req *rank.RankRequest) (FeatureMetaMap, error) {
	m := make(FeatureMetaMap)
	ui := req.GetUserInfo()
	ver := ui.GetVersion()
	items := req.GetMap().Recall

	itemKeyPrefix := fmt.Sprintf("%s:item", ver)
	userKeyPrefix := fmt.Sprintf("%s:user", ver)
	ctxKeyPrefix := fmt.Sprintf("%s:ctx", ver)

	ukeys, err := TFS.features.GetPrefix(userKeyPrefix)

	if err != nil {
		return nil, err
	}
	// 处理userSchema
	for _, v := range ukeys {
		f, err := NewFeatureMeta(v, ui.GetUserMap()[v], ver, USER)
		if err != nil {
			fmt.Println(err)
		}
		m[v] = f
	}

	ctxkeys, err := TFS.features.GetPrefix(ctxKeyPrefix)

	if err != nil {
		return nil, err
	}
	// 处理上下文schema
	for _, v := range ctxkeys {
		f, err := NewFeatureMeta(v, ui.GetContextMap()[v], ver, CTX)
		if err != nil {
			log.WithFields(log.Fields{
				"feat":  "ctx",
				"name":  v,
				"value": ui.GetContextMap()[v],
			}).Errorf("error:%s", err.Error())
		}
		m[v] = f
	}

	// items
	ikeys, err := TFS.features.GetPrefix(itemKeyPrefix)
	if err != nil {
		return nil, err
	}
	if len(items) > 0 {
		for _, v := range ikeys {
			f, err := NewFeatureMeta(v, items, ver, ITEM)
			if err != nil {
				log.WithFields(log.Fields{
					"feat": "item",
					"name": v,
				}).Errorf("error:%s", err.Error())
			}
			m[v] = f
		}

	}
	return m, nil
}
