package tfs

import (
	"bid-gateway/pkg/feats"
	"context"
	"fmt"
	"io/ioutil"
	"sync"
	"time"

	api "tfs28/apis"
	log "github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"gopkg.in/yaml.v2"
)

var (
	TFS     *gRpcTFServing
	fmmPool sync.Pool
	flmPool sync.Pool
)

type gRpcTFServing struct {
	gc       api.PredictionServiceClient
	features feats.Featurer
}

type Option func(*gRpcTFServing)

func WithFeaturer(f feats.Featurer) Option {
	return func(g *gRpcTFServing) {
		g.features = f
	}
}

func (gs *gRpcTFServing) initSchema(schemaPath string) error {
	dataBytes, err := ioutil.ReadFile(schemaPath)
	if err != nil {
		log.Fatalf("read features file error:%s", err)
	}
	// 载入特征库
	schema := make(map[string]map[string]map[string]map[string]string)

	err = yaml.Unmarshal(dataBytes, &schema)

	if err != nil {
		log.Fatalf("parse yaml error: %s", err)
		return err
	}

	for ver, feats := range schema {
		for clan, v := range feats {
			for kk, vv := range v {
				key := fmt.Sprintf("%s:%s:%s", ver, clan, kk)
				err := gs.features.Add(key, vv) //非安全操作
				if err != nil {
					return err
				}
			}
		}
	}

	return nil

}

func InitgRpcTfserving(schemaPath string, opts ...Option) error {

	//创建grpc 连接
	dopts := []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithBlock(),
	}

	ctx, _ := context.WithTimeout(context.Background(), 5*time.Second)
	conn, err := grpc.DialContext(
		ctx,
		viper.GetString("tfs.endpoint"),
		dopts...,
	)

	if err != nil {
		log.Fatalln("tfsering connection error:", viper.GetString("tfs.endpoint"))
	}

	c := api.NewPredictionServiceClient(conn)

	s := &gRpcTFServing{
		gc: c,
	}
	for _, opt := range opts {
		opt(s)
	}
	s.initSchema(schemaPath) //
	TFS = s

	return nil
}
