package tfs

import "errors"

type FeatureMeta struct {
	Name  string
	Value any
	meta  map[string]string
	Type  Type
	maker Maker
}

func (fm *FeatureMeta) Default() *FeatureMeta {
	return new(FeatureMeta)
}

func (fm *FeatureMeta) Reset() {
	fm.Name = ""
	fm.Value = nil
	fm.meta = nil
	fm.Type = ""
	fm.maker = nil
}

func (fm *FeatureMeta) setStrategy(maker Maker) {
	fm.maker = maker
}

func (fm *FeatureMeta) getDT() string {
	return fm.meta["data_type"]
}

func (fm *FeatureMeta) getFT() string {
	return fm.meta["feat_type"]
}

func (fm *FeatureMeta) makeTfsRecord() ([]byte, error) {
	if fm.getFT() == "ctx" {
		fm.setStrategy(&feature{})
	}
	if fm.getFT() == "fl" {
		fm.setStrategy(&featureList{})
	}
	return fm.maker.make(*fm)
}

type Maker interface {
	make(m FeatureMeta) ([]byte, error)
}

type feature struct {
	recorder tfsrecorder
}

func (f *feature) setRecorder(recorder tfsrecorder) {
	f.recorder = recorder
}

func (f *feature) make(m FeatureMeta) ([]byte, error) {
	switch m.getDT() {
	case "STR":
		f.setRecorder(&str{})
		return f.recorder.build(m)
	case "FLOAT":
		f.setRecorder(&float{})
		return f.recorder.build(m)
	case "LONG":
		f.setRecorder(&long{})
		return f.recorder.build(m)
	case "LIST_STR":
		f.setRecorder(&listStr{})
		return f.recorder.build(m)
	case "LIST_FLOAT":
		f.setRecorder(&listFloat{})
		return f.recorder.build(m)
	case "LIST_LONG":
		f.setRecorder(&listLong{})
		return f.recorder.build(m)
	default:
		return nil, errors.New("bad value")
	}
}

type featureList struct {
	recorder tfsrecorder
}

func (fl *featureList) setRecorder(recorder tfsrecorder) {
	fl.recorder = recorder
}

func (fl *featureList) make(m FeatureMeta) ([]byte, error) {
	switch m.getDT() {
	case "LIST_STR":
		fl.setRecorder(&d1arrayStr{})
		return fl.recorder.build(m)
	case "LIST_FLOAT":
		fl.setRecorder(&d1arrayFloat{})
		return fl.recorder.build(m)
	case "LIST_LONG":
		fl.setRecorder(&d1arrayLong{})
		return fl.recorder.build(m)
	case "LIST_LIST_STR":
		fl.setRecorder(&d2arrayStr{})
		return fl.recorder.build(m)
	case "LIST_LIST_FLOAT":
		fl.setRecorder(&d2arrayFloat{})
		return fl.recorder.build(m)
	case "LIST_LIST_LONG":
		fl.setRecorder(&d2arrayLong{})
		return fl.recorder.build(m)
	default:
		return nil, errors.New("bad value")
	}
}
