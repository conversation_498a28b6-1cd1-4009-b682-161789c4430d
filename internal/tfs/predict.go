package tfs

import (
	"context"
	"time"

	"github.com/spf13/viper"
	tensor "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto"
	shape "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto"
	types "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto"

	"bid-gateway/proto/api/rank"

	pb "tfs28/apis"
	log "github.com/sirupsen/logrus"
)

func DoPredict(in *rank.RankRequest, items []*rank.Item) (*pb.PredictResponse, error) {

	ver := in.GetUserInfo().GetVersion()

	fm, err := genFeatureMeta(in)

	if err != nil {
		log.Errorf("genarate feats meta data map error:%s", err.Error())
		return nil, err
	}

	bs, err := fm.Build()
	if err != nil {
		log.Errorf("genarate TF feats  error:%s", err.<PERSON><PERSON>r())
		return nil, err
	}

	inputProto := &tensor.TensorProto{
		StringVal: [][]byte{bs},
		Dtype:     types.DataType_DT_STRING,
		TensorShape: &shape.TensorShapeProto{
			Dim: []*shape.TensorShapeProto_Dim{
				{
					Size: int64(1),
				},
			},
		},
	}

	recordBytes := []byte(`SequenceExample`)
	recordTypeProto := &tensor.TensorProto{
		StringVal: [][]byte{recordBytes},
		Dtype:     types.DataType_DT_STRING,
	}

	request := &pb.PredictRequest{
		ModelSpec: &pb.ModelSpec{
			Name:          ver,
			SignatureName: "pred",
		},
		Inputs: map[string]*tensor.TensorProto{
			"record_type": recordTypeProto,
			"input":       inputProto,
		},
	}
	// 超时
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(viper.GetInt("app.rpc_timeout"))*time.Millisecond)
	defer cancel()

	return TFS.gc.Predict(ctx, request)
}
