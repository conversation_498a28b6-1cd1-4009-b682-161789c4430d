package tfs

import (
	"context"
	"time"

	"github.com/spf13/viper"
	tensor "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto"
	shape "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto"
	types "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto"

	"bid-gateway/proto/api/rank"

	pb "tfs28/apis"

	log "github.com/sirupsen/logrus"
)

func DoPredict(in *rank.RankRequest, items []*rank.Item) (*pb.PredictResponse, error) {

	ver := in.GetUserInfo().GetVersion()

	fm, err := genFeatureMeta(in)

	if err != nil {
		log.Errorf("genarate feats meta data map error:%s", err.Error())
		return nil, err
	}

	bs, err := fm.Build()
	if err != nil {
		log.Errorf("genarate TF feats  error:%s", err.<PERSON><PERSON>r())
		return nil, err
	}

	inputProto := &tensor.TensorProto{
		StringVal: [][]byte{bs},
		Dtype:     types.DataType_DT_STRING,
		TensorShape: &shape.TensorShapeProto{
			Dim: []*shape.TensorShapeProto_Dim{
				{
					Size: int64(1),
				},
			},
		},
	}

	recordBytes := []byte(`SequenceExample`)
	recordTypeProto := &tensor.TensorProto{
		StringVal: [][]byte{recordBytes},
		Dtype:     types.DataType_DT_STRING,
	}

	request := &pb.PredictRequest{
		ModelSpec: &pb.ModelSpec{
			Name:          ver,
			SignatureName: "pred",
		},
		Inputs: map[string]*tensor.TensorProto{
			"record_type": recordTypeProto,
			"input":       inputProto,
		},
	}
	// 超时
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(viper.GetInt("app.rpc_timeout"))*time.Millisecond)
	defer cancel()

	return TFS.gc.Predict(ctx, request)
}

// DoSimplePredict 处理简单的数值输入预测请求
// 参考您提供的JSON格式: {"inputs": {"intput0": [13792273858822192]}, "model_spec": {"name": "model", "signature_name": "serving_default"}}
func DoSimplePredict(inputValue int64, modelName string) (*pb.PredictResponse, error) {
	// 构造输入张量 - 使用int64类型
	inputProto := &tensor.TensorProto{
		Int64Val: []int64{inputValue},
		Dtype:    types.DataType_DT_INT64,
		TensorShape: &shape.TensorShapeProto{
			Dim: []*shape.TensorShapeProto_Dim{
				{
					Size: int64(1), // 单个值
				},
			},
		},
	}

	// 构造预测请求
	request := &pb.PredictRequest{
		ModelSpec: &pb.ModelSpec{
			Name:          modelName,
			SignatureName: "serving_default", // 使用默认签名
		},
		Inputs: map[string]*tensor.TensorProto{
			"intput0": inputProto, // 注意这里保持原始的拼写 "intput0"
		},
	}

	// 设置超时
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(viper.GetInt("app.rpc_timeout"))*time.Millisecond)
	defer cancel()

	log.WithFields(log.Fields{
		"model_name":  modelName,
		"input_value": inputValue,
		"input_name":  "intput0",
	}).Info("发送简单预测请求")

	return TFS.gc.Predict(ctx, request)
}
