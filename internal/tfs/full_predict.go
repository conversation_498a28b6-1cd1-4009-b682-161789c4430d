package tfs

import (
	"context"
	"fmt"
	"time"

	"github.com/spf13/viper"
	tensor "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto"
	shape "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto"
	types "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto"

	pb "tfs28/apis"
	log "github.com/sirupsen/logrus"
)

// 45个需要hash的特征名称（按slot_id顺序）
var HashFeatureNames = []string{
	"dsp_id", "exchange_id", "week_day", "hour", "inventory_type",
	"adp_id", "client_ip", "adp_dim", "bidfloor", "sponsor_id",
	"campaign_id", "strategy_id", "creative_id", "dsp_advertiser_id", "dsp_creative_id",
	"creative_type", "media_bid_type", "dm_platform", "template_id", "app_bundle",
	"ad_source", "province", "city", "standard_make", "dev_make",
	"dev_model", "exp_id", "dm_media_id", "absolute_pos", "dsp_ad_slot",
	"api_version", "tanx_task_id", "tanx_group_id", "tanx_ad_id", "client_ipv6",
	"country", "domob_bidfloor", "cat_id", "bid_id", "schain",
	"surge_score", "dsp_cost_mod", "budget_type_v1", "app_name", "app_package_name",
}

// BatchPredictData 批量预测数据结构
type BatchPredictData struct {
	BatchSize   int                      `json:"batch_size"`
	RawFeatures map[string][]int64       `json:"raw_features"`    // 每个特征是数组，支持批量
	DspBid      []int64                  `json:"dsp_bid"`         // dsp_bid特征
}

// DoFullPredict 处理完整的46个特征预测请求
// 支持批量预测，每个特征都是数组格式
func DoFullPredict(batchData *BatchPredictData, modelName string) (*pb.PredictResponse, error) {
	if batchData == nil || batchData.BatchSize == 0 {
		return nil, fmt.Errorf("empty batch data")
	}

	batchSize := int64(batchData.BatchSize)
	
	log.WithFields(log.Fields{
		"model_name":  modelName,
		"batch_size":  batchSize,
		"feature_count": len(batchData.RawFeatures) + 1, // +1 for dsp_bid
	}).Info("开始处理完整特征预测请求")

	// 构建严格符合 TensorFlow Serving 标准的 gRPC 请求
	request := &pb.PredictRequest{
		ModelSpec: &pb.ModelSpec{
			Name:          modelName,
			SignatureName: "serving_default",
		},
		Inputs: make(map[string]*tensor.TensorProto),
	}

	// 1. 处理45个hash特征
	for _, featureName := range HashFeatureNames {
		var batchValues []int64
		
		if values, exists := batchData.RawFeatures[featureName]; exists {
			// 确保数组长度匹配batch_size
			batchValues = make([]int64, batchSize)
			for i := int64(0); i < batchSize; i++ {
				if i < int64(len(values)) {
					batchValues[i] = values[i]
				} else {
					batchValues[i] = 0 // 默认值
				}
			}
		} else {
			// 特征不存在，使用默认值
			batchValues = make([]int64, batchSize)
			for i := range batchValues {
				batchValues[i] = 0
			}
		}

		// 创建张量 - shape=(batch_size,)
		tensor := &tensor.TensorProto{
			Dtype: types.DataType_DT_INT64,
			TensorShape: &shape.TensorShapeProto{
				Dim: []*shape.TensorShapeProto_Dim{
					{Size: batchSize},
				},
			},
			Int64Val: batchValues,
		}

		request.Inputs[featureName] = tensor
		
		log.WithFields(log.Fields{
			"feature_name": featureName,
			"batch_size":   batchSize,
			"sample_value": batchValues[0],
		}).Debug("添加特征到请求")
	}

	// 2. 处理dsp_bid特征
	dspBidValues := make([]int64, batchSize)
	if len(batchData.DspBid) > 0 {
		for i := int64(0); i < batchSize; i++ {
			if i < int64(len(batchData.DspBid)) {
				dspBidValues[i] = batchData.DspBid[i]
			} else {
				dspBidValues[i] = 0
			}
		}
	} else {
		for i := range dspBidValues {
			dspBidValues[i] = 0
		}
	}

	dspBidTensor := &tensor.TensorProto{
		Dtype: types.DataType_DT_INT64,
		TensorShape: &shape.TensorShapeProto{
			Dim: []*shape.TensorShapeProto_Dim{
				{Size: batchSize},
			},
		},
		Int64Val: dspBidValues,
	}
	request.Inputs["dsp_bid"] = dspBidTensor

	log.WithFields(log.Fields{
		"feature_name": "dsp_bid",
		"batch_size":   batchSize,
		"sample_value": dspBidValues[0],
	}).Debug("添加dsp_bid特征到请求")

	// 验证输入数量 - 应该是46个特征
	expectedFeatureCount := len(HashFeatureNames) + 1 // 45 + dsp_bid
	actualFeatureCount := len(request.Inputs)
	
	log.WithFields(log.Fields{
		"expected_features": expectedFeatureCount,
		"actual_features":   actualFeatureCount,
		"input_tensors":     len(request.Inputs),
	}).Info("特征数量验证")

	if actualFeatureCount != expectedFeatureCount {
		log.WithFields(log.Fields{
			"expected": expectedFeatureCount,
			"actual":   actualFeatureCount,
		}).Warn("特征数量不匹配")
	}

	// 设置超时
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(viper.GetInt("app.rpc_timeout"))*time.Millisecond)
	defer cancel()

	log.WithFields(log.Fields{
		"model_name":     modelName,
		"batch_size":     batchSize,
		"feature_count":  actualFeatureCount,
		"signature_name": "serving_default",
		"endpoint":       viper.GetString("tfs.endpoint"),
	}).Info("发送完整特征预测请求")

	return TFS.gc.Predict(ctx, request)
}

// CreateSampleBatchData 创建示例批量数据
func CreateSampleBatchData(batchSize int) *BatchPredictData {
	data := &BatchPredictData{
		BatchSize:   batchSize,
		RawFeatures: make(map[string][]int64),
		DspBid:      make([]int64, batchSize),
	}

	// 为每个特征创建示例数据
	for _, featureName := range HashFeatureNames {
		values := make([]int64, batchSize)
		for i := 0; i < batchSize; i++ {
			// 创建一些示例值
			values[i] = int64(i + 1) // 简单的递增值
		}
		data.RawFeatures[featureName] = values
	}

	// dsp_bid示例数据
	for i := 0; i < batchSize; i++ {
		data.DspBid[i] = int64(1000 + i*100) // 示例出价
	}

	return data
}

// convertToInt64 将任意类型转换为 int64
func convertToInt64(value interface{}) (int64, bool) {
	switch v := value.(type) {
	case int64:
		return v, true
	case int32:
		return int64(v), true
	case int:
		return int64(v), true
	case float64:
		return int64(v), true
	case float32:
		return int64(v), true
	default:
		return 0, false
	}
}
