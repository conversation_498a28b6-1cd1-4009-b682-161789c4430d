package tfs

import (
	"bid-gateway/pkg/utils"
	"bid-gateway/proto/api/rank"
	"fmt"
	"strconv"

	log "github.com/sirupsen/logrus"
	example "github.com/tensorflow/tensorflow/tensorflow/go/core/example/example_protos_go_proto"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

type FeatureMeta struct {
	Name  string
	Value any
	meta  map[string]string
	Type  Type
}

func (fm *FeatureMeta) Default() *FeatureMeta {
	return new(FeatureMeta)
}

func (fm *FeatureMeta) Reset() {
	fm.Name = ""
	fm.Value = nil
	fm.meta = nil
	fm.Type = ""
}

type FeatureMetaMap map[string]*FeatureMeta

// type featureCate interface {
// 	example.BytesList | example.Feature | example.FeatureList
// }

// type tfrecord[T featureCate] struct {
// 	cate *T
// }

// func (tr *tfrecord[T]) Default() *tfrecord[T] {
// 	return &tfrecord[T]{}
// }

// func (tr *tfrecord[T]) Reset() {
// 	tr.cate = nil
// }

var (
	fmp = utils.NewPool[*FeatureMeta]()
	// feature     = utils.NewPool[*tfrecord[example.Feature]]()
	// featureList = utils.NewPool[*tfrecord[example.FeatureList]]()
)

// func (fmm FeatureMetaMap) MarshalJSON() ([]byte, error) {
// 	tempMap := make(map[string]interface{})
// 	for key, val := range fmm {
// 		tempMap[key] = map[string]interface{}{
// 			"type": val.Type,
// 			"meta": val.meta,
// 		}
// 	}
// 	return json.Marshal(tempMap)
// }

// var bufferPool = sync.Pool{
// 	New: func() any {
// 		return &bytes.Buffer{}
// 	},
// }

func (fm FeatureMetaMap) Build() ([]byte, error) {
	flm := make(map[string]*example.FeatureList)
	fmm := make(map[string]*example.Feature)

	for k, v := range fm {
		ft := v.meta["feat_type"]
		dt := v.meta["data_type"]

		if ft == "ctx" {
			// var f = feature.Get()
			switch dt {
			case "STR":
				if ts, ok := v.Value.(string); ok {
					// buf := bufferPool.Get().(*bytes.Buffer)
					strBytes := []byte(utils.FillNa(ts))
					log.WithFields(log.Fields{
						"name":         k,
						"str_value":    ts,
						"data_type":    "STR",
						"parsed_value": strBytes,
					}).Debugf("CTX feat [%s] parsed ok", k)
					f := &example.Feature{
						Kind: &example.Feature_BytesList{
							BytesList: &example.BytesList{
								Value: [][]byte{strBytes},
							},
						},
					}
					fmm[k] = f
					// v.UpdateMeta("origin", ts)
					// v.UpdateMeta("tfrecord", string(strBytes))
				}

			case "FLOAT":
				f := &example.Feature{}
				if ts, ok := v.Value.(string); ok {
					floatValue := utils.ParseFloat(ts, 1.0)
					log.WithFields(log.Fields{
						"name":         k,
						"str_value":    ts,
						"data_type":    "FLOAT",
						"parsed_value": []float32{floatValue},
					}).Debugf("CTX feat [%s] parsed ok", k)
					bl := &example.FloatList{
						Value: []float32{floatValue},
					}
					f.Kind = &example.Feature_FloatList{
						FloatList: bl,
					}
					fmm[k] = f
				}
			case "LONG":
				if ts, ok := v.Value.(string); ok {
					longValue := utils.ParseLong(ts, 1)
					log.WithFields(log.Fields{
						"name":         k,
						"str_value":    ts,
						"data_type":    "LONG",
						"parsed_value": []int64{longValue},
					}).Debugf("CTX feat [%s] parsed ok", k)
					f := &example.Feature{}
					bl := &example.Int64List{
						Value: []int64{longValue},
					}
					f.Kind = &example.Feature_Int64List{
						Int64List: bl,
					}
					fmm[k] = f
				}
			case "LIST_STR":
				if ts, ok := v.Value.([]string); ok {
					var v [][]byte
					f := &example.Feature{}
					for _, s := range ts {
						v = append(v, []byte(s))
					}

					bl := &example.BytesList{
						Value: v,
					}

					f.Kind = &example.Feature_BytesList{
						BytesList: bl,
					}
					fmm[k] = f
				}
			case "LIST_FLOAT":
				if ts, ok := v.Value.([]float32); ok {
					f := &example.Feature{}
					bl := &example.FloatList{
						Value: ts,
					}
					f.Kind = &example.Feature_FloatList{
						FloatList: bl,
					}
					fmm[k] = f
				}

			case "LIST_LONG":
				if ts, ok := v.Value.([]int64); ok {
					f := &example.Feature{}
					bl := &example.Int64List{
						Value: ts,
					}
					f.Kind = &example.Feature_Int64List{
						Int64List: bl,
					}
					fmm[k] = f
				}
			default:
				fmt.Printf("%s Unknow data type", k)
			}

		}
		if ft == "fl" {
			switch dt {
			case "LIST_STR":
				val := []any{}
				fs := []*example.Feature{}
				for _, el := range v.Value.([]*rank.Item) {
					// padding, _ := strconv.Atoi(v.meta["padding"])
					// l := utils.GetTfList(el.Map[v.Name], ",", padding, "_")
					val = append(val, utils.FillNa(el.Map[v.Name]))
				}
				ts := utils.AnySliceTo[string](val)
				for _, s := range ts {
					f := &example.Feature{}
					strBytes := []byte(s)
					bl := &example.BytesList{
						Value: [][]byte{strBytes},
					}
					f.Kind = &example.Feature_BytesList{
						BytesList: bl,
					}
					fs = append(fs, f)
				}
				flm[v.Name] = &example.FeatureList{
					Feature: fs,
				}
			case "LIST_FLOAT":
				val := []any{}
				fs := []*example.Feature{}
				for _, el := range v.Value.([]*rank.Item) {
					val = append(val, utils.ParseFloat(el.Map[v.Name], -1.0))
				}
				ts := utils.AnySliceTo[float32](val)
				for _, s := range ts {
					f := &example.Feature{}
					bl := &example.FloatList{
						Value: []float32{s},
					}
					f.Kind = &example.Feature_FloatList{
						FloatList: bl,
					}
					fs = append(fs, f)
				}
				flm[v.Name] = &example.FeatureList{
					Feature: fs,
				}
			case "LIST_LONG":
				val := []any{}
				fs := []*example.Feature{}
				for _, el := range v.Value.([]*rank.Item) {
					val = append(val, utils.ParseLong(el.Map[v.Name], -1))
				}
				ts := utils.AnySliceTo[int64](val)
				for _, s := range ts {
					f := &example.Feature{}
					bl := &example.Int64List{
						Value: []int64{s},
					}
					f.Kind = &example.Feature_Int64List{
						Int64List: bl,
					}
					fs = append(fs, f)
				}
				flm[v.Name] = &example.FeatureList{
					Feature: fs,
				}
			case "LIST_LIST_STR":
				fs := []*example.Feature{}
				for _, el := range v.Value.([]*rank.Item) {
					bArr := [][]byte{}
					padding, _ := strconv.Atoi(v.meta["padding"])
					li := utils.GetTfList(el.Map[v.Name], ",", padding, "_") //填充值
					sl := utils.AnySliceTo[string](li)
					for _, v := range sl {
						bArr = append(bArr, []byte(v))
					}
					f := &example.Feature{}
					bl := &example.BytesList{
						Value: bArr,
					}
					f.Kind = &example.Feature_BytesList{
						BytesList: bl,
					}

					fs = append(fs, f)
				}

				flm[v.Name] = &example.FeatureList{
					Feature: fs,
				}

			case "LIST_LIST_FLOAT":
				fs := []*example.Feature{}
				for _, el := range v.Value.([]*rank.Item) {
					padding, _ := strconv.Atoi(v.meta["padding"])
					li := utils.GetTfList(el.Map[v.Name], ",", padding, "-1")
					sl := utils.AnySliceTo[float32](li)
					// for _, v := range sl {
					// 	fArr = append(fArr, v)
					// }
					f := &example.Feature{
						Kind: &example.Feature_FloatList{
							FloatList: &example.FloatList{
								Value: sl,
							},
						},
					}
					fs = append(fs, f)
				}
				flm[v.Name] = &example.FeatureList{
					Feature: fs,
				}
			case "LIST_LIST_LONG":
				fs := []*example.Feature{}
				for _, el := range v.Value.([]*rank.Item) {
					padding, _ := strconv.Atoi(v.meta["padding"])
					li := utils.GetTfList(el.Map[v.Name], ",", padding, "-1")
					sl := utils.AnySliceTo[int64](li)
					f := &example.Feature{
						Kind: &example.Feature_Int64List{
							Int64List: &example.Int64List{
								Value: sl,
							},
						},
					}
					fs = append(fs, f)
				}
				flm[v.Name] = &example.FeatureList{
					Feature: fs,
				}
			default:
				fmt.Printf("%s Unknow data type", k)
			}

		}
	}
	// label 处理
	fmm["label"] = &example.Feature{
		Kind: &example.Feature_FloatList{
			FloatList: &example.FloatList{
				Value: []float32{1.0},
			},
		},
	}
	fmm["label1"] = &example.Feature{
		Kind: &example.Feature_FloatList{
			FloatList: &example.FloatList{
				Value: []float32{1.0},
			},
		},
	}

	fmm["label2"] = &example.Feature{
		Kind: &example.Feature_FloatList{
			FloatList: &example.FloatList{
				Value: []float32{1.0},
			},
		},
	}

	es := example.SequenceExample{
		Context: &example.Features{
			Feature: fmm,
		},
		FeatureLists: &example.FeatureLists{
			FeatureList: flm,
		},
	}
	//
	exampleStr, _ := proto.Marshal(&es)
	jsonString := protojson.Format(es.Context)

	log.WithFields(log.Fields{
		"featsType": "ctx",
	}).Debugf("ctx tfrecords:%s", jsonString)

	return exampleStr, nil

}

// 用户输入 => 特征转换
// @param name 特征名
// @param value 特征值
// @param version 模型版本
// @param tpy  自定义特征集类别
func NewFeatureMeta(name string, value any, version string, tpy Type) (*FeatureMeta, error) {

	key := fmt.Sprintf("%s:%s:%s", version, tpy, name)
	t, err := TFS.features.Get(key)
	if err != nil {
		return nil, err
	}
	f := fmp.Get() //sync.pool
	defer fmp.Put(f)
	{
		f.Name = name
		f.Value = value
		f.Type = tpy
		f.meta = t
	}
	// f := &FeatureMeta{
	// 	Name:  name,
	// 	Value: value,
	// 	Type:  tpy,
	// 	meta:  t,
	// }
	m := *f
	return &m, nil
}
