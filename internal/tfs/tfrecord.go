package tfs

import (
	"bid-gateway/pkg/utils"
	"bid-gateway/proto/api/rank"
	"errors"
	"strconv"
	"sync"

	log "github.com/sirupsen/logrus"
	example "github.com/tensorflow/tensorflow/tensorflow/go/core/example/example_protos_go_proto"
	"google.golang.org/protobuf/proto"
)

const MAX_BYTES = 200

// 内存复用

// type originFeature[T any] struct {
// 	feat *example.Feature
// }

// func (o *originFeature[T]) Default() *originFeature[T] {
// 	return &originFeature[T]{
// 		feat: &example.Feature{
// 			Kind: T,
// 		},
// 	}
// }

type tfsrecorder interface {
	build(m FeatureMeta) ([]byte, error)
}

var byteListPool = sync.Pool{
	New: func() any {
		return &example.Feature{
			Kind: &example.Feature_BytesList{
				BytesList: &example.BytesList{
					Value: [][]byte{},
				},
			},
		}
	},
}

var floatListPool = sync.Pool{
	New: func() any {
		return &example.Feature{
			Kind: &example.Feature_FloatList{
				FloatList: &example.FloatList{
					Value: []float32{},
				},
			},
		}
	},
}

var longListPool = sync.Pool{
	New: func() any {
		return &example.Feature{
			Kind: &example.Feature_Int64List{
				Int64List: &example.Int64List{
					Value: []int64{},
				},
			},
		}
	},
}

// feat
type str struct{}

func (s *str) build(v FeatureMeta) ([]byte, error) {
	if ts, ok := v.Value.(string); ok {
		var mxByteArray [MAX_BYTES]byte
		var strBytes = mxByteArray[:0]
		// strBytes := []byte(utils.FillNa(ts))
		strBytes = append(strBytes, utils.FillNa(ts)...)

		log.WithFields(log.Fields{
			"name":         v.Name,
			"str_value":    ts,
			"data_type":    "STR",
			"parsed_value": strBytes,
		}).Debugf("CTX feat [%s] parsed ok", v.Name)

		feature := byteListPool.Get().(*example.Feature)

		defer func() {
			feature.GetBytesList().Value = nil
			byteListPool.Put(feature)
		}()
		// 序列化probuf msg
		feature.GetBytesList().Value = [][]byte{strBytes}
		// feature.GetBytesList().Reset()
		msgBytes, err := proto.Marshal(feature)
		if err != nil {
			return nil, err
		}
		return msgBytes, nil
	}
	return nil, errors.New("bad value")
}

type float struct{}

func (f *float) build(v FeatureMeta) ([]byte, error) {
	if ts, ok := v.Value.(string); ok {
		floatValue := utils.ParseFloat(ts, 1.0)
		log.WithFields(log.Fields{
			"name":         v.Name,
			"str_value":    ts,
			"data_type":    "FLOAT",
			"parsed_value": []float32{floatValue},
		}).Debugf("CTX feat [%s] parsed ok", v.Name)
		feature := floatListPool.Get().(*example.Feature)
		defer func() {
			feature.GetFloatList().Value = nil
			floatListPool.Put(feature)
		}()
		feature.GetFloatList().Value = []float32{floatValue}
		msgBytes, err := proto.Marshal(feature)
		if err != nil {
			return nil, err
		}
		return msgBytes, nil
	}
	return nil, errors.New("bad value")
}

type long struct{}

func (l *long) build(v FeatureMeta) ([]byte, error) {
	if ts, ok := v.Value.(string); ok {
		longValue := utils.ParseLong(ts, 1)
		log.WithFields(log.Fields{
			"name":         v.Name,
			"str_value":    ts,
			"data_type":    "LONG",
			"parsed_value": []int64{longValue},
		}).Debugf("CTX feat [%s] parsed ok", v.Name)

		feature := longListPool.Get().(*example.Feature)
		defer func() {
			feature.GetInt64List().Value = nil
			longListPool.Put(feature)
		}()
		feature.GetInt64List().Value = []int64{longValue}
		msgBytes, err := proto.Marshal(feature)
		if err != nil {
			return nil, err
		}
		return msgBytes, nil

	}
	return nil, errors.New("bad value")
}

type listStr struct{}

func (ls *listStr) build(v FeatureMeta) ([]byte, error) {
	if ts, ok := v.Value.([]string); ok {
		var b [MAX_BYTES][]byte
		var v = b[:0]
		for _, s := range ts {
			v = append(v, []byte(s))
		}

		feature := byteListPool.Get().(*example.Feature)
		defer func() {
			// feature.GetBytesList().Reset()
			feature.GetBytesList().Value = [][]byte{}

			byteListPool.Put(feature)

		}()

		feature.GetBytesList().Value = v
		msgBytes, err := proto.Marshal(feature)
		if err != nil {
			return nil, err
		}
		return msgBytes, nil
	}
	return nil, errors.New("bad value")
}

type listFloat struct{}

func (lf *listFloat) build(v FeatureMeta) ([]byte, error) {
	if ts, ok := v.Value.([]float32); ok {
		feature := floatListPool.Get().(*example.Feature)
		defer func() {
			feature.GetFloatList().Value = nil
			floatListPool.Put(feature)
		}()
		feature.GetFloatList().Value = ts
		msgBytes, err := proto.Marshal(feature)
		if err != nil {
			return nil, err
		}
		return msgBytes, nil
	}
	return nil, errors.New("bad value")
}

type listLong struct{}

func (ll *listLong) build(v FeatureMeta) ([]byte, error) {
	if ts, ok := v.Value.([]int64); ok {
		feature := longListPool.Get().(*example.Feature)
		defer func() {
			feature.GetInt64List().Value = nil
			longListPool.Put(feature)
		}()
		feature.GetInt64List().Value = ts
		msgBytes, err := proto.Marshal(feature)
		if err != nil {
			return nil, err
		}
		return msgBytes, nil
	}
	return nil, errors.New("bad value")
}

// featlist
type d1arrayStr struct{}

func (d1s *d1arrayStr) build(v FeatureMeta) ([]byte, error) {
	val := []any{}
	fs := []*example.Feature{}
	for _, el := range v.Value.([]*rank.Item) {
		// padding, _ := strconv.Atoi(v.meta["padding"])
		// l := utils.GetTfList(el.Map[v.Name], ",", padding, "_")
		val = append(val, utils.FillNa(el.Map[v.Name]))
	}
	ts := utils.AnySliceTo[string](val)
	for _, s := range ts {
		f := &example.Feature{}
		strBytes := []byte(s)
		bl := &example.BytesList{
			Value: [][]byte{strBytes},
		}
		f.Kind = &example.Feature_BytesList{
			BytesList: bl,
		}
		fs = append(fs, f)
	}
	// flm[v.Name] = &example.FeatureList{
	// 	Feature: fs,
	// }
	msgBytes, err := proto.Marshal(&example.FeatureList{
		Feature: fs,
	})
	if err != nil {
		return nil, err
	}
	return msgBytes, nil
	// return &example.FeatureList{
	// 	Feature: fs,
	// }, nil
}

type d1arrayFloat struct{}

func (d1f *d1arrayFloat) build(v FeatureMeta) ([]byte, error) {
	val := []any{}
	fs := []*example.Feature{}
	for _, el := range v.Value.([]*rank.Item) {
		val = append(val, utils.ParseFloat(el.Map[v.Name], -1.0))
	}
	ts := utils.AnySliceTo[float32](val)
	for _, s := range ts {
		f := &example.Feature{}
		bl := &example.FloatList{
			Value: []float32{s},
		}
		f.Kind = &example.Feature_FloatList{
			FloatList: bl,
		}
		fs = append(fs, f)
	}
	// return &example.FeatureList{
	// 	Feature: fs,
	// }, nil
	msgBytes, err := proto.Marshal(&example.FeatureList{
		Feature: fs,
	})
	if err != nil {
		return nil, err
	}
	return msgBytes, nil

}

type d1arrayLong struct{}

func (d1l *d1arrayLong) build(v FeatureMeta) ([]byte, error) {
	val := []any{}
	fs := []*example.Feature{}
	for _, el := range v.Value.([]*rank.Item) {
		val = append(val, utils.ParseLong(el.Map[v.Name], -1))
	}
	ts := utils.AnySliceTo[int64](val)
	for _, s := range ts {
		f := &example.Feature{}
		bl := &example.Int64List{
			Value: []int64{s},
		}
		f.Kind = &example.Feature_Int64List{
			Int64List: bl,
		}
		fs = append(fs, f)
	}
	// return &example.FeatureList{
	// 	Feature: fs,
	// }, nil
	msgBytes, err := proto.Marshal(&example.FeatureList{
		Feature: fs,
	})
	if err != nil {
		return nil, err
	}
	return msgBytes, nil
}

type d2arrayStr struct{}

func (d2s *d2arrayStr) build(v FeatureMeta) ([]byte, error) {
	fs := []*example.Feature{}
	for _, el := range v.Value.([]*rank.Item) {
		bArr := [][]byte{}
		padding, _ := strconv.Atoi(v.meta["padding"])
		li := utils.GetTfList(el.Map[v.Name], ",", padding, "_") //填充值
		sl := utils.AnySliceTo[string](li)
		for _, v := range sl {
			bArr = append(bArr, []byte(v))
		}
		f := &example.Feature{}
		bl := &example.BytesList{
			Value: bArr,
		}
		f.Kind = &example.Feature_BytesList{
			BytesList: bl,
		}

		fs = append(fs, f)
	}

	// return &example.FeatureList{
	// 	Feature: fs,
	// }, nil
	msgBytes, err := proto.Marshal(&example.FeatureList{
		Feature: fs,
	})
	if err != nil {
		return nil, err
	}
	return msgBytes, nil
}

type d2arrayFloat struct{}

func (d2f *d2arrayFloat) build(v FeatureMeta) ([]byte, error) {
	fs := []*example.Feature{}
	for _, el := range v.Value.([]*rank.Item) {
		padding, _ := strconv.Atoi(v.meta["padding"])
		li := utils.GetTfList(el.Map[v.Name], ",", padding, "-1")
		sl := utils.AnySliceTo[float32](li)
		// for _, v := range sl {
		// 	fArr = append(fArr, v)
		// }
		f := &example.Feature{
			Kind: &example.Feature_FloatList{
				FloatList: &example.FloatList{
					Value: sl,
				},
			},
		}
		fs = append(fs, f)
	}
	// return &example.FeatureList{
	// 	Feature: fs,
	// }, nil
	msgBytes, err := proto.Marshal(&example.FeatureList{
		Feature: fs,
	})
	if err != nil {
		return nil, err
	}
	return msgBytes, nil
}

type d2arrayLong struct{}

func (d2l *d2arrayLong) build(v FeatureMeta) ([]byte, error) {
	fs := []*example.Feature{}
	for _, el := range v.Value.([]*rank.Item) {
		padding, _ := strconv.Atoi(v.meta["padding"])
		li := utils.GetTfList(el.Map[v.Name], ",", padding, "-1")
		sl := utils.AnySliceTo[int64](li)
		f := &example.Feature{
			Kind: &example.Feature_Int64List{
				Int64List: &example.Int64List{
					Value: sl,
				},
			},
		}
		fs = append(fs, f)
	}
	// return &example.FeatureList{
	// 	Feature: fs,
	// }, nil
	msgBytes, err := proto.Marshal(&example.FeatureList{
		Feature: fs,
	})
	if err != nil {
		return nil, err
	}
	return msgBytes, nil
}
