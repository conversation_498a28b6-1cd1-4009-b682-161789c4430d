package tfs

import (
	"context"
	"fmt"
	"time"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	tensor "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_go_proto"
	shape "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/tensor_shape_go_proto"
	types "github.com/tensorflow/tensorflow/tensorflow/go/core/framework/types_go_proto"

	pb "tfs28/apis"
)

// 46个特征名称（45个hash特征 + dsp_bid）
var AllFeatureNames = []string{
	"dsp_id", "exchange_id", "week_day", "hour", "inventory_type",
	"adp_id", "client_ip", "adp_dim", "bidfloor", "sponsor_id",
	"campaign_id", "strategy_id", "creative_id", "dsp_advertiser_id", "dsp_creative_id",
	"creative_type", "media_bid_type", "dm_platform", "template_id", "app_bundle",
	"ad_source", "province", "city", "standard_make", "dev_make",
	"dev_model", "exp_id", "dm_media_id", "absolute_pos", "dsp_ad_slot",
	"api_version", "tanx_task_id", "tanx_group_id", "tanx_ad_id", "client_ipv6",
	"country", "domob_bidfloor", "cat_id", "bid_id", "schain",
	"surge_score", "dsp_cost_mod", "budget_type_v1", "app_name", "app_package_name",
	"dsp_bid", // 第46个特征
}

// BatchFeatures 批量特征数据
type BatchFeatures struct {
	BatchSize int                `json:"batch_size"`
	Features  map[string][]int64 `json:"features"` // 特征名 -> 批量值
}

// DoBatchPredict 执行批量预测 - 46个特征
func DoBatchPredict(batchFeatures *BatchFeatures, modelName string) (*pb.PredictResponse, error) {
	if batchFeatures == nil || batchFeatures.BatchSize == 0 {
		return nil, fmt.Errorf("empty batch features")
	}

	batchSize := int64(batchFeatures.BatchSize)

	log.WithFields(log.Fields{
		"model":         modelName,
		"batch_size":    batchSize,
		"feature_count": len(AllFeatureNames),
	}).Info("批量预测开始")

	// 构建gRPC请求
	request := &pb.PredictRequest{
		ModelSpec: &pb.ModelSpec{
			Name:          modelName,
			SignatureName: "serving_default",
		},
		Inputs: make(map[string]*tensor.TensorProto),
	}

	// 为每个特征创建张量 - shape=(batch_size,)
	for _, featureName := range AllFeatureNames {
		var batchValues []int64

		if values, exists := batchFeatures.Features[featureName]; exists {
			// 确保数组长度匹配batch_size
			batchValues = make([]int64, batchSize)
			for i := int64(0); i < batchSize; i++ {
				if i < int64(len(values)) {
					batchValues[i] = values[i]
				} else {
					batchValues[i] = 0 // 默认值
				}
			}
		} else {
			// 特征不存在，使用默认值
			batchValues = make([]int64, batchSize)
		}

		// 创建张量
		tensor := &tensor.TensorProto{
			Dtype: types.DataType_DT_INT64,
			TensorShape: &shape.TensorShapeProto{
				Dim: []*shape.TensorShapeProto_Dim{
					{Size: batchSize},
				},
			},
			Int64Val: batchValues,
		}

		request.Inputs[featureName] = tensor
	}

	// 验证特征数量
	if len(request.Inputs) != len(AllFeatureNames) {
		log.WithFields(log.Fields{
			"expected": len(AllFeatureNames),
			"actual":   len(request.Inputs),
		}).Warn("特征数量不匹配")
	}

	// 发送请求
	ctx, cancel := context.WithTimeout(context.Background(),
		time.Duration(viper.GetInt("app.rpc_timeout"))*time.Millisecond)
	defer cancel()

	response, err := TFS.gc.Predict(ctx, request)
	if err != nil {
		log.WithFields(log.Fields{
			"model": modelName,
			"error": err.Error(),
		}).Error("预测请求失败")
		return nil, err
	}

	log.WithFields(log.Fields{
		"model":        modelName,
		"output_count": len(response.Outputs),
	}).Info("批量预测完成")

	return response, nil
}

// CreateSampleBatch 创建示例批量数据
func CreateSampleBatch(batchSize int) *BatchFeatures {
	features := make(map[string][]int64)

	// 为每个特征创建示例数据
	for i, featureName := range AllFeatureNames {
		values := make([]int64, batchSize)
		for j := 0; j < batchSize; j++ {
			// 创建一些有意义的示例值
			if featureName == "dsp_bid" {
				values[j] = int64(1000 + j*100) // 出价示例
			} else {
				values[j] = int64(i*10 + j + 1) // 其他特征示例
			}
		}
		features[featureName] = values
	}

	return &BatchFeatures{
		BatchSize: batchSize,
		Features:  features,
	}
}

// ValidateBatchFeatures 验证批量特征数据
func ValidateBatchFeatures(batch *BatchFeatures) error {
	if batch == nil {
		return fmt.Errorf("batch is nil")
	}

	if batch.BatchSize <= 0 {
		return fmt.Errorf("invalid batch size: %d", batch.BatchSize)
	}

	// 检查特征数量
	if len(batch.Features) != len(AllFeatureNames) {
		return fmt.Errorf("feature count mismatch: expected %d, got %d",
			len(AllFeatureNames), len(batch.Features))
	}

	// 检查每个特征的数组长度
	for featureName, values := range batch.Features {
		if len(values) != batch.BatchSize {
			return fmt.Errorf("feature %s length mismatch: expected %d, got %d",
				featureName, batch.BatchSize, len(values))
		}
	}

	return nil
}
