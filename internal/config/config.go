package config

import (
	"fmt"

	"github.com/fsnotify/fsnotify"
	"github.com/pkg/errors"
	"github.com/spf13/viper"
)

func Init(confPath string) error {
	if err := initConfig(confPath); err != nil {
		panic(err)
	}
	watchConfig()
	return nil
}

func initConfig(path string) error {
	if path != "" {
		viper.SetConfigFile(path)
	} else {
		viper.AddConfigPath("config")
		viper.SetConfigName("prod")
	}
	viper.SetConfigType("yaml")
	viper.AutomaticEnv()
	if err := viper.ReadInConfig(); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func watchConfig() {
	viper.WatchConfig()
	viper.OnConfigChange(func(e fsnotify.Event) {
		// logger.Info("Config file changed: %s", e.Name)
		fmt.Printf("Config file changed: %s", e.Name)
	})
}
