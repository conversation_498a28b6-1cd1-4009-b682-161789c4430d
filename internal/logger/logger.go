package logger

import (
	"fmt"
	"io"
	"os"
	"path"
	"time"

	rotatelogs "github.com/lestrrat-go/file-rotatelogs"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

func Init() {
	logDir := viper.GetString("app.log.dir")
	logrus.Infof("app log:[%s]", logDir)
	dirInfo, err := os.Stat(logDir)
	if os.IsNotExist(err) {
		if err := os.MkdirAll(logDir, 0755); err != nil {
			panic(err)
		}
	} else {
		// 判断是否是一个目录
		if !dirInfo.IsDir() {
			fmt.Printf("%s 不是一个目录\n", logDir)
		}
	}

	logfile := path.Join(logDir, "app")
	fsWriter, err := rotatelogs.New(
		logfile+"_%Y-%m-%d.log",
		rotatelogs.WithMaxAge(time.Duration(168)*time.Hour),
		rotatelogs.WithRotationTime(time.Duration(24)*time.Hour),
	)
	if err != nil {
		panic(err)
	}

	multiWriter := io.MultiWriter(fsWriter, os.Stdout)
	logrus.SetReportCaller(viper.GetBool("app.log.show_caller"))
	logrus.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
	})
	logrus.SetOutput(multiWriter)
	level, err := logrus.ParseLevel(viper.GetString("app.log.level"))
	if err != nil {
		panic(err)
	}
	logrus.SetLevel(level)
}
