package main

import (
	"bid-gateway/internal/config"
	"bid-gateway/internal/logger"
	"bid-gateway/internal/tfs"
	"bid-gateway/pkg/feats"
	"bid-gateway/service"
	"context"
	"flag"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/spf13/viper"
)

func main() {
	var (
		configPath = flag.String("config", "config/prod.yaml", "配置文件路径")
		batchSize  = flag.String("batch", "2", "批量大小")
		modelName  = flag.String("model", "model", "模型名称")
		endpoint   = flag.String("endpoint", "localhost:8500", "TensorFlow Serving端点")
		testMode   = flag.String("mode", "full", "测试模式: full(完整46特征) | simple(单特征)")
	)
	flag.Parse()

	fmt.Printf("=== 完整特征预测测试 ===\n")
	fmt.Printf("配置文件: %s\n", *configPath)
	fmt.Printf("批量大小: %s\n", *batchSize)
	fmt.Printf("模型名: %s\n", *modelName)
	fmt.Printf("TFS端点: %s\n", *endpoint)
	fmt.Printf("测试模式: %s\n", *testMode)
	fmt.Printf("========================\n\n")

	// 初始化配置
	err := config.Init(*configPath)
	if err != nil {
		log.Fatalf("初始化配置失败: %v", err)
	}

	// 命令行参数优先，覆盖配置文件中的设置
	viper.Set("tfs.endpoint", *endpoint)

	// 初始化日志
	logger.Init()

	// 初始化特征器
	featurer := feats.InitMapCache()

	// 初始化TensorFlow Serving客户端
	schemaPath := "config/simple_schema.yaml"
	if viper.GetString("app.schema_path") != "" {
		schemaPath = viper.GetString("app.schema_path")
	}

	err = tfs.InitgRpcTfserving(schemaPath, tfs.WithFeaturer(featurer))
	if err != nil {
		log.Fatalf("初始化TFS客户端失败: %v", err)
	}

	// 转换批量大小
	batchSizeInt, err := strconv.Atoi(*batchSize)
	if err != nil {
		log.Fatalf("批量大小转换失败: %v", err)
	}

	if *testMode == "full" {
		// 完整特征测试
		testFullFeatures(batchSizeInt, *modelName)
	} else {
		// 简单特征测试
		testSimpleFeature(*modelName)
	}
}

func testFullFeatures(batchSize int, modelName string) {
	fmt.Printf("=== 完整46特征预测测试 ===\n")
	fmt.Printf("批量大小: %d\n", batchSize)
	fmt.Printf("特征数量: 46 (45个hash特征 + dsp_bid)\n\n")

	// 创建示例批量数据
	batchData := tfs.CreateSampleBatchData(batchSize)

	// 显示部分示例数据
	fmt.Printf("示例数据:\n")
	fmt.Printf("  dsp_id: %v\n", batchData.RawFeatures["dsp_id"])
	fmt.Printf("  exchange_id: %v\n", batchData.RawFeatures["exchange_id"])
	fmt.Printf("  dsp_bid: %v\n", batchData.DspBid)
	fmt.Printf("  ...(共46个特征)\n\n")

	// 调用完整预测
	start := time.Now()
	resp, err := tfs.DoFullPredict(batchData, modelName)
	elapsed := time.Since(start)

	if err != nil {
		fmt.Printf("❌ 完整特征预测失败: %v\n", err)
		analyzeFailureReasons(err)
		return
	}

	if resp == nil {
		fmt.Printf("❌ 预测响应为空\n")
		return
	}

	// 处理响应结果
	fmt.Printf("✅ 完整特征预测成功!\n")
	fmt.Printf("响应时间: %d毫秒\n", elapsed.Milliseconds())
	fmt.Printf("输出数量: %d\n", len(resp.Outputs))

	// 显示输出结果
	for outputName, outputTensor := range resp.Outputs {
		fmt.Printf("\n输出: %s\n", outputName)
		fmt.Printf("  类型: %s\n", outputTensor.Dtype.String())
		fmt.Printf("  形状: %v\n", outputTensor.TensorShape)

		// 显示前几个值
		switch outputTensor.Dtype.String() {
		case "DT_FLOAT":
			if floatVals := outputTensor.GetFloatVal(); floatVals != nil {
				fmt.Printf("  值: %v (显示前%d个)\n",
					floatVals[:min(len(floatVals), 5)],
					min(len(floatVals), 5))
			}
		case "DT_INT64":
			if int64Vals := outputTensor.GetInt64Val(); int64Vals != nil {
				fmt.Printf("  值: %v (显示前%d个)\n",
					int64Vals[:min(len(int64Vals), 5)],
					min(len(int64Vals), 5))
			}
		}
	}

	fmt.Printf("\n=== 完整特征测试完成 ===\n")
}

func testSimpleFeature(modelName string) {
	fmt.Printf("=== 简单特征对比测试 ===\n")

	// 创建服务实例
	rankService := service.NewRankService()

	// 调用简单预测
	inputValue := int64(13792273858822192)
	result, err := rankService.QuerySimpleRank(context.Background(), inputValue, modelName)

	if err != nil {
		fmt.Printf("❌ 简单预测失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 简单预测成功!\n")
	fmt.Printf("结果: %+v\n", result.Data[0])
	fmt.Printf("\n=== 简单特征测试完成 ===\n")
}

func analyzeFailureReasons(err error) {
	fmt.Printf("\n🔍 失败原因分析:\n")

	errStr := err.Error()

	if contains(errStr, "InvalidArgument") {
		fmt.Printf("1. ❌ 参数错误 - 可能的原因:\n")
		fmt.Printf("   - 特征数量不匹配 (期望46个，实际发送了多少?)\n")
		fmt.Printf("   - 数据类型不匹配 (模型期望float但发送了int64?)\n")
		fmt.Printf("   - 张量形状不正确 (batch_size不匹配?)\n")
		fmt.Printf("   - 特征名称不匹配 (模型期望的特征名与发送的不一致?)\n")
	}

	if contains(errStr, "NotFound") {
		fmt.Printf("2. ❌ 模型未找到 - 可能的原因:\n")
		fmt.Printf("   - 模型名称错误\n")
		fmt.Printf("   - 模型未加载到TensorFlow Serving\n")
		fmt.Printf("   - 签名名称错误 (serving_default)\n")
	}

	if contains(errStr, "connection") || contains(errStr, "dial") {
		fmt.Printf("3. ❌ 连接错误 - 可能的原因:\n")
		fmt.Printf("   - TensorFlow Serving未运行\n")
		fmt.Printf("   - 端口错误 (检查8500端口)\n")
		fmt.Printf("   - 网络连接问题\n")
	}

	fmt.Printf("\n💡 对比成功的简单版本:\n")
	fmt.Printf("   - 简单版本: 1个特征 intput0, shape=(1,), int64类型\n")
	fmt.Printf("   - 完整版本: 46个特征, shape=(batch_size,), int64类型\n")
	fmt.Printf("   - 关键差异: 特征数量和批量处理\n")
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[:len(substr)] == substr ||
		len(s) > len(substr) && s[len(s)-len(substr):] == substr ||
		(len(s) > len(substr) && findSubstring(s, substr))
}

func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
