package main

import (
	"bid-gateway/internal/config"
	L "bid-gateway/internal/logger"
	"bid-gateway/internal/tfs"
	"bid-gateway/internal/version"
	"bid-gateway/pkg/feats"
	"bid-gateway/proto/api/ping"
	"bid-gateway/proto/api/rank"
	"bid-gateway/service"
	"context"
	"fmt"
	"net"
	"net/http"
	"os"
	"strings"

	log "github.com/sirupsen/logrus"

	_ "net/http/pprof"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/spf13/viper"
	"github.com/urfave/cli"
	"golang.org/x/net/http2"
	"golang.org/x/net/http2/h2c"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

func main() {
	app := cli.NewApp()
	app.Name = "bid-gateway"
	app.Usage = "bid-gateway -c config/prod.yaml"
	printVersion := false
	app.Flags = []cli.Flag{
		cli.StringFlag{
			Name:  "conf, c",
			Value: "config/prod.yaml",
			Usage: "config/config.{local|dev|test|pre|prod}.yaml",
		},
		cli.StringFlag{
			Name:  "schema, s",
			Value: "config/schema.prod.yaml",
			Usage: "config/schema.{local|dev|test|pre|prod}.yaml",
		},
		cli.BoolFlag{
			Name:        "version, v",
			Required:    false,
			Usage:       "-v",
			Destination: &printVersion,
		},
	}

	app.Action = func(c *cli.Context) error {
		if printVersion {
			fmt.Printf("{%#v}", version.Get())
			return nil
		}

		var err error
		// 初始化配置文件
		conf := c.String("conf")
		config.Init(conf)

		// 初始化日志
		L.Init()

		//初始化特征库
		lib := feats.InitMapCache()

		// 初始化tfserving服务
		err = tfs.InitgRpcTfserving(c.String("schema"), tfs.WithFeaturer(lib))

		if err != nil {
			log.Fatalln("load schema error:", err)
		}
		// 启动pprof
		go func() {
			log.Errorln(http.ListenAndServe(":6060", nil))
		}()

		// 启动服务
		RunServer()

		return nil
	}
	app.Run(os.Args)
}

func RunServer() {
	listen, err := net.Listen("tcp", viper.GetString("app.endpoint"))
	if err != nil {
		log.Fatalln("failed to listen:", err)
	}
	rpc := grpc.NewServer()

	rs := service.NewRankService()

	ps := service.NewPingService()

	rank.RegisterRankerServer(rpc, rs)
	ping.RegisterCheckerServer(rpc, ps)

	// 创建grpc gateway
	gwmux := runtime.NewServeMux()
	dops := []grpc.DialOption{grpc.WithTransportCredentials(insecure.NewCredentials())}

	// ping
	err = ping.RegisterCheckerHandlerFromEndpoint(
		context.Background(),
		gwmux,
		viper.GetString("app.endpoint"),
		dops)

	if err != nil {
		log.Fatalln("Failed to register gwmux:", err)
	}

	//rank
	err = rank.RegisterRankerHandlerFromEndpoint(
		context.Background(),
		gwmux,
		viper.GetString("app.endpoint"),
		dops)

	if err != nil {
		log.Fatalln("Failed to register gwmux:", err)
	}

	mux := http.NewServeMux()
	mux.Handle("/", gwmux)

	gwServer := &http.Server{
		Addr:    viper.GetString("app.endpoint"),
		Handler: grpcHandlerFunc(rpc, mux),
	}
	log.Println("Serving httpserver  on:", gwServer.Addr)
	log.Fatalln(gwServer.Serve(listen))

}

func grpcHandlerFunc(grpcServer *grpc.Server, otherHandler http.Handler) http.Handler {
	return h2c.NewHandler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.ProtoMajor == 2 && strings.Contains(r.Header.Get("Content-Type"), "application/grpc") {
			grpcServer.ServeHTTP(w, r)
		} else {
			otherHandler.ServeHTTP(w, r)
		}
	}), &http2.Server{})
}
