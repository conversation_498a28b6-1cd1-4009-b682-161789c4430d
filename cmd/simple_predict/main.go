package main

import (
	"bid-gateway/internal/config"
	"bid-gateway/internal/logger"
	"bid-gateway/internal/tfs"
	"bid-gateway/pkg/feats"
	"bid-gateway/service"
	"context"
	"flag"
	"fmt"
	"log"
	"strconv"

	"github.com/spf13/viper"
)

func main() {
	var (
		configPath = flag.String("config", "config/prod.yaml", "配置文件路径")
		inputValue = flag.String("input", "13792273858822192", "输入值")
		modelName  = flag.String("model", "model", "模型名称")
	)
	flag.Parse()

	// 初始化配置
	err := config.InitConfig(*configPath)
	if err != nil {
		log.Fatalf("初始化配置失败: %v", err)
	}

	// 初始化日志
	logger.InitLogger()

	// 初始化特征器
	featurer := feats.NewFeaturer()

	// 初始化TensorFlow Serving客户端
	err = tfs.InitgRpcTfserving(
		viper.GetString("app.schema_path"),
		tfs.WithFeaturer(featurer),
	)
	if err != nil {
		log.Fatalf("初始化TFS客户端失败: %v", err)
	}

	// 转换输入值
	inputVal, err := strconv.ParseInt(*inputValue, 10, 64)
	if err != nil {
		log.Fatalf("输入值转换失败: %v", err)
	}

	// 创建服务实例
	rankService := service.NewRankService()

	// 调用简单预测
	fmt.Printf("发送预测请求:\n")
	fmt.Printf("  输入值: %d\n", inputVal)
	fmt.Printf("  模型名: %s\n", *modelName)
	fmt.Printf("  TFS端点: %s\n", viper.GetString("tfs.endpoint"))

	result, err := rankService.QuerySimpleRank(context.Background(), inputVal, *modelName)
	if err != nil {
		log.Fatalf("预测请求失败: %v", err)
	}

	// 打印结果
	fmt.Printf("\n预测结果:\n")
	for i, item := range result.Data {
		fmt.Printf("  项目 %d:\n", i+1)
		fmt.Printf("    Pctr: %f\n", item.Pctr)
		fmt.Printf("    Pcvr: %f\n", item.Pcvr)
		fmt.Printf("    Map: %v\n", item.Map)
	}
}
