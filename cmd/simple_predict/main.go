package main

import (
	"bid-gateway/internal/config"
	"bid-gateway/internal/logger"
	"bid-gateway/internal/tfs"
	"bid-gateway/pkg/feats"
	"bid-gateway/service"
	"context"
	"flag"
	"fmt"
	"log"
	"strconv"

	"github.com/spf13/viper"
)

func main() {
	var (
		configPath = flag.String("config", "config/prod.yaml", "配置文件路径")
		inputValue = flag.String("input", "13792273858822192", "intput0输入值")
		modelName  = flag.String("model", "model", "模型名称")
		endpoint   = flag.String("endpoint", "localhost:8500", "TensorFlow Serving端点")
	)
	flag.Parse()

	fmt.Printf("=== intput0特征预测测试 ===\n")
	fmt.Printf("配置文件: %s\n", *configPath)
	fmt.Printf("输入值: %s\n", *inputValue)
	fmt.Printf("模型名: %s\n", *modelName)
	fmt.Printf("TFS端点: %s\n", *endpoint)
	fmt.Printf("========================\n\n")

	// 初始化配置
	err := config.Init(*configPath)
	if err != nil {
		log.Fatalf("初始化配置失败: %v", err)
	}

	// 命令行参数优先，覆盖配置文件中的设置
	viper.Set("tfs.endpoint", *endpoint)

	// 初始化日志
	logger.Init()

	// 初始化特征器（简化版本，不需要复杂的特征配置）
	featurer := feats.InitMapCache()

	// 初始化TensorFlow Serving客户端
	// 使用简化的schema配置
	schemaPath := "config/simple_schema.yaml"
	if viper.GetString("app.schema_path") != "" {
		schemaPath = viper.GetString("app.schema_path")
	}

	err = tfs.InitgRpcTfserving(schemaPath, tfs.WithFeaturer(featurer))
	if err != nil {
		log.Fatalf("初始化TFS客户端失败: %v", err)
	}

	// 转换输入值
	inputVal, err := strconv.ParseInt(*inputValue, 10, 64)
	if err != nil {
		log.Fatalf("输入值转换失败: %v", err)
	}

	// 创建服务实例
	rankService := service.NewRankService()

	// 调用简单预测
	fmt.Printf("发送intput0预测请求:\n")
	fmt.Printf("  输入值: %d\n", inputVal)
	fmt.Printf("  模型名: %s\n", *modelName)
	fmt.Printf("  TFS端点: %s\n", viper.GetString("tfs.endpoint"))
	fmt.Printf("  对应JSON: {\"inputs\": {\"intput0\": [%d]}, \"model_spec\": {\"name\": \"%s\", \"signature_name\": \"serving_default\"}}\n\n", inputVal, *modelName)

	result, err := rankService.QuerySimpleRank(context.Background(), inputVal, *modelName)
	if err != nil {
		log.Fatalf("预测请求失败: %v", err)
	}

	// 打印结果
	fmt.Printf("预测结果:\n")
	fmt.Printf("  返回项目数: %d\n", len(result.Data))
	for i, item := range result.Data {
		fmt.Printf("  项目 %d:\n", i+1)
		fmt.Printf("    Pctr: %f\n", item.Pctr)
		fmt.Printf("    Pcvr: %f\n", item.Pcvr)
		fmt.Printf("    Ecpm: %f\n", item.Ecpm)
		fmt.Printf("    Map: %v\n", item.Map)
		fmt.Printf("    ---\n")
	}

	fmt.Printf("\n=== 测试完成 ===\n")
}
